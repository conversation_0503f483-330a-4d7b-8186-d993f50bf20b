<execution>
  <constraint>
    ## Java企业级开发客观约束
    - **JDK版本兼容性**：确保项目使用的JDK版本与生产环境一致
    - **Spring Boot版本**：严格遵循项目中已定义的Spring Boot 3.x版本
    - **数据库事务ACID**：确保数据操作的原子性、一致性、隔离性、持久性
    - **内存限制**：JVM堆内存配置，避免OOM异常
    - **网络延迟**：外部API调用超时配置合理
  </constraint>
  
  <rule>
    ## 强制性开发规则
    - **代码规范强制**：严格遵循阿里巴巴Java开发手册
    - **异常处理强制**：所有外部调用必须包含异常处理
    - **日志记录强制**：关键业务操作必须记录日志
    - **参数校验强制**：所有Controller入参必须进行校验
    - **SQL注入防护**：禁止字符串拼接SQL，必须使用参数化查询
    - **敏感信息保护**：密码、密钥等敏感信息严禁明文存储
  </rule>
  
  <guideline>
    ## 开发指导原则
    - **单一职责原则**：每个类、方法只负责一个明确的功能
    - **开放封闭原则**：对扩展开放，对修改封闭
    - **依赖注入优先**：使用Spring的依赖注入，避免硬编码依赖
    - **接口编程**：面向接口编程，提高代码灵活性
    - **配置外部化**：所有配置项都应该可以通过外部配置文件或环境变量设置
    - **优雅降级**：外部服务不可用时，提供备选方案
  </guideline>
  
  <process>
    ## Java后端开发标准流程
    
    ### 需求分析阶段
    ```mermaid
    flowchart TD
        A[接收需求] --> B[业务分析]
        B --> C[技术可行性评估]
        C --> D[工作量评估]
        D --> E[技术方案设计]
        E --> F[接口设计]
        F --> G[数据库设计]
    ```
    
    ### 开发实现阶段
    ```mermaid
    flowchart TD
        A[创建分支] --> B[编写Entity]
        B --> C[编写Mapper]
        C --> D[编写Service]
        D --> E[编写Controller]
        E --> F[编写单元测试]
        F --> G[自测验证]
        G --> H[代码审查]
        H --> I[合并主分支]
    ```
    
    ### 代码层级标准结构
    ```mermaid
    graph TD
        A[Controller层] --> B[Service层]
        B --> C[Mapper层]
        C --> D[Entity层]
        
        A --> A1[参数校验]
        A --> A2[异常处理]
        A --> A3[统一响应]
        
        B --> B1[业务逻辑]
        B --> B2[事务管理]
        B --> B3[缓存处理]
        
        C --> C1[数据访问]
        C --> C2[SQL优化]
        C --> C3[结果映射]
    ```
    
    ### Spring Boot项目配置流程
    ```mermaid
    flowchart LR
        A[application.yml] --> B[Environment Variables]
        B --> C[Profile配置]
        C --> D[配置类加载]
        D --> E[Bean初始化]
        E --> F[应用启动]
    ```
    
    ### 异常处理标准流程
    ```mermaid
    flowchart TD
        A[Controller接收请求] --> B{参数校验}
        B -->|失败| C[参数异常处理]
        B -->|成功| D[Service处理]
        D --> E{业务处理}
        E -->|异常| F[业务异常处理]
        E -->|成功| G[返回结果]
        C --> H[统一异常响应]
        F --> H
        G --> I[正常响应]
    ```
    
    ### 数据库操作最佳实践
    ```mermaid
    graph TD
        A[接收请求] --> B{事务需求判断}
        B -->|需要事务| C[@Transactional]
        B -->|不需要事务| D[直接调用Mapper]
        C --> E[开启事务]
        E --> F[执行数据库操作]
        F --> G{操作成功?}
        G -->|成功| H[提交事务]
        G -->|失败| I[回滚事务]
        D --> J[执行查询]
        H --> K[返回结果]
        I --> L[抛出异常]
        J --> K
    ```
  </process>
  
  <criteria>
    ## 代码质量评价标准
    
    ### 功能正确性
    - ✅ 业务逻辑实现正确
    - ✅ 边界条件处理完整
    - ✅ 异常场景覆盖充分
    - ✅ 单元测试覆盖率≥80%
    
    ### 性能要求
    - ✅ 接口响应时间≤2秒
    - ✅ 数据库查询优化，避免全表扫描
    - ✅ 合理使用缓存，提升响应速度
    - ✅ 内存使用合理，避免内存泄漏
    
    ### 安全标准
    - ✅ 输入参数严格校验
    - ✅ SQL注入防护到位
    - ✅ 敏感信息加密存储
    - ✅ 权限控制粒度合理
    
    ### 可维护性
    - ✅ 代码结构清晰，易于理解
    - ✅ 注释完整，关键逻辑有说明
    - ✅ 命名规范，见名知意
    - ✅ 模块划分合理，耦合度低
    
    ### 扩展性
    - ✅ 架构设计支持水平扩展
    - ✅ 接口设计向前兼容
    - ✅ 配置灵活，支持多环境部署
    - ✅ 组件化设计，便于功能扩展
  </criteria>
</execution>