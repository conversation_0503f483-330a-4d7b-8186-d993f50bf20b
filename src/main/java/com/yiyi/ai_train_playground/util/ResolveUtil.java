package com.yiyi.ai_train_playground.util;

import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.ArrayList;
import java.util.List;

/**
 * HTML解析工具类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
public class ResolveUtil {
    
    /**
     * 从HTML内容中提取所有img标签的src属性
     * 
     * @param htmlContent HTML内容
     * @return 图片URL列表，按在HTML中出现的顺序排列
     */
    public static List<String> extractImageSources(String htmlContent) {
        List<String> imageSources = new ArrayList<>();
        
        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            log.warn("HTML内容为空，返回空列表");
            return imageSources;
        }
        
        try {
            // 使用Jsoup解析HTML
            Document document = Jsoup.parse(htmlContent);
            
            // 选择所有img标签
            Elements imgElements = document.select("img");
            
            log.debug("找到 {} 个img标签", imgElements.size());
            
            // 按顺序提取src属性
            for (Element img : imgElements) {
                String src = img.attr("src");
                
                if (src != null && !src.trim().isEmpty()) {
                    // 处理相对URL，添加协议
                    String processedSrc = processImageUrl(src);
                    imageSources.add(processedSrc);
                    log.debug("提取到图片URL: {}", processedSrc);
                } else {
                    log.debug("跳过空的src属性的img标签");
                }
            }
            
            log.info("成功提取到 {} 个图片URL", imageSources.size());
            
        } catch (Exception e) {
            log.error("解析HTML时发生异常", e);
        }
        
        return imageSources;
    }
    
    /**
     * 处理图片URL，确保格式正确
     * 
     * @param src 原始src属性值
     * @return 处理后的URL
     */
    private static String processImageUrl(String src) {
        if (src == null || src.trim().isEmpty()) {
            return src;
        }
        
        String trimmedSrc = src.trim();
        
        // 如果是相对URL且以//开头，添加https协议
        if (trimmedSrc.startsWith("//")) {
            return "https:" + trimmedSrc;
        }
        
        // 如果是相对路径，这里可以根据需要添加基础URL
        // 目前直接返回原值
        return trimmedSrc;
    }
    
    /**
     * 从HTML内容中提取img标签的src属性，支持过滤条件
     * 
     * @param htmlContent HTML内容
     * @param filterEmpty 是否过滤空的src
     * @param filterDataUrl 是否过滤data:开头的URL
     * @return 图片URL列表
     */
    public static List<String> extractImageSources(String htmlContent, boolean filterEmpty, boolean filterDataUrl) {
        List<String> allSources = extractImageSources(htmlContent);
        
        if (!filterEmpty && !filterDataUrl) {
            return allSources;
        }
        
        List<String> filteredSources = new ArrayList<>();
        
        for (String src : allSources) {
            // 过滤空值
            if (filterEmpty && (src == null || src.trim().isEmpty())) {
                continue;
            }
            
            // 过滤data URL
            if (filterDataUrl && src != null && src.startsWith("data:")) {
                continue;
            }
            
            filteredSources.add(src);
        }
        
        log.info("过滤后剩余 {} 个图片URL", filteredSources.size());
        return filteredSources;
    }
    
    /**
     * 验证图片URL格式是否正确
     * 
     * @param url 图片URL
     * @return 是否为有效的图片URL
     */
    public static boolean isValidImageUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        String lowerUrl = url.toLowerCase();
        
        // 检查是否为常见的图片格式
        return lowerUrl.matches(".*\\.(jpg|jpeg|png|gif|bmp|webp|svg)(\\?.*)?$") ||
               lowerUrl.startsWith("http://") || 
               lowerUrl.startsWith("https://");
    }
    
    /**
     * 从HTML中提取指定CSS选择器的img标签src属性
     * 
     * @param htmlContent HTML内容
     * @param cssSelector CSS选择器，例如："div.detail-content img"
     * @return 图片URL列表
     */
    public static List<String> extractImageSourcesBySelector(String htmlContent, String cssSelector) {
        List<String> imageSources = new ArrayList<>();
        
        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            log.warn("HTML内容为空，返回空列表");
            return imageSources;
        }
        
        if (cssSelector == null || cssSelector.trim().isEmpty()) {
            log.warn("CSS选择器为空，使用默认的img选择器");
            return extractImageSources(htmlContent);
        }
        
        try {
            Document document = Jsoup.parse(htmlContent);
            Elements imgElements = document.select(cssSelector);
            
            log.debug("使用选择器 '{}' 找到 {} 个元素", cssSelector, imgElements.size());
            
            for (Element img : imgElements) {
                String src = img.attr("src");
                if (src != null && !src.trim().isEmpty()) {
                    String processedSrc = processImageUrl(src);
                    imageSources.add(processedSrc);
                }
            }
            
            log.info("通过选择器成功提取到 {} 个图片URL", imageSources.size());
            
        } catch (Exception e) {
            log.error("使用CSS选择器解析HTML时发生异常", e);
        }
        
        return imageSources;
    }
} 