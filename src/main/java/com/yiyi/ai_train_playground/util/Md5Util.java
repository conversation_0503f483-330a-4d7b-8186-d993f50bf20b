package com.yiyi.ai_train_playground.util;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * MD5工具类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Slf4j
public class Md5Util {
    
    /**
     * 生成字符串的MD5摘要
     * 
     * @param input 输入字符串
     * @return MD5摘要（32位小写十六进制字符串），如果输入为空或发生异常则返回null
     */
    public static String generateMd5(String input) {
        if (input == null || input.isEmpty()) {
            log.warn("输入字符串为空，无法生成MD5摘要");
            return null;
        }
        
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            
            // 将字节数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            String result = hexString.toString();
            log.debug("生成MD5摘要成功: 输入长度={}, MD5={}", input.length(), result);
            return result;
            
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5算法不可用", e);
            return null;
        } catch (Exception e) {
            log.error("生成MD5摘要时发生异常", e);
            return null;
        }
    }
    
    /**
     * 验证字符串与MD5摘要是否匹配
     * 
     * @param input 输入字符串
     * @param md5Hash MD5摘要
     * @return 是否匹配
     */
    public static boolean verifyMd5(String input, String md5Hash) {
        if (input == null || md5Hash == null) {
            return false;
        }
        
        String generatedMd5 = generateMd5(input);
        return md5Hash.equalsIgnoreCase(generatedMd5);
    }
    
    /**
     * 比较两个MD5摘要是否相等（忽略大小写）
     * 
     * @param md5Hash1 第一个MD5摘要
     * @param md5Hash2 第二个MD5摘要
     * @return 是否相等
     */
    public static boolean compareMd5(String md5Hash1, String md5Hash2) {
        if (md5Hash1 == null && md5Hash2 == null) {
            return true;
        }
        if (md5Hash1 == null || md5Hash2 == null) {
            return false;
        }
        return md5Hash1.equalsIgnoreCase(md5Hash2);
    }
}
