package com.yiyi.ai_train_playground.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Component
public class JwtUtil {

    @Value("${jwt.secret:default_secret_key_for_jwt_please_change_in_production}")
    private String secret;

    @Value("${jwt.expiration:3600000}") // 默认1小时
    private Long expiration;

    @Value("${jwt.remember-me-expiration}")
    private Long rememberMeExpiration;

    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    private Claims extractAllClaims(String token) {
        SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
        return Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    private Boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    /**
     * 生成JWT令牌
     * @param username 用户名
     * @return JWT令牌
     */
    public String generateToken(String username) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration);
        
        SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
        
        return Jwts.builder()
                .setSubject(username)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(key, SignatureAlgorithm.HS256)
                .compact();
    }

    public String generateRememberMeToken(String username) {
        Map<String, Object> claims = new HashMap<>();
        return createToken(claims, username, rememberMeExpiration);
    }

    private String createToken(Map<String, Object> claims, String subject, Long expiration) {
        SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + expiration))
                .signWith(key, SignatureAlgorithm.HS256)
                .compact();
    }

    /**
     * 验证JWT令牌
     * @param token JWT令牌
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
            Jws<Claims> claimsJws = Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token);
            
            // 如果token已过期，会抛出异常，所以这里返回true
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private Key getSignKey() {
        return Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
    }
    
    // 兼容旧代码的方法
    public String generateToken(Long userId, String username, Long teamId, boolean rememberMe) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("teamId", teamId);
        
        long expirationTime = rememberMe ? rememberMeExpiration : expiration;
        
        SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
        
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(username)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + expirationTime))
                .signWith(key, SignatureAlgorithm.HS256)
                .compact();
    }
    
    public String generateToken(UserDetails userDetails) {
        Map<String, Object> claims = new HashMap<>();
        return createToken(claims, userDetails.getUsername(), expiration);
    }
    
    /**
     * 从JWT令牌中获取用户名
     * @param token JWT令牌
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        SecretKey key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody();
        
        return claims.getSubject();
    }

    public Long getUserIdFromToken(String token) {
        Claims claims = getAllClaimsFromToken(token);
        return claims.get("userId", Long.class);
    }

    public Long getTeamIdFromToken(String token) {
        Claims claims = getAllClaimsFromToken(token);
        return claims.get("teamId", Long.class);
    }
    
    public Claims parseToken(String token) {
        return getAllClaimsFromToken(token);
    }
    
    private Claims getAllClaimsFromToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(getSignKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }
    
    /**
     * 调试方法：获取当前JWT配置信息
     * @return 配置信息Map
     */
    public Map<String, Object> getJwtConfigInfo() {
        Map<String, Object> configInfo = new HashMap<>();
        configInfo.put("expiration", expiration);
        configInfo.put("rememberMeExpiration", rememberMeExpiration);
        configInfo.put("expirationInMinutes", expiration / (1000 * 60));
        configInfo.put("expirationInHours", expiration / (1000 * 60 * 60));
        configInfo.put("expirationInDays", expiration / (1000 * 60 * 60 * 24));
        configInfo.put("rememberMeExpirationInMinutes", rememberMeExpiration / (1000 * 60));
        configInfo.put("rememberMeExpirationInHours", rememberMeExpiration / (1000 * 60 * 60));
        configInfo.put("rememberMeExpirationInDays", rememberMeExpiration / (1000 * 60 * 60 * 24));
        configInfo.put("secretLength", secret != null ? secret.length() : 0);
        return configInfo;
    }
    
    /**
     * 生成临时token（包含默认的userId和teamId）
     * @param username 用户名
     * @return JWT令牌
     */
    public String generateTempToken(String username) {
        // 为临时token提供默认值，避免解析时出错
        return generateToken(0L, username, 1L, false);
    }
} 