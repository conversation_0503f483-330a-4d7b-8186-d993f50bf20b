package com.yiyi.ai_train_playground.util;

import com.yiyi.ai_train_playground.service.BigmodelPromptsService;
import com.yiyi.ai_train_playground.service.ConverterServiceByLLM;
import com.yiyi.ai_train_playground.service.SuperBigModelInterface;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 依赖健康检查工具类
 * 在应用启动完成后检查关键依赖的状态
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-20
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DependencyHealthChecker {

    private final ApplicationContext applicationContext;

    /**
     * 应用启动完成后执行健康检查
     */
    @EventListener(ApplicationReadyEvent.class)
    public void checkDependencyHealth() {
        log.info("=== 开始执行依赖健康检查 ===");

        boolean allHealthy = true;

        // 1. 检查 ConverterServiceByLLM
        try {
            ConverterServiceByLLM converterService = applicationContext.getBean(ConverterServiceByLLM.class);
            if (converterService != null) {
                log.info("✅ ConverterServiceByLLM Bean 状态正常: {}", converterService.getClass().getName());
            } else {
                log.error("❌ ConverterServiceByLLM Bean 为 null");
                allHealthy = false;
            }
        } catch (Exception e) {
            log.error("❌ ConverterServiceByLLM Bean 获取失败", e);
            allHealthy = false;
        }

        // 2. 检查 SuperBigModelInterface
        try {
            SuperBigModelInterface bigModelService = applicationContext.getBean(SuperBigModelInterface.class);
            if (bigModelService != null) {
                log.info("✅ SuperBigModelInterface Bean 状态正常: {}", bigModelService.getClass().getName());
            } else {
                log.error("❌ SuperBigModelInterface Bean 为 null");
                allHealthy = false;
            }
        } catch (Exception e) {
            log.error("❌ SuperBigModelInterface Bean 获取失败", e);
            allHealthy = false;
        }

        // 3. 检查 BigmodelPromptsService
        try {
            BigmodelPromptsService promptsService = applicationContext.getBean(BigmodelPromptsService.class);
            if (promptsService != null) {
                log.info("✅ BigmodelPromptsService Bean 状态正常: {}", promptsService.getClass().getName());
            } else {
                log.error("❌ BigmodelPromptsService Bean 为 null");
                allHealthy = false;
            }
        } catch (Exception e) {
            log.error("❌ BigmodelPromptsService Bean 获取失败", e);
            allHealthy = false;
        }

        // 4. 检查环境变量
        String arkApiKey = System.getenv("ARK_API_KEY");
        if (arkApiKey == null || arkApiKey.trim().isEmpty()) {
            log.warn("⚠️ ARK_API_KEY 环境变量未设置或为空，这可能导致 DoubaoBigModelServiceImpl 创建失败");
            allHealthy = false;
        } else {
            log.info("✅ ARK_API_KEY 环境变量已设置 (长度: {})", arkApiKey.length());
        }

        // 5. 检查配置属性
        try {
            String arkApiKeyFromProperty = applicationContext.getEnvironment().getProperty("ARK_API_KEY");
            if (arkApiKeyFromProperty == null || arkApiKeyFromProperty.trim().isEmpty()) {
                log.warn("⚠️ ARK_API_KEY 配置属性未设置或为空");
            } else {
                log.info("✅ ARK_API_KEY 配置属性已设置 (长度: {})", arkApiKeyFromProperty.length());
            }
        } catch (Exception e) {
            log.error("❌ 获取 ARK_API_KEY 配置属性失败", e);
        }

        // 6. 总结
        if (allHealthy) {
            log.info("✅ 所有关键依赖状态正常");
        } else {
            log.error("❌ 发现依赖问题，JdImg2VecScheduler 可能会遇到 NullPointerException");
            log.error("建议检查：");
            log.error("1. 确保 ARK_API_KEY 环境变量正确设置");
            log.error("2. 检查数据库连接是否正常");
            log.error("3. 查看应用启动日志中的详细错误信息");
        }

        log.info("=== 依赖健康检查完成 ===");
    }

    /**
     * 手动执行健康检查
     * 可以在运行时调用此方法进行诊断
     */
    public void performManualHealthCheck() {
        log.info("=== 执行手动依赖健康检查 ===");
        checkDependencyHealth();
    }
}
