package com.yiyi.ai_train_playground.util;

import com.yiyi.ai_train_playground.security.JwtAuthenticationToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * Security工具类，用于获取当前登录用户信息
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
public class SecurityUtil {

    /**
     * 获取当前登录用户的teamId
     *
     * @return teamId，如果未登录或无法获取则返回null
     */
    public static Long getCurrentTeamId() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication instanceof JwtAuthenticationToken) {
                JwtAuthenticationToken jwtAuth = (JwtAuthenticationToken) authentication;
                Long teamId = jwtAuth.getTeamId();
                log.debug("从SecurityContext获取teamId: {}", teamId);
                return teamId;
            }
            
            log.warn("当前认证对象不是JwtAuthenticationToken类型: {}", 
                    authentication != null ? authentication.getClass().getSimpleName() : "null");
            return null;
            
        } catch (Exception e) {
            log.error("获取当前用户teamId失败", e);
            return null;
        }
    }

    /**
     * 获取当前登录用户的userId
     *
     * @return userId，如果未登录或无法获取则返回null
     */
    public static Long getCurrentUserId() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication instanceof JwtAuthenticationToken) {
                JwtAuthenticationToken jwtAuth = (JwtAuthenticationToken) authentication;
                Long userId = jwtAuth.getUserId();
                log.debug("从SecurityContext获取userId: {}", userId);
                return userId;
            }
            
            log.warn("当前认证对象不是JwtAuthenticationToken类型: {}", 
                    authentication != null ? authentication.getClass().getSimpleName() : "null");
            return null;
            
        } catch (Exception e) {
            log.error("获取当前用户userId失败", e);
            return null;
        }
    }

    /**
     * 获取当前登录用户的用户名
     *
     * @return 用户名，如果未登录或无法获取则返回null
     */
    public static String getCurrentUsername() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication != null && authentication.getPrincipal() instanceof UserDetails) {
                UserDetails userDetails = (UserDetails) authentication.getPrincipal();
                String username = userDetails.getUsername();
                log.debug("从SecurityContext获取username: {}", username);
                return username;
            }
            
            log.warn("无法获取当前用户名，认证对象: {}", 
                    authentication != null ? authentication.getClass().getSimpleName() : "null");
            return null;
            
        } catch (Exception e) {
            log.error("获取当前用户名失败", e);
            return null;
        }
    }

    /**
     * 检查当前用户是否已认证
     *
     * @return true如果已认证，false如果未认证
     */
    public static boolean isAuthenticated() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            return authentication != null && authentication.isAuthenticated() && 
                   !"anonymousUser".equals(authentication.getPrincipal());
        } catch (Exception e) {
            log.error("检查用户认证状态失败", e);
            return false;
        }
    }

    /**
     * 获取当前用户的完整认证信息
     *
     * @return JwtAuthenticationToken，如果未登录或类型不匹配则返回null
     */
    public static JwtAuthenticationToken getCurrentJwtAuthentication() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication instanceof JwtAuthenticationToken) {
                return (JwtAuthenticationToken) authentication;
            }
            
            return null;
            
        } catch (Exception e) {
            log.error("获取当前JWT认证信息失败", e);
            return null;
        }
    }
}
