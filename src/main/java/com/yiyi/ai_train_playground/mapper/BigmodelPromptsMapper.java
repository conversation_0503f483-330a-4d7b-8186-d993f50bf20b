package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.BigmodelPrompts;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BigmodelPromptsMapper {
    
    /**
     * 根据关键词查询系统提示词
     * @param keyword 关键词
     * @return 系统提示词列表
     */
    List<String> findSysPromptsByKeyword(@Param("keyword") String keyword);
    
    /**
     * 根据关键词查询用户提示词
     * @param keyword 关键词
     * @return 用户提示词列表
     */
    List<String> findUsrPromptsByKeyword(@Param("keyword") String keyword);
    
    /**
     * 根据关键词查询系统和用户提示词
     * @param keyword 关键词
     * @return 提示词列表
     */
    List<String> findBothPromptsByKeyword(@Param("keyword") String keyword);
    
    /**
     * 插入提示词记录
     * @param prompt 提示词对象
     * @return 影响的行数
     */
    int insert(BigmodelPrompts prompt);
} 