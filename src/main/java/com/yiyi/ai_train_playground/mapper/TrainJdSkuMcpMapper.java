package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.TrainJdSkuMcp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 京东SKU多类目属性表 Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Mapper
public interface TrainJdSkuMcpMapper {
    
    /**
     * 批量插入SKU多类目属性
     * 
     * @param mcpList 多类目属性列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<TrainJdSkuMcp> mcpList);
    
    /**
     * 根据skuId物理删除多类目属性
     * 
     * @param skuId SKU ID
     * @return 影响行数
     */
    int deleteBySkuId(@Param("skuId") Long skuId);
    
    /**
     * 根据skuId查询多类目属性列表
     * 
     * @param skuId SKU ID
     * @return 多类目属性列表
     */
    List<TrainJdSkuMcp> findBySkuId(@Param("skuId") Long skuId);
} 