package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.ScriptGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface ScriptGroupMapper {
    
    List<ScriptGroup> selectList(@Param("groupTitle") String groupTitle, @Param("teamId") Long teamId);
    
    int insert(ScriptGroup scriptGroup);
    
    int update(ScriptGroup scriptGroup);
    
    int deleteByIds(@Param("ids") String ids, @Param("teamId") Long teamId);

    ScriptGroup getDefaultGroup();

    List<ScriptGroup> getByTeamId(@Param("teamId") Long teamId);

    List<ScriptGroup> getOfficialGroups();

    List<ScriptGroup> findAllWithTree(@Param("groupTitle") String groupTitle);
    
    List<ScriptGroup> findOfficialGroups();
    
    List<ScriptGroup> findUserGroups();
    
    ScriptGroup findById(@Param("id") Long id);
} 