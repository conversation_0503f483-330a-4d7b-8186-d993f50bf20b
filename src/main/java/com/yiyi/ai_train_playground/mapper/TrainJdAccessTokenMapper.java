package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.TrainJdAccessToken;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface TrainJdAccessTokenMapper {
    
    /**
     * 插入京东访问令牌
     */
    @Insert("INSERT INTO train_jd_accesstoken (user_id, team_id, access_token, expires_time, refresh_token, scope, xid, shop_id, is_authorize, is_sync_complete, creator, updater, create_time, update_time, version) " +
            "VALUES (#{userId}, #{teamId}, #{accessToken}, #{expiresTime}, #{refreshToken}, #{scope}, #{xid}, #{shopId}, #{isAuthorize}, #{isSyncComplete}, #{creator}, #{updater}, NOW(), NOW(), 0)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(TrainJdAccessToken token);
    
    /**
     * 根据xid查询访问令牌
     */
    @Select("SELECT * FROM train_jd_accesstoken WHERE xid = #{xid}")
    TrainJdAccessToken findByXid(@Param("xid") String xid);
    
    /**
     * 根据用户ID和团队ID查询访问令牌
     */
    @Select("SELECT * FROM train_jd_accesstoken WHERE user_id = #{userId} AND team_id = #{teamId} ORDER BY create_time DESC LIMIT 1")
    TrainJdAccessToken findByUserIdAndTeamId(@Param("userId") Long userId, @Param("teamId") Long teamId);
    
    /**
     * 更新访问令牌
     */
    @Update("UPDATE train_jd_accesstoken SET access_token = #{accessToken}, expires_time = #{expiresTime}, " +
            "refresh_token = #{refreshToken}, scope = #{scope}, shop_id = #{shopId}, is_authorize = #{isAuthorize}, is_sync_complete = #{isSyncComplete}, " +
            "updater = #{updater}, update_time = NOW(), version = version + 1 " +
            "WHERE xid = #{xid}")
    int updateByXid(TrainJdAccessToken token);

    /**
     * 查询所有访问令牌记录
     */
    @Select("SELECT * FROM train_jd_accesstoken ORDER BY create_time DESC")
    List<TrainJdAccessToken> findAll();

    /**
     * 更新令牌信息（用于刷新token）
     */
    @Update("UPDATE train_jd_accesstoken SET access_token = #{accessToken}, expires_time = #{expiresTime}, " +
            "refresh_token = #{refreshToken}, scope = #{scope}, " +
            "updater = #{updater}, update_time = NOW(), version = version + 1 " +
            "WHERE id = #{id}")
    int updateTokenInfo(TrainJdAccessToken token);

    /**
     * 根据店铺ID查询访问令牌
     */
    @Select("SELECT * FROM train_jd_accesstoken WHERE shop_id = #{shopId} ORDER BY create_time DESC LIMIT 1")
    TrainJdAccessToken findByShopId(@Param("shopId") Long shopId);

    /**
     * 根据xid动态更新访问令牌（忽略空字段）
     * 使用动态SQL，只更新非空字段
     */
    int updateJATByXid(TrainJdAccessToken token);
}