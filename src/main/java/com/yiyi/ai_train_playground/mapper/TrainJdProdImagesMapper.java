package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.TrainJdProdImages;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 京东商品图片信息表Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Mapper
public interface TrainJdProdImagesMapper {
    
    /**
     * 插入商品图片信息
     *
     * @param image 商品图片信息
     * @return 影响行数
     */
    int insert(TrainJdProdImages image);
    
    /**
     * 批量插入商品图片信息
     *
     * @param imageList 商品图片信息列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<TrainJdProdImages> imageList);
    
    /**
     * 根据ID更新商品图片信息
     *
     * @param image 商品图片信息
     * @return 影响行数
     */
    int updateById(TrainJdProdImages image);
    
    /**
     * 根据ID删除商品图片信息
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据teamId和jdProdId查询商品图片列表
     *
     * @param teamId 团队ID
     * @param jdProdId 京东商品ID
     * @return 商品图片列表
     */
    List<TrainJdProdImages> findByTeamIdAndJdProdId(@Param("teamId") Long teamId, @Param("jdProdId") Long jdProdId);
    
    /**
     * 根据ID查询商品图片信息
     *
     * @param id 主键ID
     * @return 商品图片信息
     */
    TrainJdProdImages findById(@Param("id") Long id);
    
    /**
     * 根据teamId查询商品图片列表
     *
     * @param teamId 团队ID
     * @return 商品图片列表
     */
    List<TrainJdProdImages> findByTeamId(@Param("teamId") Long teamId);
    
    /**
     * 根据同步状态查询商品图片列表
     *
     * @param teamId 团队ID
     * @param syncStatus 同步状态
     * @return 商品图片列表
     */
    List<TrainJdProdImages> findByTeamIdAndSyncStatus(@Param("teamId") Long teamId, @Param("syncStatus") Integer syncStatus);
    
    /**
     * 根据teamId和jdProdId删除商品图片信息
     *
     * @param teamId 团队ID
     * @param jdProdId 京东商品ID
     * @return 影响行数
     */
    int deleteByTeamIdAndJdProdId(@Param("teamId") Long teamId, @Param("jdProdId") Long jdProdId);
    
    /**
     * 统计团队商品图片数量
     *
     * @param teamId 团队ID
     * @return 图片数量
     */
    Long countByTeamId(@Param("teamId") Long teamId);
    
    /**
     * 统计指定商品的图片数量
     *
     * @param teamId 团队ID
     * @param jdProdId 京东商品ID
     * @return 图片数量
     */
    Long countByTeamIdAndJdProdId(@Param("teamId") Long teamId, @Param("jdProdId") Long jdProdId);

    /**
     * 根据teamId、jdProdId和imgUrl查询商品图片信息
     *
     * @param teamId 团队ID
     * @param jdProdId 京东商品ID
     * @param imgUrl 图片URL
     * @return 商品图片信息
     */
    TrainJdProdImages findByTeamIdAndJdProdIdAndImgUrl(@Param("teamId") Long teamId,
                                                       @Param("jdProdId") Long jdProdId,
                                                       @Param("imgUrl") String imgUrl);

    /**
     * 根据team_id、sync_status分页查询商品图片列表
     *
     * @param teamId 团队ID
     * @param syncStatus 同步状态（可为null，表示查询所有状态）
     * @param jdProdId 京东商品ID（可为null，表示查询所有商品）
     * @param offset 偏移量
     * @param pageSize 每页大小
     * @return 商品图片列表
     */
    List<TrainJdProdImages> findByTeamIdAndSyncStatusWithPagination(@Param("teamId") Long teamId,
                                                                    @Param("syncStatus") Integer syncStatus,
                                                                    @Param("jdProdId") Long jdProdId,
                                                                    @Param("offset") Integer offset,
                                                                    @Param("pageSize") Integer pageSize);
}
