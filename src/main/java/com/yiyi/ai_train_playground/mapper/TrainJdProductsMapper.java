package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.TrainJdProducts;
import com.yiyi.ai_train_playground.vo.JdProductListVO;
import com.yiyi.ai_train_playground.vo.JdProductImageDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 京东商品表Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Mapper
public interface TrainJdProductsMapper {
    
    /**
     * 根据wareId查询商品信息
     *
     * @param wareId 商品ID
     * @param teamId 团队ID
     * @return 商品信息
     */
    TrainJdProducts findByWareIdAndTeamId(@Param("wareId") Long wareId, @Param("teamId") Long teamId);
    
    /**
     * 插入商品信息
     *
     * @param product 商品信息
     * @return 影响行数
     */
    int insert(TrainJdProducts product);
    
    /**
     * 更新商品信息
     *
     * @param product 商品信息
     * @return 影响行数
     */
    int updateByWareIdAndTeamId(TrainJdProducts product);
    
    /**
     * 批量插入商品信息
     *
     * @param productList 商品信息列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<TrainJdProducts> productList);

    /**
     * 根据团队ID查询商品列表
     *
     * @param teamId 团队ID
     * @return 商品列表
     */
    List<TrainJdProducts> findByTeamId(@Param("teamId") Long teamId);

    /**
     * 根据wareId和teamId删除商品信息
     *
     * @param wareId 商品ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteByWareIdAndTeamId(@Param("wareId") Long wareId, @Param("teamId") Long teamId);

    /**
     * 统计团队商品数量
     *
     * @param teamId 团队ID
     * @return 商品数量
     */
    long countByTeamId(@Param("teamId") Long teamId);

    /**
     * 根据wareId列表批量查询商品
     *
     * @param wareIds wareId列表
     * @param teamId 团队ID
     * @return 商品列表
     */
    List<TrainJdProducts> findByWareIds(@Param("wareIds") List<Long> wareIds, @Param("teamId") Long teamId);

    /**
     * 分页查询团队商品
     *
     * @param teamId 团队ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 商品列表
     */
    List<TrainJdProducts> findByTeamIdWithPage(@Param("teamId") Long teamId, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 根据商品标题模糊查询
     *
     * @param teamId 团队ID
     * @param title 商品标题关键词
     * @return 商品列表
     */
    List<TrainJdProducts> findByTeamIdAndTitleLike(@Param("teamId") Long teamId, @Param("title") String title);

    /**
     * 根据品牌查询商品
     *
     * @param teamId 团队ID
     * @param brandName 品牌名称
     * @return 商品列表
     */
    List<TrainJdProducts> findByTeamIdAndBrandName(@Param("teamId") Long teamId, @Param("brandName") String brandName);

    /**
     * 根据价格区间查询商品
     *
     * @param teamId 团队ID
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @return 商品列表
     */
    List<TrainJdProducts> findByTeamIdAndPriceRange(@Param("teamId") Long teamId, @Param("minPrice") Double minPrice, @Param("maxPrice") Double maxPrice);

    /**
     * 分页查询京东商品列表（关联SKU表）
     *
     * @param teamId 团队ID
     * @param title 商品标题（模糊查询，可为null）
     * @param offset 偏移量
     * @param pageSize 每页大小
     * @return 商品列表
     */
    List<JdProductListVO> findJdProductListWithPagination(@Param("teamId") Long teamId,
                                                          @Param("title") String title,
                                                          @Param("offset") Integer offset,
                                                          @Param("pageSize") Integer pageSize);

    /**
     * 统计京东商品总数（关联SKU表）
     *
     * @param teamId 团队ID
     * @param title 商品标题（模糊查询，可为null）
     * @return 总记录数
     */
    Long countJdProductList(@Param("teamId") Long teamId, @Param("title") String title);

    /**
     * 根据wareId查询商品详情
     *
     * @param teamId 团队ID
     * @param wareId 京东商品ID
     * @return 商品详情
     */
    TrainJdProducts findByTeamIdAndWareId(@Param("teamId") Long teamId, @Param("wareId") Long wareId);

    /**
     * 根据team_id、sync_status分页查询商品列表
     *
     * @param teamId 团队ID
     * @param syncStatus 同步状态
     * @param jdProdId 京东商品ID（可为null，表示查询所有商品）
     * @param offset 偏移量
     * @param pageSize 每页大小
     * @return 商品列表
     */
    List<TrainJdProducts> findByTeamIdAndSyncStatusWithPagination(@Param("teamId") Long teamId,
                                                                  @Param("syncStatus") Integer syncStatus,
                                                                  @Param("jdProdId") Long jdProdId,
                                                                  @Param("offset") Integer offset,
                                                                  @Param("pageSize") Integer pageSize);

    /**
     * 根据ID动态更新商品信息
     *
     * @param product 商品信息（只更新非null字段）
     * @return 影响行数
     */
    int updateByIdSelective(TrainJdProducts product);

    /**
     * 根据teamId和wareId查询商品图片详情（join查询）
     * 通过 tjp.ware_id = tji.jd_prod_id 进行关联
     *
     * @param teamId 团队ID
     * @param wareId 商品wareId
     * @return 商品图片详情列表
     */
    List<JdProductImageDetailVO> findProductImageDetailsByTeamIdAndWareId(@Param("teamId") Long teamId,
                                                                          @Param("wareId") Long wareId);
}