package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.TrainScriptProducts;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 剧本商品关联Mapper接口
 *
 * <AUTHOR> Assistant
 * @since 2025-07-03
 */
@Mapper
public interface TrainScriptProductsMapper {

    /**
     * 插入剧本商品关联记录
     *
     * @param scriptProducts 剧本商品关联信息
     * @return 影响行数
     */
    int insert(TrainScriptProducts scriptProducts);

    /**
     * 批量插入剧本商品关联记录
     *
     * @param scriptProductsList 剧本商品关联信息列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") java.util.List<TrainScriptProducts> scriptProductsList);

    /**
     * 根据剧本ID删除剧本商品关联
     *
     * @param scriptId 剧本ID
     * @return 影响行数
     */
    int deleteByScriptId(Long scriptId);

    /**
     * 根据剧本ID查询剧本商品关联
     *
     * @param scriptId 剧本ID
     * @return 剧本商品关联列表
     */
    java.util.List<TrainScriptProducts> selectByScriptId(Long scriptId);

    /**
     * 根据剧本ID和团队ID查询关联的商品ID列表
     *
     * @param scriptId 剧本ID
     * @param teamId 团队ID
     * @return 商品ID列表
     */
    java.util.List<Long> selectTrainProductIdsByScriptIdAndTeamId(@Param("scriptId") Long scriptId, @Param("teamId") Long teamId);
}
