package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.TrainTeam;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface TrainTeamMapper {

    @Select("SELECT t.* FROM train_team t " +
            "INNER JOIN user u ON u.team_id = t.id " +
            "WHERE u.id = #{userId}")
    TrainTeam findByUserId(@Param("userId") Long userId);

    @Select("SELECT * FROM train_team WHERE id = #{teamId}")
    TrainTeam findById(@Param("teamId") Long teamId);

    @Select("SELECT * FROM train_team ORDER BY create_time DESC")
    List<TrainTeam> findAll();

    @Insert("INSERT INTO train_team (name, description, creator, updater, create_time, update_time, version) " +
            "VALUES (#{name}, #{description}, #{creator}, #{updater}, NOW(), NOW(), 0)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(TrainTeam team);
}