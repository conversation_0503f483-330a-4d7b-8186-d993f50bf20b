package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.dto.EvaluationPlanShortDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 评价方案Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-03
 */
@Mapper
public interface TrainEvaluationPlanMapper {
    
    /**
     * 查询评价方案简短列表
     * 
     * @param teamId 团队ID
     * @return 评价方案简短信息列表
     */
    List<EvaluationPlanShortDTO> selectShortList(@Param("teamId") Long teamId);
}
