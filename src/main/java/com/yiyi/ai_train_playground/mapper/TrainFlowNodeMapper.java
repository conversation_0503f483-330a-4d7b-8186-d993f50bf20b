package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.TrainFlowNode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 流程节点Mapper接口
 *
 * <AUTHOR> Assistant
 * @since 2025-07-03
 */
@Mapper
public interface TrainFlowNodeMapper {

    /**
     * 插入流程节点记录
     *
     * @param flowNode 流程节点信息
     * @return 影响行数
     */
    int insert(TrainFlowNode flowNode);

    /**
     * 批量插入流程节点记录
     *
     * @param flowNodeList 流程节点信息列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") java.util.List<TrainFlowNode> flowNodeList);

    /**
     * 根据剧本ID删除流程节点
     *
     * @param scriptId 剧本ID
     * @return 影响行数
     */
    int deleteByScriptId(Long scriptId);

    /**
     * 根据剧本ID和团队ID删除流程节点
     *
     * @param scriptId 剧本ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteByScriptIdAndTeamId(@Param("scriptId") Long scriptId, @Param("teamId") Long teamId);

    /**
     * 根据剧本ID查询流程节点
     *
     * @param scriptId 剧本ID
     * @return 流程节点列表
     */
    java.util.List<TrainFlowNode> selectByScriptId(Long scriptId);
}
