package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.EvaluationGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface EvaluationGroupMapper {
    
    List<EvaluationGroup> selectList(@Param("groupTitle") String groupTitle, @Param("teamId") Long teamId);
    
    int insert(EvaluationGroup evaluationGroup);
    
    int update(EvaluationGroup evaluationGroup);
    
    int deleteByIds(@Param("ids") String ids, @Param("teamId") Long teamId);

    EvaluationGroup getDefaultGroup();

    List<EvaluationGroup> getByTeamId(@Param("teamId") Long teamId);

    List<EvaluationGroup> getOfficialGroups();

    List<EvaluationGroup> findAllWithTree(@Param("groupTitle") String groupTitle);
    
    List<EvaluationGroup> findOfficialGroups();
    
    List<EvaluationGroup> findUserGroups();
    
    EvaluationGroup findById(@Param("id") Long id);
}
