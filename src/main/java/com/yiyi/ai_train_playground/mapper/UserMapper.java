package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.User;
import org.apache.ibatis.annotations.*;
import java.time.LocalDateTime;

@Mapper
public interface UserMapper {
    @Select("SELECT * FROM user WHERE username = #{identity} OR email = #{identity} OR mobile = #{identity}")
    User findByIdentity(@Param("identity") String identity);

    @Update("UPDATE user SET failed_attempts = #{attempts}, is_locked = #{locked}, lock_time = #{lockTime} WHERE id = #{userId}")
    void updateLoginStatus(@Param("userId") Long userId, 
                         @Param("attempts") int attempts, 
                         @Param("locked") boolean locked, 
                         @Param("lockTime") LocalDateTime lockTime);

    @Select("SELECT * FROM user WHERE username = #{username}")
    User findByUsername(@Param("username") String username);

    @Select("SELECT * FROM user WHERE email = #{email}")
    User findByEmail(@Param("email") String email);

    @Select("SELECT * FROM user WHERE mobile = #{mobile}")
    User findByMobile(@Param("mobile") String mobile);
    
    @Insert("INSERT INTO user (username, password_hash, mobile, display_name, team_id, failed_attempts, is_locked, creator, updater, create_time, update_time, version) " +
            "VALUES (#{username}, #{passwordHash}, #{mobile}, #{displayName}, #{teamId}, 0, 0, #{creator}, #{updater}, NOW(), NOW(), 0)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(User user);

    @Select("SELECT * FROM user WHERE id = #{userId}")
    User findByUserId(@Param("userId") Long userId);
}