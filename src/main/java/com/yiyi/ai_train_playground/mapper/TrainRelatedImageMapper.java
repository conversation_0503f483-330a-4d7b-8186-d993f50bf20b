package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.TrainRelatedImage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 关联图片Mapper接口
 *
 * <AUTHOR> Assistant
 * @since 2025-07-03
 */
@Mapper
public interface TrainRelatedImageMapper {

    /**
     * 插入关联图片记录
     *
     * @param relatedImage 关联图片信息
     * @return 影响行数
     */
    int insert(TrainRelatedImage relatedImage);

    /**
     * 批量插入关联图片记录
     *
     * @param relatedImageList 关联图片信息列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") java.util.List<TrainRelatedImage> relatedImageList);

    /**
     * 根据剧本ID删除关联图片
     *
     * @param scriptId 剧本ID
     * @return 影响行数
     */
    int deleteByScriptId(Long scriptId);

    /**
     * 根据剧本ID和团队ID删除关联图片
     *
     * @param scriptId 剧本ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteByScriptIdAndTeamId(@Param("scriptId") Long scriptId, @Param("teamId") Long teamId);

    /**
     * 根据剧本ID查询关联图片
     *
     * @param scriptId 剧本ID
     * @return 关联图片列表
     */
    java.util.List<TrainRelatedImage> selectByScriptId(Long scriptId);
}
