package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.TrainJdProductsFeatures;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 京东商品功能表Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Mapper
public interface TrainJdProductsFeaturesMapper {
    
    /**
     * 根据商品ID删除功能信息
     * 
     * @param tjpId 商品ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    @Delete("DELETE FROM train_jd_products_features WHERE tjp_id = #{tjpId} AND team_id = #{teamId}")
    int deleteByTjpIdAndTeamId(@Param("tjpId") Long tjpId, @Param("teamId") Long teamId);
    
    /**
     * 批量插入商品功能信息
     * 
     * @param featuresList 功能信息列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<TrainJdProductsFeatures> featuresList);
    
    /**
     * 根据商品ID查询功能信息
     * 
     * @param tjpId 商品ID
     * @param teamId 团队ID
     * @return 功能信息列表
     */
    @Select("SELECT * FROM train_jd_products_features WHERE tjp_id = #{tjpId} AND team_id = #{teamId}")
    List<TrainJdProductsFeatures> findByTjpIdAndTeamId(@Param("tjpId") Long tjpId, @Param("teamId") Long teamId);
} 