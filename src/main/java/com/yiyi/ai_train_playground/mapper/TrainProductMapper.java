package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.TrainProduct;
import com.yiyi.ai_train_playground.dto.ProductListDTO;
import com.yiyi.ai_train_playground.dto.ProductDetailDTO;
import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface TrainProductMapper {
    
    /**
     * 查询商品列表（不包含详情字段）
     */
    @Select("<script>" +
            "SELECT id, external_product_id as externalProductId, external_product_name as externalProductName, " +
            "external_product_link as externalProductLink, external_product_image as externalProductImage, " +
            "status, learn_status as learnStatus, category, tags, platform, shop_name as shopName, " +
            "create_time as createTime, update_time as updateTime, creator, updater " +
            "FROM train_product WHERE team_id = #{teamId} " +
            "<if test='externalProductName != null and externalProductName != \"\"'>" +
            "AND external_product_name LIKE CONCAT('%', #{externalProductName}, '%') " +
            "</if>" +
            "ORDER BY update_time DESC" +
            "</script>")
    List<ProductListDTO> findProductList(@Param("teamId") Long teamId, 
                                        @Param("externalProductName") String externalProductName);
    
    /**
     * 根据ID查询商品详情（包含详情字段）
     */
    @Select("SELECT id, external_product_id as externalProductId, external_product_name as externalProductName, " +
            "external_product_link as externalProductLink, external_product_image as externalProductImage, " +
            "external_product_detail as externalProductDetail, external_product_detail_cleaned as externalProductDetailCleaned, " +
            "status, learn_status as learnStatus, category, tags, platform, shop_name as shopName, " +
            "create_time as createTime, update_time as updateTime, creator, updater " +
            "FROM train_product WHERE id = #{id} AND team_id = #{teamId}")
    ProductDetailDTO findProductById(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 更新商品信息
     */
    @Update("UPDATE train_product SET " +
            "external_product_id = #{externalProductId}, " +
            "external_product_name = #{externalProductName}, " +
            "external_product_link = #{externalProductLink}, " +
            "external_product_image = #{externalProductImage}, " +
            "external_product_detail = #{externalProductDetail}, " +
            "external_product_detail_cleaned = #{externalProductDetailCleaned}, " +
            "status = #{status}, " +
            "learn_status = 1, " +
            "category = #{category}, " +
            "tags = #{tags}, " +
            "platform = #{platform}, " +
            "shop_id = #{shopId}, " +
            "shop_name = #{shopName}, " +
            "updater = #{updater}, " +
            "update_time = NOW() " +
            "WHERE id = #{id} AND team_id = #{teamId}")
    int updateProduct(@Param("id") Long id,
                     @Param("teamId") Long teamId,
                     @Param("externalProductId") String externalProductId,
                     @Param("externalProductName") String externalProductName,
                     @Param("externalProductLink") String externalProductLink,
                     @Param("externalProductImage") String externalProductImage,
                     @Param("externalProductDetail") String externalProductDetail,
                     @Param("externalProductDetailCleaned") String externalProductDetailCleaned,
                     @Param("status") String status,
                     @Param("category") String category,
                     @Param("tags") String tags,
                     @Param("platform") String platform,
                     @Param("shopId") String shopId,
                     @Param("shopName") String shopName,
                     @Param("updater") String updater);
    
    /**
     * 删除商品
     */
    @Delete("DELETE FROM train_product WHERE id = #{id} AND team_id = #{teamId}")
    int deleteProduct(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 检查商品是否存在
     */
    @Select("SELECT COUNT(1) FROM train_product WHERE id = #{id} AND team_id = #{teamId}")
    int existsById(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 检查商品编号是否已存在
     */
    @Select("SELECT COUNT(1) FROM train_product WHERE external_product_id = #{externalProductId} AND team_id = #{teamId}")
    int existsByExternalProductId(@Param("externalProductId") String externalProductId, @Param("teamId") Long teamId);
    
    /**
     * 分页查询商品列表（不包含详情字段）
     */
    @Select("<script>" +
            "SELECT id, external_product_id as externalProductId, external_product_name as externalProductName, " +
            "external_product_link as externalProductLink, external_product_image as externalProductImage, " +
            "status, learn_status as learnStatus, category, tags, platform, shop_name as shopName, " +
            "create_time as createTime, update_time as updateTime, creator, updater " +
            "FROM train_product WHERE team_id = #{teamId} " +
            "<if test='externalProductName != null and externalProductName != \"\"'>" +
            "AND external_product_name LIKE CONCAT('%', #{externalProductName}, '%') " +
            "</if>" +
            "ORDER BY " +
            "<choose>" +
            "<when test='sort != null and sort != \"\"'>" +
            "${sort} ${order}" +
            "</when>" +
            "<otherwise>" +
            "update_time DESC" +
            "</otherwise>" +
            "</choose>" +
            " LIMIT #{offset}, #{size}" +
            "</script>")
    List<ProductListDTO> findProductListWithPage(@Param("teamId") Long teamId, 
                                               @Param("externalProductName") String externalProductName,
                                               @Param("offset") Integer offset,
                                               @Param("size") Integer size,
                                               @Param("sort") String sort,
                                               @Param("order") String order);
    
    /**
     * 统计商品总数
     */
    @Select("<script>" +
            "SELECT COUNT(1) FROM train_product WHERE team_id = #{teamId} " +
            "<if test='externalProductName != null and externalProductName != \"\"'>" +
            "AND external_product_name LIKE CONCAT('%', #{externalProductName}, '%') " +
            "</if>" +
            "</script>")
    Long countProducts(@Param("teamId") Long teamId, 
                      @Param("externalProductName") String externalProductName);
    
    /**
     * 插入商品
     */
    @Insert("INSERT INTO train_product (team_id, external_product_id, external_product_name, external_product_link, " +
            "external_product_image, status, learn_status, category, tags, platform, shop_id, shop_name, " +
            "creator, updater, create_time, update_time) VALUES " +
            "(#{teamId}, #{externalProductId}, #{externalProductName}, #{externalProductLink}, " +
            "#{externalProductImage}, #{status}, 0, #{category}, #{tags}, #{platform}, #{shopId}, #{shopName}, " +
            "#{creator}, #{updater}, NOW(), NOW())")
    int insertProduct(@Param("teamId") Long teamId,
                     @Param("externalProductId") String externalProductId,
                     @Param("externalProductName") String externalProductName,
                     @Param("externalProductLink") String externalProductLink,
                     @Param("externalProductImage") String externalProductImage,
                     @Param("status") String status,
                     @Param("category") String category,
                     @Param("tags") String tags,
                     @Param("platform") String platform,
                     @Param("shopId") String shopId,
                     @Param("shopName") String shopName,
                     @Param("creator") String creator,
                     @Param("updater") String updater);
    
    /**
     * 更新商品学习状态和清理后的详情
     */
    @Update("UPDATE train_product SET " +
            "learn_status = #{learnStatus}, " +
            "external_product_detail_cleaned = #{cleanedDetail}, " +
            "updater = #{updater}, " +
            "update_time = NOW() " +
            "WHERE external_product_id = #{externalProductId} AND team_id = #{teamId}")
    int updateLearningStatus(@Param("externalProductId") String externalProductId,
                            @Param("teamId") Long teamId,
                            @Param("learnStatus") Integer learnStatus,
                            @Param("cleanedDetail") String cleanedDetail,
                            @Param("updater") String updater);

    /**
     * 根据外部商品ID查询商品
     */
    @Select("SELECT * FROM train_product WHERE external_product_id = #{externalProductId} AND team_id = #{teamId}")
    TrainProduct findByExternalProductId(@Param("externalProductId") String externalProductId,
                                        @Param("teamId") Long teamId);

    /**
     * 批量删除商品
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids, @Param("teamId") Long teamId);
}