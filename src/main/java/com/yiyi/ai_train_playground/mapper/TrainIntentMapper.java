package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.TrainIntent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface TrainIntentMapper {
    
    @Select("SELECT * FROM train_intent WHERE parent_id = 0")
    List<TrainIntent> findParentIntents();
    
    @Select("SELECT * FROM train_intent WHERE parent_id = #{parentId}")
    List<TrainIntent> findChildIntents(@Param("parentId") Long parentId);
} 