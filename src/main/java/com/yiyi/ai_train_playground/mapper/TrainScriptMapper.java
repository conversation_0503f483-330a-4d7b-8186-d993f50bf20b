package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.dto.ScriptDetailDTO;
import com.yiyi.ai_train_playground.dto.ScriptListDTO;
import com.yiyi.ai_train_playground.dto.ScriptQueryRequest;
import com.yiyi.ai_train_playground.entity.TrainScript;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 剧本Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-02
 */
@Mapper
public interface TrainScriptMapper {
    
    /**
     * 分页查询剧本列表
     * 
     * @param request 查询请求参数
     * @param teamId 团队ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 剧本列表
     */
    List<ScriptListDTO> selectScriptList(@Param("request") ScriptQueryRequest request, 
                                        @Param("teamId") Long teamId,
                                        @Param("offset") Integer offset, 
                                        @Param("limit") Integer limit);
    
    /**
     * 查询剧本总数
     * 
     * @param request 查询请求参数
     * @param teamId 团队ID
     * @return 总数
     */
    Long countScripts(@Param("request") ScriptQueryRequest request, 
                     @Param("teamId") Long teamId);
    
    /**
     * 根据ID查询剧本
     * 
     * @param id 剧本ID
     * @param teamId 团队ID
     * @return 剧本信息
     */
    TrainScript selectById(@Param("id") Long id, @Param("teamId") Long teamId);
    
    /**
     * 插入剧本
     * 
     * @param script 剧本信息
     * @return 影响行数
     */
    int insert(TrainScript script);
    
    /**
     * 更新剧本
     * 
     * @param script 剧本信息
     * @return 影响行数
     */
    int update(TrainScript script);
    
    /**
     * 删除剧本
     *
     * @param id 剧本ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id, @Param("teamId") Long teamId);

    /**
     * 批量删除剧本
     *
     * @param ids 剧本ID列表
     * @param teamId 团队ID
     * @return 影响行数
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids, @Param("teamId") Long teamId);

    /**
     * 根据ID查询剧本详情
     *
     * @param id 剧本ID
     * @param teamId 团队ID
     * @return 剧本详情
     */
    ScriptDetailDTO getScriptDetailById(@Param("id") Long id, @Param("teamId") Long teamId);
}
