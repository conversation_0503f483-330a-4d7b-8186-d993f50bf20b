package com.yiyi.ai_train_playground.websocket;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.model.ScriptChatMessage;
import com.yiyi.ai_train_playground.service.ScriptChatService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
@ServerEndpoint(value = "/ws/script/chat/{token}")
public class ScriptChatEndpoint {

    private static ObjectMapper objectMapper;
    private static ScriptChatService scriptChatService;
    private static JwtUtil jwtUtil;

    @Autowired
    public void setObjectMapper(ObjectMapper objectMapper) {
        ScriptChatEndpoint.objectMapper = objectMapper;
    }

    @Autowired
    public void setScriptChatService(ScriptChatService scriptChatService) {
        ScriptChatEndpoint.scriptChatService = scriptChatService;
    }
    
    @Autowired
    public void setJwtUtil(JwtUtil jwtUtil) {
        ScriptChatEndpoint.jwtUtil = jwtUtil;
    }

    @OnOpen
    public void onOpen(Session session, @PathParam("token") String token) {
        log.info("新的WebSocket连接，token: {}", token);
        
        // 验证token
        try {
            if (!jwtUtil.validateToken(token)) {
                log.warn("无效的token: {}", token);
                session.close(new CloseReason(CloseReason.CloseCodes.VIOLATED_POLICY, "无效的token"));
                return;
            }
        } catch (Exception e) {
            log.error("验证token失败: {}", e.getMessage());
            try {
                session.close(new CloseReason(CloseReason.CloseCodes.VIOLATED_POLICY, "验证token失败"));
            } catch (IOException ioException) {
                log.error("关闭WebSocket连接失败", ioException);
            }
            return;
        }
        
        log.info("WebSocket连接已建立");
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("收到消息: {}", message);
        
        try {
            ScriptChatMessage chatMessage = objectMapper.readValue(message, ScriptChatMessage.class);
            
            if ("INIT".equals(chatMessage.getType())) {
                scriptChatService.handleInitMessage(chatMessage, session);
            } else if ("MESSAGE".equals(chatMessage.getType())) {
                scriptChatService.handleUserMessage(chatMessage, session);
            } else {
                log.warn("未知的消息类型: {}", chatMessage.getType());
            }
        } catch (JsonProcessingException e) {
            log.error("解析消息失败", e);
            try {
                session.getBasicRemote().sendText("消息格式错误");
            } catch (IOException ioException) {
                log.error("发送错误消息失败", ioException);
            }
        }
    }

    @OnClose
    public void onClose(Session session, CloseReason closeReason) {
        log.info("WebSocket连接关闭: {}", closeReason.getReasonPhrase());
    }

    @OnError
    public void onError(Session session, Throwable throwable) {
        log.error("WebSocket错误", throwable);
        try {
            session.close(new CloseReason(CloseReason.CloseCodes.UNEXPECTED_CONDITION, "服务器内部错误"));
        } catch (IOException e) {
            log.error("关闭WebSocket连接失败", e);
        }
    }
} 