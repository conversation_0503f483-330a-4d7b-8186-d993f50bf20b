package com.yiyi.ai_train_playground.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 京东商品状态枚举
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Getter
@AllArgsConstructor
public enum JdSyncStatus {

    /**
     * 未同步
     */
    UN_SYNC(0, "未同步"),

    /**
     * 同步中
     */
    SYNCING(1, "同步中"),

    /**
     * 已同步
     */
    SYNCED(2, "已同步");


    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码获取状态描述
     *
     * @param code 状态码
     * @return 状态描述，如果找不到则返回"未知状态"
     */
    public static String getDescriptionByCode(Integer code) {
        if (code == null) {
            return "未知状态";
        }
        
        for (JdSyncStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status.getDescription();
            }
        }
        return "未知状态";
    }

    /**
     * 根据状态码获取枚举对象
     *
     * @param code 状态码
     * @return 枚举对象，如果找不到则返回null
     */
    public static JdSyncStatus getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (JdSyncStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
