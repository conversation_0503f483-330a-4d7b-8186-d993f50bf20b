package com.yiyi.ai_train_playground.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 京东商品状态枚举
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Getter
@AllArgsConstructor
public enum JdProductStatus {

    /**
     * 删除
     */
    DELETED(-1, "删除"),

    /**
     * 从未上架
     */
    NEVER_ONLINE(1, "从未上架"),

    /**
     * 自主下架
     */
    SELF_OFFLINE(2, "自主下架"),

    /**
     * 系统下架
     */
    SYSTEM_OFFLINE(4, "系统下架"),

    /**
     * 上架
     */
    ONLINE(8, "上架"),

    /**
     * 从未上架待审
     */
    NEVER_ONLINE_PENDING(513, "从未上架待审"),

    /**
     * 自主下架待审
     */
    SELF_OFFLINE_PENDING(514, "自主下架待审"),

    /**
     * 系统下架待审
     */
    SYSTEM_OFFLINE_PENDING(516, "系统下架待审"),

    /**
     * 上架待审核
     */
    ONLINE_PENDING(520, "上架待审核"),

    /**
     * 系统下架审核失败
     */
    SYSTEM_OFFLINE_AUDIT_FAILED(1028, "系统下架审核失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码获取状态描述
     *
     * @param code 状态码
     * @return 状态描述，如果找不到则返回"未知状态"
     */
    public static String getDescriptionByCode(Integer code) {
        if (code == null) {
            return "未知状态";
        }
        
        for (JdProductStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status.getDescription();
            }
        }
        return "未知状态";
    }

    /**
     * 根据状态码获取枚举对象
     *
     * @param code 状态码
     * @return 枚举对象，如果找不到则返回null
     */
    public static JdProductStatus getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (JdProductStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
