package com.yiyi.ai_train_playground.enums;

import lombok.Getter;

@Getter
public enum FileType {
    EXCEL(0, "excel", "xls_", "excel/"),
    PIC(1, "pic", "pic_", "pic/"),
    VIDEO(2, "video", "vid_", "video/");

    private final Integer code;
    private final String type;
    private final String prefix;
    private final String path;

    FileType(Integer code, String type, String prefix, String path) {
        this.code = code;
        this.type = type;
        this.prefix = prefix;
        this.path = path;
    }

    public static FileType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (FileType fileType : FileType.values()) {
            if (fileType.getCode().equals(code)) {
                return fileType;
            }
        }
        return null;
    }
} 