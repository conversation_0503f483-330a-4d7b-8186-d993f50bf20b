package com.yiyi.ai_train_playground.enums;

/**
 * 场景名称枚举
 */
public enum SceneName {
    TRIAL_ONE("trialOne", "试用剧本"),
    TRIAL_FOUR("trialFour", "随机剧本"),
    FORMAL("formal", "正式剧本");
    
    private final String code;
    private final String name;
    
    SceneName(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public static SceneName fromCode(String code) {
        for (SceneName sceneName : values()) {
            if (sceneName.code.equals(code)) {
                return sceneName;
            }
        }
        throw new IllegalArgumentException("未知的场景代码: " + code);
    }
} 