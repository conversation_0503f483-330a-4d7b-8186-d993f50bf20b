package com.yiyi.ai_train_playground.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 图片向量处理线程池配置
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Slf4j
@Configuration
@EnableAsync
public class ImgVectorThreadPoolConfig {

    @Value("${jd.img-vector.thread-pool-size:50}")
    private Integer threadPoolSize;

    @Value("${jd.img-vector.max-thread-pool-size:100}")
    private Integer maxThreadPoolSize;

    @Value("${jd.img-vector.timeout-seconds:300}")
    private Integer timeoutSeconds;

    /**
     * 图片向量处理线程池
     * 
     * @return ThreadPoolExecutor
     */
    @Bean("imgVectorThreadPool")
    public ThreadPoolExecutor imgVectorThreadPool() {
        log.info("初始化图片向量处理线程池，核心线程数: {}, 最大线程数: {}, 超时时间: {}秒",
                threadPoolSize, maxThreadPoolSize, timeoutSeconds);

        return new ThreadPoolExecutor(
                threadPoolSize,                    // 核心线程数
                maxThreadPoolSize,                 // 最大线程数
                60L,                              // 空闲线程存活时间
                TimeUnit.SECONDS,                 // 时间单位
                new LinkedBlockingQueue<>(50),    // 任务队列，减少到50个任务，促进线程创建
                new ImgVectorThreadFactory(),     // 线程工厂
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：调用者执行
        );
    }

    /**
     * 自定义线程工厂
     */
    private static class ImgVectorThreadFactory implements ThreadFactory {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix = "img-vector-";

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            if (t.isDaemon()) {
                t.setDaemon(false);
            }
            if (t.getPriority() != Thread.NORM_PRIORITY) {
                t.setPriority(Thread.NORM_PRIORITY);
            }
            return t;
        }
    }
}
