package com.yiyi.ai_train_playground.config;

import com.hankcs.hanlp.HanLP;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.io.File;
import java.net.URL;

/**
 * HanLP配置类
 */
@Slf4j
@Configuration
public class HanLpConfig {

    @PostConstruct
    public void initHanLP() {
        try {
            // 获取数据目录路径
           /* URL dataUrl = this.getClass().getClassLoader().getResource("data");
            if (dataUrl == null) {
                log.warn("HanLP数据目录未找到，将使用默认配置");
                return;
            }
            
            String dataPath = dataUrl.getPath();*/

            String dataPath = "C:\\projects\\myaiprojects\\ai_train_playground\\src\\main\\resources\\data";
            // 处理Windows路径问题
            if (dataPath.startsWith("/") && System.getProperty("os.name").toLowerCase().contains("windows")) {
                dataPath = dataPath.substring(1);
            }
            
            // 确保路径存在
            File dataDir = new File(dataPath);
            if (!dataDir.exists()) {
                log.warn("HanLP数据目录不存在: {}", dataPath);
                return;
            }
            
            // 设置HanLP根目录和具体路径
            System.setProperty("hanlp.root", dataPath + "/");
            
            // 直接设置词典路径（只设置存在的字段）
            String dictPath = dataPath + "/dictionary/";
            HanLP.Config.CoreDictionaryPath = dictPath + "CoreNatureDictionary.txt";
            HanLP.Config.BiGramDictionaryPath = dictPath + "CoreNatureDictionary.ngram.txt";
            HanLP.Config.CoreStopWordDictionaryPath = dictPath + "stopwords.txt";
            HanLP.Config.PersonDictionaryPath = dictPath + "person/nr.txt";
            HanLP.Config.PlaceDictionaryPath = dictPath + "place/ns.txt";
            HanLP.Config.OrganizationDictionaryPath = dictPath + "organization/nt.txt";
            HanLP.Config.CustomDictionaryPath = new String[]{dictPath + "custom/CustomDictionary.txt"};
            
            // 设置基本配置
            HanLP.Config.ShowTermNature = true;
            HanLP.Config.Normalization = true;
            
            log.info("HanLP配置初始化完成，数据路径: {}", dataPath);
            log.info("核心词典路径: {}", HanLP.Config.CoreDictionaryPath);
            
        } catch (Exception e) {
            log.warn("HanLP配置初始化失败，将使用默认配置: {}", e.getMessage());
        }
    }
} 