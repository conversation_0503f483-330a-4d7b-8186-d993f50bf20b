package com.yiyi.ai_train_playground.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "oss")
public class OssConfig {
    private String endpoint;
    private String accessId;
    private String accessKey;
    private String bucketName;
    private Long tempFileExpiration = 24L; // 临时文件过期时间，默认24小时

    @Bean
    public OSS ossClient() {
        return new OSSClientBuilder().build(endpoint, accessId, accessKey);
    }
} 