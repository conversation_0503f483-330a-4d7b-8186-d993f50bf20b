package com.yiyi.ai_train_playground.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 京东配置类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "jd")
public class JdConfig {
    
    /**
     * 京东API服务器地址
     */
    private String serverUrl = "https://api.jd.com/routerjson";
    
    /**
     * 期望的state参数值
     */
    private String expectedState = "YyJdPlayground2025";
    
    /**
     * 京东应用Key
     */
    private String appKey = "58EC57CE1D6C6B997519BA25E73A7228";
    
    /**
     * 京东应用Secret
     */
    private String appSecret = "dbff743f33fd4a46bfa3399cf189e252";
    
    /**
     * 京东访问令牌
     */
    private String accessToken = "89fd9dcc03d34c6d997fc66e019700bcy2mw";
    
    /**
     * 京东令牌获取URL
     */
    private String tokenUrl = "https://open-oauth.jd.com/oauth2/access_token";

    /**
     * 京东令牌刷新URL
     */
    private String refreshTokenUrl = "https://open-oauth.jd.com/oauth2/refresh_token";

    /**
     * 重定向基础URL
     */
    private String redirectBaseUrl = "http://www.yiyiailocal.com:5173/product-management";
    
    /**
     * 同步配置
     */
    private Sync sync;

    /**
     * Token刷新配置
     */
    private TokenRefresh tokenRefresh;

    /**
     * 同步配置内部类
     */
    @Data
    public static class Sync {
        /**
         * 京东商品同步分页大小，每页获取的商品数量
         */
        private Integer pageSize;

        /**
         * 是否使用本地模拟数据开关，默认false使用远程API
         */
        private Boolean isMockSwitch = false;
    }

    /**
     * Token刷新配置内部类
     */
    @Data
    public static class TokenRefresh {
        /**
         * Token刷新间隔时间（分钟），默认60分钟
         */
        private Integer intervalMinutes = 60;

        /**
         * 分布式锁过期时间（分钟），默认50分钟
         */
        private Integer lockExpireMinutes = 50;
    }
} 