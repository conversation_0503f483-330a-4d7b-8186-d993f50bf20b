package com.yiyi.ai_train_playground.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "my.doubao")
public class DoubaoConfig {
    private Think think;
    private Normal normal;
    private String url;
    private Image image;
    private Integer estimateToken;
    private Embed embed;
    private ConnectionPool connectionPool;

    /**
     * 获取estimateToken，适配不同的字段名称
     * @return 估算的token数量
     */
    public Integer getEstimateToken() {
        return estimateToken;
    }

    @Data
    public static class Think {
        private Model model;
    }

    @Data
    public static class Normal {
        private Model model;
        private Endpoint endpoint;
    }

    @Data
    public static class Image {
        private Model model;
    }
    
    @Data
    public static class Embed {
        private String modelName;
    }

    @Data
    public static class Model {
        private String name;
    }

    @Data
    public static class Endpoint {
        private String name;
    }
    
    @Data
    public static class ConnectionPool {
        private Integer maxIdle = 100;
        private Integer maxRequests = 100;
        private Integer maxRequestsPerHost = 50;
        private Integer keepAliveDuration = 5; // 分钟
    }
} 