package com.yiyi.ai_train_playground.config;

import com.jd.open.api.sdk.DefaultJdClient;
import com.jd.open.api.sdk.JdClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 京东客户端工厂类
 * 用于创建带有动态accessToken的JdClient实例
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JdClientFactory {
    
    private final JdConfig jdConfig;
    
    /**
     * 创建带有指定accessToken的JdClient
     * 
     * @param accessToken 动态的访问令牌
     * @return JdClient实例
     */
    public JdClient createClient(String accessToken) {
        if (accessToken == null || accessToken.trim().isEmpty()) {
            log.warn("accessToken为空，将使用配置文件中的默认值");
            accessToken = jdConfig.getAccessToken();
        }
        
        log.debug("正在创建京东客户端，accessToken: {}****", 
                accessToken != null && accessToken.length() > 4 
                        ? accessToken.substring(0, 4) : "null");
        
        return new DefaultJdClient(
                jdConfig.getServerUrl(),
                accessToken,
                jdConfig.getAppKey(),
                jdConfig.getAppSecret(),
                0, // 连接超时时间，0表示使用默认值
                0  // 读取超时时间，0表示使用默认值
        );
    }
    
    /**
     * 使用配置文件中默认accessToken创建JdClient
     * 
     * @return JdClient实例
     */
    public JdClient createDefaultClient() {
        return createClient(jdConfig.getAccessToken());
    }
    
    /**
     * 验证accessToken是否有效（基本格式检查）
     * 
     * @param accessToken 要验证的令牌
     * @return 是否有效
     */
    public boolean isValidAccessToken(String accessToken) {
        if (accessToken == null || accessToken.trim().isEmpty()) {
            return false;
        }
        
        // 基本长度检查（京东accessToken通常较长）
        return accessToken.length() >= 20;
    }
} 