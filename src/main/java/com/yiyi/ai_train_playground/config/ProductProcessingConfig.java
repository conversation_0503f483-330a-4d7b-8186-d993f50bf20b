package com.yiyi.ai_train_playground.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "product.processing")
public class ProductProcessingConfig {
    private Integer chunkSize = 60;
    private Integer overlapSize = 12;
    private Integer vectorDimension = 2560;
    private String collectionName = "train_prod_collection";
} 