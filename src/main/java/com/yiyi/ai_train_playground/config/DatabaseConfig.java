package com.yiyi.ai_train_playground.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * 数据库配置类
 * 主要负责事务管理器的配置
 */
@Configuration
@EnableTransactionManagement
public class DatabaseConfig {

    /**
     * 配置数据源事务管理器
     * 解决 SqlSession 和 JDBC Connection 未被 Spring 管理的问题
     */
    @Bean
    public PlatformTransactionManager transactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
} 