package com.yiyi.ai_train_playground.config;

import com.yiyi.ai_train_playground.security.JwtAuthenticationEntryPoint;
import com.yiyi.ai_train_playground.security.JwtAuthenticationFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    public SecurityConfig(JwtAuthenticationFilter jwtAuthenticationFilter,
                         JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint) {
        this.jwtAuthenticationFilter = jwtAuthenticationFilter;
        this.jwtAuthenticationEntryPoint = jwtAuthenticationEntryPoint;
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        return http
            .csrf(csrf -> csrf.disable())
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(auth -> auth
                .requestMatchers("/api/auth/login").permitAll()
                .requestMatchers("/api/auth/register").permitAll()
                .requestMatchers("/api/sms/**").permitAll() // 允许短信验证码接口不需要认证
                .requestMatchers("/api/multi-media-upload").permitAll() // 允许多媒体上传接口绕过Spring Security认证
                .requestMatchers("/api/hanlp/**").permitAll() // 允许HanLP接口不需要认证
                .requestMatchers("/api/qdrant/**").permitAll() // 允许Qdrant接口不需要认证（测试用）
                .requestMatchers("/api/test-chunking").permitAll() // 允许测试接口不需要认证
                .requestMatchers("/api/test-vector-insert").permitAll() // 允许向量插入测试接口不需要认证
                .requestMatchers("/test/**").permitAll() // 允许所有测试接口不需要认证
                .requestMatchers("/api/yiyicallback").permitAll() // 允许京东回调接口不需要认证
                .requestMatchers("/api/v2/yiyicallback").permitAll() // 允许京东回调接口V2不需要认证
                .requestMatchers("/api/v2/sms/*").permitAll() // 允许手机-xid快速绑定接口不需要认证
                .requestMatchers("/api/user/**").permitAll() // 允许用户接口不需要认证
                .requestMatchers("/api/**").authenticated()
                    .requestMatchers("/api/**").authenticated()
                .anyRequest().permitAll()
            )
            .exceptionHandling(exceptions -> exceptions
                .authenticationEntryPoint(jwtAuthenticationEntryPoint)
            )
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
            .headers(headers -> headers.frameOptions().disable())
            .build();
    }

    @Bean
    public BCryptPasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
