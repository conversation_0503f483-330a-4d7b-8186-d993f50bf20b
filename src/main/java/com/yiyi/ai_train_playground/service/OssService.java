package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.dto.FileUploadDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface OssService {
    /**
     * 上传文件到临时目录
     * @param file 文件
     * @param type 文件类型
     * @param teamId 团队ID
     * @return 文件访问URL
     */
    String upload(MultipartFile file, Integer type, Long teamId);

    /**
     * 批量生成永久文件
     * @param fileNames 文件名数组
     * @return 永久文件访问URL列表
     */
    List<String> geneProc(String[] fileNames);
} 