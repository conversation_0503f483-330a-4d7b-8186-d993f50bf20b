package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.entity.TrainTeamShops;

/**
 * 团队店铺服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
public interface TrainTeamShopsService {
    
    /**
     * 根据团队ID和店铺类型查询店铺信息
     *
     * @param teamId 团队ID
     * @param shopType 店铺类型
     * @return 店铺信息，如果不存在返回null
     */
    TrainTeamShops findByTeamIdAndShopType(Long teamId, Integer shopType);
    
    /**
     * 根据店铺ID查询店铺信息
     *
     * @param shopId 店铺ID
     * @return 店铺信息，如果不存在返回null
     */
    TrainTeamShops findByShopId(Long shopId);
    
    /**
     * 插入新的店铺记录
     *
     * @param trainTeamShops 团队店铺信息
     * @return 插入成功返回true，失败返回false
     */
    boolean insert(TrainTeamShops trainTeamShops);
    
    /**
     * 更新店铺授权状态和同步状态
     *
     * @param teamId 团队ID
     * @param shopType 店铺类型
     * @param isAuthorize 是否授权
     * @param isSyncComplete 同步完成状态
     * @param updater 更新人
     * @return 更新成功返回true，失败返回false
     */
    boolean updateAuthorizationStatus(Long teamId, Integer shopType, Boolean isAuthorize, 
                                    Integer isSyncComplete, String updater);
    
    /**
     * 根据店铺ID更新店铺授权状态和同步状态
     *
     * @param shopId 店铺ID
     * @param isAuthorize 是否授权
     * @param isSyncComplete 同步完成状态
     * @param updater 更新人
     * @return 更新成功返回true，失败返回false
     */
    boolean updateByShopId(Long shopId, Boolean isAuthorize, Integer isSyncComplete, String updater);
    
    /**
     * 根据店铺ID更新团队店铺信息（完整实体更新）
     *
     * @param trainTeamShops 团队店铺信息
     * @return 更新成功返回true，失败返回false
     */
    boolean updateEntityByShopId(TrainTeamShops trainTeamShops);
    
    /**
     * 检查店铺是否存在
     *
     * @param shopId 店铺ID
     * @return 存在返回true，不存在返回false
     */
    boolean existsByShopId(Long shopId);
    
    /**
     * 保存或更新店铺信息
     * 如果店铺已存在（根据shopId判断）则更新，否则插入
     *
     * @param trainTeamShops 团队店铺信息
     * @return 操作成功返回true，失败返回false
     */
    boolean saveOrUpdate(TrainTeamShops trainTeamShops);
}
