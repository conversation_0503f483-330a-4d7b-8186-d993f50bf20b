package com.yiyi.ai_train_playground.service.user.impl;

import com.yiyi.ai_train_playground.entity.User;
import com.yiyi.ai_train_playground.mapper.UserMapper;
import com.yiyi.ai_train_playground.service.user.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-21
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Override
    public User findByUserId(Long userId) {
        log.info("根据用户ID查询用户信息: userId={}", userId);
        
        if (userId == null) {
            log.warn("用户ID不能为空");
            return null;
        }
        
        try {
            User user = userMapper.findByUserId(userId);
            if (user != null) {
                log.info("查询用户成功: userId={}, username={}", userId, user.getUsername());
                // 出于安全考虑，清除密码字段
                // user.setPasswordHash(null);
            } else {
                log.warn("用户不存在: userId={}", userId);
            }
            return user;
        } catch (Exception e) {
            log.error("查询用户失败: userId={}", userId, e);
            throw new RuntimeException("查询用户失败: " + e.getMessage(), e);
        }
    }
}
