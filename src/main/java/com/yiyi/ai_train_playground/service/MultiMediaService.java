package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.util.JwtUtil;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

public interface MultiMediaService {
    
    /**
     * 处理多媒体上传并返回Flux流式响应
     * 
     * @param file 文件（本地上传时使用）
     * @param uploadType 上传类型 1:本地上传 2:URL上传
     * @param fileUrl URL上传时的文件URL
     * @param promKW 提示词关键词
     * @param jwtToken JWT token字符串
     * @param jwtUtil JWT工具类
     * @return Flux流式响应
     */
    Flux<String> processMultiMediaUpload(MultipartFile file, String uploadType, String fileUrl, String promKW, String jwtToken, JwtUtil jwtUtil);
} 