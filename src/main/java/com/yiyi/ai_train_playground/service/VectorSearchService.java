package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.entity.TrainJdProducts;
import com.yiyi.ai_train_playground.model.VectorData;
import com.yiyi.ai_train_playground.model.VectorSearchResult;

import java.util.List;
import java.util.Map;

/**
 * 向量搜索服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
public interface VectorSearchService {
    
    /**
     * 根据文本查询进行向量搜索
     * 
     * @param queryText 查询文本
     * @param teamId 团队ID
     * @param externalProductId 外部产品ID
     * @param limit 返回结果数量限制
     * @return 搜索结果内容文本
     */
    String searchByText(String queryText, String teamId, String externalProductId, int limit);
    
    /**
     * 根据向量进行搜索
     * 
     * @param vector 查询向量
     * @param teamId 团队ID
     * @param externalProductId 外部产品ID
     * @param limit 返回结果数量限制
     * @return 向量搜索结果
     */
    VectorSearchResult searchByVector(List<Float> vector, String teamId, String externalProductId, int limit);
    
    /**
     * 处理并存储产品详情的向量数据
     * 
     * @param externalProductId 外部产品ID
     * @param teamId 团队ID
     * @param productDetail 产品详情文本
     */
    void processAndStoreProductVectors(String externalProductId, String teamId, String productDetail);
    
    /**
     * 删除指定产品的所有向量数据
     * 
     * @param externalProductId 外部产品ID
     * @param teamId 团队ID
     * @return 是否删除成功
     */
    boolean deleteProductVectors(String externalProductId, String teamId);
    
    /**
     * 文本向量化
     * 
     * @param texts 待向量化的文本列表
     * @return 向量列表
     */
    List<List<Double>> embedTexts(List<String> texts);
    
    /**
     * 检查向量集合是否存在，不存在则创建
     * 
     * @param collectionName 集合名称
     * @return 集合是否可用
     */
    boolean ensureCollectionExists(String collectionName);
    
    /**
     * 批量插入向量数据
     * 
     * @param collectionName 集合名称
     * @param vectorDataList 向量数据列表
     * @return 是否插入成功
     */
    boolean insertVectors(String collectionName, List<VectorData> vectorDataList);
    
    /**
     * 根据payload条件查询向量ID
     *
     * @param collectionName 集合名称
     * @param conditions 查询条件
     * @return 向量ID列表
     */
    List<String> getVectorIdsByConditions(String collectionName, Map<String, Object> conditions);

    /**
     * 处理京东商品向量数据
     *
     * @param trainJdProducts 京东商品对象
     * @param recoTextWithMD 推荐文本（Markdown格式）
     * @param imgUrl 图片URL
     * @param teamId 团队ID
     * @return 是否插入成功
     */
    boolean processVectorForWare(TrainJdProducts trainJdProducts, String recoTextWithMD, String imgUrl, Long teamId);

    /**
     * 删除京东商品的向量数据
     *
     * @param trainJdProducts 京东商品对象
     * @param imgUrl 图片URL
     * @param teamId 团队ID
     * @return 是否删除成功
     */
    boolean deleteVectorForWare(TrainJdProducts trainJdProducts, String imgUrl, Long teamId);
}