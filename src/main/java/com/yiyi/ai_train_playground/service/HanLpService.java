package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.model.TextChunk;

import java.util.List;

/**
 * HanLP自然语言处理服务接口
 */
public interface HanLpService {
    
    /**
     * 智能语义分块
     * 
     * @param text 待分块的文本
     * @param chunkSize 分块大小（字符数）
     * @param overlapSize 重叠字符数
     * @return 分块结果列表
     */
    List<TextChunk> semanticChunking(String text, int chunkSize, int overlapSize);
    
    /**
     * 文本分词
     * 
     * @param text 待分词的文本
     * @return 分词结果列表
     */
    List<TextChunk.SemanticUnit> segment(String text);
    
    /**
     * 提取关键词
     * 
     * @param text 待提取的文本
     * @param topK 返回前K个关键词
     * @return 关键词列表
     */
    List<String> extractKeywords(String text, int topK);
    
    /**
     * 提取摘要
     * 
     * @param text 待提取的文本
     * @param maxSentences 最大句子数
     * @return 摘要文本
     */
    String extractSummary(String text, int maxSentences);
} 