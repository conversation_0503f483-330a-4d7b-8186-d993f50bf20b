package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.service.*;
import com.yiyi.ai_train_playground.service.jd.AsyncProductProcessingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 异步产品处理服务实现类
 * 
 * <AUTHOR> Assistant
 * 这个类好像没有用到
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsyncProductProcessingServiceImpl implements AsyncProductProcessingService {

    private final VectorSearchService vectorSearchService;

    @Override
    @Async("productProcessingExecutor")
    public void processProductDetailAsync(String externalProductId, String teamId, String productDetail) {
        log.info("开始异步处理商品详情向量化，商品ID: {}, 团队ID: {}", externalProductId, teamId);
        
        try {
            // 使用VectorSearchService处理和存储产品向量
            vectorSearchService.processAndStoreProductVectors(externalProductId, teamId, productDetail);
            log.info("商品详情异步处理完成，商品ID: {}, 团队ID: {}", externalProductId, teamId);
            
        } catch (Exception e) {
            log.error("商品详情异步处理失败，商品ID: {}, 团队ID: {}, 错误信息: {}", 
                    externalProductId, teamId, e.getMessage(), e);
            
            // 可以考虑添加重试机制或者错误通知机制
            try {
                // 这里可以添加错误恢复逻辑，比如重试或通知
                log.info("尝试错误恢复处理，商品ID: {}", externalProductId);
            } catch (Exception recoveryException) {
                log.error("错误恢复处理也失败了，商品ID: {}", externalProductId, recoveryException);
            }
        }
    }
}