package com.yiyi.ai_train_playground.service.jd.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.text.SimpleDateFormat;
import com.jd.open.api.sdk.domain.ware.WareReadService.response.searchWare4Valid.Ware;
import com.yiyi.ai_train_playground.entity.TrainJdProducts;
import com.yiyi.ai_train_playground.service.jd.GetTdFromJsonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 从JSON文件读取并转换为TrainJdProducts的服务实现类
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@Service
public class GetTdFromJsonServiceImpl implements GetTdFromJsonService {

    @Autowired
    private YiYiJdServiceImpl yiYiJdService;

    private final ObjectMapper objectMapper;

    /**
     * JSON文件路径，可通过配置文件配置
     */
    @Value("${jd.json.file.path:C:/Users/<USER>/Downloads/temp/bot_product.json}")
    private String jsonFilePath;

    /**
     * 构造函数，初始化ObjectMapper并配置忽略未知字段
     */
    public GetTdFromJsonServiceImpl() {
        this.objectMapper = new ObjectMapper();
        // 配置忽略未知字段，避免JSON中有额外字段时解析失败
        this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 配置日期格式
        this.objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    }
    
    @Override
    public List<TrainJdProducts> getTrainJdProductsFromJson() {
        log.info("开始从JSON文件读取数据并转换为TrainJdProducts列表，文件路径: {}", jsonFilePath);

        try {
            // 1. 读取JSON文件
            File jsonFile = new File(jsonFilePath);
            if (!jsonFile.exists()) {
                log.error("JSON文件不存在: {}", jsonFilePath);
                return new ArrayList<>();
            }

            // 2. 使用Jackson的ObjectMapper反序列化为List<Map>，避免字段映射问题
            List<Map<String, Object>> rawDataList = objectMapper.readValue(jsonFile, new TypeReference<List<Map<String, Object>>>() {});
            log.info("成功读取JSON文件，共{}条原始记录", rawDataList.size());

            // 3. 转换为List<TrainJdProducts>
            List<TrainJdProducts> trainJdProductsList = new ArrayList<>();

            for (Map<String, Object> rawData : rawDataList) {
                try {
                    // 直接从Map转换为TrainJdProducts
                    TrainJdProducts trainJdProducts = convertMapToTrainJdProducts(rawData);
                    if (trainJdProducts != null) {
                        trainJdProductsList.add(trainJdProducts);
                    }
                } catch (Exception e) {
                    log.error("转换原始数据时出错，wareId: {}, 错误信息: {}",
                             rawData.get("ware_id"), e.getMessage());
                }
            }

            log.info("成功转换{}条TrainJdProducts记录", trainJdProductsList.size());
            return trainJdProductsList;

        } catch (Exception e) {
            log.error("从JSON文件读取并转换数据时出错", e);
            return new ArrayList<>();
        }
    }

    /**
     * 将Map数据转换为TrainJdProducts对象
     *
     * @param rawData 原始Map数据
     * @return TrainJdProducts对象
     */
    private TrainJdProducts convertMapToTrainJdProducts(Map<String, Object> rawData) {
        try {
            TrainJdProducts product = new TrainJdProducts();

            // 设置基本字段，注意JSON中使用下划线命名
            product.setWareId(getLongValue(rawData, "ware_id"));
            product.setShopId(getLongValue(rawData, "shop_id"));
            product.setTitle(getStringValue(rawData, "title"));
            product.setBrandId(getLongValue(rawData, "brand_id"));
            product.setBrandName(getStringValue(rawData, "brand_name"));
            product.setCategoryId(getLongValue(rawData, "category_id"));
            product.setCategorySecId(getLongValue(rawData, "category_sec_id"));
            product.setColType(getIntegerValue(rawData, "col_type"));
            product.setCostPrice(getBigDecimalValue(rawData, "cost_price"));
            product.setHeight(getBigDecimalValue(rawData, "height"));
            product.setJdPrice(getBigDecimalValue(rawData, "jd_price"));
            product.setLength(getBigDecimalValue(rawData, "length"));
            product.setLogo(getStringValue(rawData, "logo"));
            product.setMarketPrice(getBigDecimalValue(rawData, "market_price"));
            product.setSpuId(getLongValue(rawData, "spu_id"));
            product.setStockNum(getIntegerValue(rawData, "stock_num"));
            product.setTemplateId(getLongValue(rawData, "template_id"));
            product.setWareStatus(getIntegerValue(rawData, "ware_status"));
            product.setWeight(getBigDecimalValue(rawData, "weight"));
            product.setWidth(getBigDecimalValue(rawData, "width"));
            product.setWrap(getStringValue(rawData, "wrap"));
            product.setWareLocation(getIntegerValue(rawData, "ware_location"));
            product.setIntroduction(getStringValue(rawData, "introduction"));
            product.setMobileDesc(getStringValue(rawData, "mobile_desc"));
            product.setFitCaseHtmlApp(getStringValue(rawData, "fit_case_html_app"));

            // 处理日期字段
            product.setCreated(parseLocalDateTimeFromString(getStringValue(rawData, "created")));
            product.setModified(parseLocalDateTimeFromString(getStringValue(rawData, "modified")));
            product.setOnlineTime(parseLocalDateTimeFromString(getStringValue(rawData, "online_time")));
            product.setOfflineTime(parseLocalDateTimeFromString(getStringValue(rawData, "offline_time")));

            // 处理复杂字段
            Object features = rawData.get("features");
            if (features != null) {
                product.setFeatures(objectMapper.writeValueAsString(features));
            }

            Object multiCateProps = rawData.get("multi_cate_props");
            if (multiCateProps != null) {
                product.setMultiCateProps(objectMapper.writeValueAsString(multiCateProps));
            }

            return product;

        } catch (Exception e) {
            log.error("转换Map数据为TrainJdProducts时出错: {}", e.getMessage());
            return null;
        }
    }

    // 辅助方法：安全获取Long值
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    // 辅助方法：安全获取Integer值
    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    // 辅助方法：安全获取BigDecimal值
    private BigDecimal getBigDecimalValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    // 辅助方法：安全获取String值
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    // 辅助方法：解析日期字符串为LocalDateTime
    private LocalDateTime parseLocalDateTimeFromString(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        log.debug("尝试解析日期字符串: '{}'", dateStr);

        // 尝试多种日期格式
        String[] dateFormats = {
            "yyyy-MM-dd HH:mm:ss",  // 2025-04-02 10:30:00
            "yyyy-MM-dd",           // 2025-04-02
            "yyyy/MM/dd HH:mm:ss",  // 2025/04/02 10:30:00
            "yyyy/MM/dd"            // 2025/04/02
        };

        for (String format : dateFormats) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
                LocalDateTime result;
                if (format.contains("HH:mm:ss")) {
                    result = LocalDateTime.parse(dateStr, formatter);
                } else {
                    // 如果只有日期，设置时间为00:00:00
                    result = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(format)).atStartOfDay();
                }
                log.debug("成功解析日期: '{}' -> {} (使用格式: {})", dateStr, result, format);
                return result;
            } catch (DateTimeParseException e) {
                log.debug("日期格式 '{}' 解析失败: {}", format, e.getMessage());
            }
        }

        log.warn("无法解析日期字符串: '{}'", dateStr);
        return null;
    }
}
