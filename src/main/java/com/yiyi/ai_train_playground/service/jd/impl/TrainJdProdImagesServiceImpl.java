package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.entity.TrainJdProdImages;
import com.yiyi.ai_train_playground.mapper.TrainJdProdImagesMapper;
import com.yiyi.ai_train_playground.service.jd.TrainJdProdImagesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 京东商品图片信息服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrainJdProdImagesServiceImpl implements TrainJdProdImagesService {
    
    private final TrainJdProdImagesMapper trainJdProdImagesMapper;
    
    @Override
    @Transactional
    public int insert(TrainJdProdImages image) {
        if (image == null) {
            log.warn("插入商品图片信息不能为空");
            return 0;
        }
        
        // 参数验证
        if (image.getTeamId() == null || image.getJdProdId() == null) {
            log.warn("插入商品图片信息teamId和jdProdId不能为空: teamId={}, jdProdId={}", 
                    image.getTeamId(), image.getJdProdId());
            return 0;
        }
        
        try {
            log.debug("插入商品图片信息: teamId={}, jdProdId={}, imgUrl={}", 
                    image.getTeamId(), image.getJdProdId(), image.getImgUrl());
            int result = trainJdProdImagesMapper.insert(image);
            log.debug("插入商品图片信息完成: teamId={}, jdProdId={}, 影响行数={}", 
                    image.getTeamId(), image.getJdProdId(), result);
            return result;
        } catch (Exception e) {
            log.error("插入商品图片信息失败: teamId={}, jdProdId={}", 
                    image.getTeamId(), image.getJdProdId(), e);
            throw new RuntimeException("插入商品图片信息失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public int batchInsert(List<TrainJdProdImages> imageList) {
        if (CollectionUtils.isEmpty(imageList)) {
            log.warn("批量插入商品图片信息列表不能为空");
            return 0;
        }
        
        try {
            log.debug("批量插入商品图片信息: 数量={}", imageList.size());
            int result = trainJdProdImagesMapper.batchInsert(imageList);
            log.debug("批量插入商品图片信息完成: 数量={}, 影响行数={}", imageList.size(), result);
            return result;
        } catch (Exception e) {
            log.error("批量插入商品图片信息失败: 数量={}", imageList.size(), e);
            throw new RuntimeException("批量插入商品图片信息失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public int updateById(TrainJdProdImages image) {
        if (image == null || image.getId() == null) {
            log.warn("更新商品图片信息ID不能为空");
            return 0;
        }
        
        try {
            log.debug("更新商品图片信息: id={}", image.getId());
            int result = trainJdProdImagesMapper.updateById(image);
            log.debug("更新商品图片信息完成: id={}, 影响行数={}", image.getId(), result);
            return result;
        } catch (Exception e) {
            log.error("更新商品图片信息失败: id={}", image.getId(), e);
            throw new RuntimeException("更新商品图片信息失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public int deleteById(Long id) {
        if (id == null) {
            log.warn("删除商品图片信息ID不能为空");
            return 0;
        }
        
        try {
            log.debug("删除商品图片信息: id={}", id);
            int result = trainJdProdImagesMapper.deleteById(id);
            log.debug("删除商品图片信息完成: id={}, 影响行数={}", id, result);
            return result;
        } catch (Exception e) {
            log.error("删除商品图片信息失败: id={}", id, e);
            throw new RuntimeException("删除商品图片信息失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public List<TrainJdProdImages> findByTeamIdAndJdProdId(Long teamId, Long jdProdId) {
        if (teamId == null || jdProdId == null) {
            log.warn("查询参数不能为空: teamId={}, jdProdId={}", teamId, jdProdId);
            return Collections.emptyList();
        }
        
        try {
            log.debug("查询商品图片列表: teamId={}, jdProdId={}", teamId, jdProdId);
            List<TrainJdProdImages> result = trainJdProdImagesMapper.findByTeamIdAndJdProdId(teamId, jdProdId);
            log.debug("查询商品图片列表完成: teamId={}, jdProdId={}, 数量={}", 
                    teamId, jdProdId, result.size());
            return result;
        } catch (Exception e) {
            log.error("查询商品图片列表失败: teamId={}, jdProdId={}", teamId, jdProdId, e);
            throw new RuntimeException("查询商品图片列表失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public TrainJdProdImages findById(Long id) {
        if (id == null) {
            log.warn("查询商品图片信息ID不能为空");
            return null;
        }
        
        try {
            log.debug("查询商品图片信息: id={}", id);
            return trainJdProdImagesMapper.findById(id);
        } catch (Exception e) {
            log.error("查询商品图片信息失败: id={}", id, e);
            throw new RuntimeException("查询商品图片信息失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public List<TrainJdProdImages> findByTeamId(Long teamId) {
        if (teamId == null) {
            log.warn("查询参数teamId不能为空");
            return Collections.emptyList();
        }
        
        try {
            log.debug("查询团队商品图片列表: teamId={}", teamId);
            List<TrainJdProdImages> result = trainJdProdImagesMapper.findByTeamId(teamId);
            log.debug("查询团队商品图片列表完成: teamId={}, 数量={}", teamId, result.size());
            return result;
        } catch (Exception e) {
            log.error("查询团队商品图片列表失败: teamId={}", teamId, e);
            throw new RuntimeException("查询团队商品图片列表失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public List<TrainJdProdImages> findByTeamIdAndSyncStatus(Long teamId, Integer syncStatus) {
        if (teamId == null || syncStatus == null) {
            log.warn("查询参数不能为空: teamId={}, syncStatus={}", teamId, syncStatus);
            return Collections.emptyList();
        }
        
        try {
            log.debug("根据同步状态查询商品图片列表: teamId={}, syncStatus={}", teamId, syncStatus);
            List<TrainJdProdImages> result = trainJdProdImagesMapper.findByTeamIdAndSyncStatus(teamId, syncStatus);
            log.debug("根据同步状态查询商品图片列表完成: teamId={}, syncStatus={}, 数量={}", 
                    teamId, syncStatus, result.size());
            return result;
        } catch (Exception e) {
            log.error("根据同步状态查询商品图片列表失败: teamId={}, syncStatus={}", teamId, syncStatus, e);
            throw new RuntimeException("根据同步状态查询商品图片列表失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public int deleteByTeamIdAndJdProdId(Long teamId, Long jdProdId) {
        if (teamId == null || jdProdId == null) {
            log.warn("删除参数不能为空: teamId={}, jdProdId={}", teamId, jdProdId);
            return 0;
        }
        
        try {
            log.debug("删除商品图片信息: teamId={}, jdProdId={}", teamId, jdProdId);
            int result = trainJdProdImagesMapper.deleteByTeamIdAndJdProdId(teamId, jdProdId);
            log.debug("删除商品图片信息完成: teamId={}, jdProdId={}, 影响行数={}", 
                    teamId, jdProdId, result);
            return result;
        } catch (Exception e) {
            log.error("删除商品图片信息失败: teamId={}, jdProdId={}", teamId, jdProdId, e);
            throw new RuntimeException("删除商品图片信息失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public Long countByTeamId(Long teamId) {
        if (teamId == null) {
            log.warn("统计参数teamId不能为空");
            return 0L;
        }
        
        try {
            log.debug("统计团队商品图片数量: teamId={}", teamId);
            Long count = trainJdProdImagesMapper.countByTeamId(teamId);
            log.debug("统计团队商品图片数量完成: teamId={}, 数量={}", teamId, count);
            return count;
        } catch (Exception e) {
            log.error("统计团队商品图片数量失败: teamId={}", teamId, e);
            throw new RuntimeException("统计团队商品图片数量失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public Long countByTeamIdAndJdProdId(Long teamId, Long jdProdId) {
        if (teamId == null || jdProdId == null) {
            log.warn("统计参数不能为空: teamId={}, jdProdId={}", teamId, jdProdId);
            return 0L;
        }
        
        try {
            log.debug("统计指定商品图片数量: teamId={}, jdProdId={}", teamId, jdProdId);
            Long count = trainJdProdImagesMapper.countByTeamIdAndJdProdId(teamId, jdProdId);
            log.debug("统计指定商品图片数量完成: teamId={}, jdProdId={}, 数量={}", 
                    teamId, jdProdId, count);
            return count;
        } catch (Exception e) {
            log.error("统计指定商品图片数量失败: teamId={}, jdProdId={}", teamId, jdProdId, e);
            throw new RuntimeException("统计指定商品图片数量失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional
    public int saveOrUpdate(TrainJdProdImages image) {
        if (image == null) {
            log.warn("保存或更新商品图片信息不能为空");
            return 0;
        }
        
        // 参数验证
        if (image.getTeamId() == null || image.getJdProdId() == null) {
            log.warn("商品图片信息teamId和jdProdId不能为空: teamId={}, jdProdId={}", 
                    image.getTeamId(), image.getJdProdId());
            return 0;
        }
        
        try {
            // 查询是否已存在相同的图片记录
            TrainJdProdImages existingImage = trainJdProdImagesMapper.findByTeamIdAndJdProdIdAndImgUrl(
                    image.getTeamId(), image.getJdProdId(), image.getImgUrl());

            if (existingImage != null) {
                // 如果存在相同URL的图片，执行更新
                image.setId(existingImage.getId());
                log.debug("图片已存在，执行更新: teamId={}, jdProdId={}, imgUrl={}",
                        image.getTeamId(), image.getJdProdId(), image.getImgUrl());
                return updateById(image);
            } else {
                // 不存在则插入
                log.debug("图片不存在，执行插入: teamId={}, jdProdId={}, imgUrl={}",
                        image.getTeamId(), image.getJdProdId(), image.getImgUrl());
                return insert(image);
            }
            
        } catch (Exception e) {
            log.error("保存或更新商品图片信息失败: teamId={}, jdProdId={}",
                    image.getTeamId(), image.getJdProdId(), e);
            throw new RuntimeException("保存或更新商品图片信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<TrainJdProdImages> findByTeamIdAndSyncStatusWithPagination(Long teamId, Integer syncStatus, Long jdProdId, Integer offset, Integer pageSize) {
        // 参数验证
        if (teamId == null) {
            log.warn("团队ID不能为空");
            return Collections.emptyList();
        }

        if (offset == null || offset < 0) {
            log.warn("偏移量参数无效: offset={}", offset);
            return Collections.emptyList();
        }

        if (pageSize == null || pageSize <= 0) {
            log.warn("分页大小参数无效: pageSize={}", pageSize);
            return Collections.emptyList();
        }

        try {
            log.debug("根据team_id、sync_status分页查询商品图片列表: teamId={}, syncStatus={}, jdProdId={}, offset={}, pageSize={}",
                    teamId, syncStatus, jdProdId, offset, pageSize);

            List<TrainJdProdImages> images = trainJdProdImagesMapper.findByTeamIdAndSyncStatusWithPagination(
                    teamId, syncStatus, jdProdId, offset, pageSize);

            log.debug("根据team_id、sync_status分页查询完成: teamId={}, 返回数量={}", teamId, images.size());
            return images;

        } catch (Exception e) {
            log.error("根据team_id、sync_status分页查询失败: teamId={}, syncStatus={}, jdProdId={}, offset={}, pageSize={}",
                    teamId, syncStatus, jdProdId, offset, pageSize, e);
            throw new RuntimeException("根据team_id、sync_status分页查询失败: " + e.getMessage(), e);
        }
    }
}
