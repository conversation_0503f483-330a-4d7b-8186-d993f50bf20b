package com.yiyi.ai_train_playground.service.jd;

import com.yiyi.ai_train_playground.entity.TrainJdAccessToken;

import java.util.List;

/**
 * 京东访问令牌服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
public interface TrainJdAccessTokenService {
    
    /**
     * 插入京东访问令牌
     *
     * @param token 访问令牌信息
     * @return 插入成功返回true，失败返回false
     */
    boolean insert(TrainJdAccessToken token);
    
    /**
     * 根据xid查询访问令牌
     *
     * @param xid 唯一标识
     * @return 访问令牌信息，如果不存在返回null
     */
    TrainJdAccessToken findByXid(String xid);
    
    /**
     * 根据用户ID和团队ID查询访问令牌
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 访问令牌信息，如果不存在返回null
     */
    TrainJdAccessToken findByUserIdAndTeamId(Long userId, Long teamId);
    
    /**
     * 根据店铺ID查询访问令牌
     *
     * @param shopId 店铺ID
     * @return 访问令牌信息，如果不存在返回null
     */
    TrainJdAccessToken findByShopId(Long shopId);
    
    /**
     * 根据xid更新访问令牌
     *
     * @param token 访问令牌信息
     * @return 更新成功返回true，失败返回false
     */
    boolean updateByXid(TrainJdAccessToken token);
    
    /**
     * 查询所有访问令牌记录
     *
     * @return 访问令牌列表
     */
    List<TrainJdAccessToken> findAll();
    
    /**
     * 更新令牌信息（用于刷新token）
     *
     * @param token 访问令牌信息
     * @return 更新成功返回true，失败返回false
     */
    boolean updateTokenInfo(TrainJdAccessToken token);
    
    /**
     * 检查访问令牌是否存在
     *
     * @param xid 唯一标识
     * @return 存在返回true，不存在返回false
     */
    boolean existsByXid(String xid);
    
    /**
     * 检查访问令牌是否过期
     *
     * @param token 访问令牌信息
     * @return 过期返回true，未过期返回false
     */
    boolean isTokenExpired(TrainJdAccessToken token);
    
    /**
     * 保存或更新访问令牌
     * 如果令牌已存在（根据xid判断）则更新，否则插入
     *
     * @param token 访问令牌信息
     * @return 操作成功返回true，失败返回false
     */
    boolean saveOrUpdate(TrainJdAccessToken token);
}
