package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.config.JdConfig;
import com.yiyi.ai_train_playground.dto.JdCallbackRequest;
import com.yiyi.ai_train_playground.dto.JdCallbackResult;
import com.yiyi.ai_train_playground.entity.TrainJdAccessToken;
import com.yiyi.ai_train_playground.mapper.TrainJdAccessTokenMapper;
import com.yiyi.ai_train_playground.service.jd.JdCallbackServiceV2;
import com.yiyi.ai_train_playground.service.jd.JdNewSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 京东回调服务实现V2
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JdCallbackServiceV2Impl implements JdCallbackServiceV2 {

    private final TrainJdAccessTokenMapper trainJdAccessTokenMapper;
    private final WebClient.Builder webClientBuilder;
    private final JdConfig jdConfig;
    private final JdNewSyncService jdNewSyncService;
    
    @Override
    public JdCallbackResult handleCallback(JdCallbackRequest request) {
        log.info("处理京东回调请求，state: {}, code: {}, teamId: {}", 
                 request.getState(), request.getCode(), request.getTeamId());
        
        try {
            // 1. 检查state参数
            if (!jdConfig.getExpectedState().equals(request.getState())) {
                log.warn("非法的state参数: {}", request.getState());
                return JdCallbackResult.failure("非法的state参数");
            }
            
            // 2. 获取access_token（支持Mock模式）
            // 检查是否启用Mock模式
            boolean isMockSwitch = (jdConfig.getSync() != null && jdConfig.getSync().getIsMockSwitch() != null)
                ? jdConfig.getSync().getIsMockSwitch() : false;
            Map<String, Object> tokenResponse;

            if (isMockSwitch) {
                log.info("Mock模式已启用，构造假的token响应数据");
                // Mock模式：构造假的响应数据
                tokenResponse = createMockTokenResponse(request);
            } else {
                // 正常模式：调用真实的京东API
                String tokenUrl = String.format("%s?app_key=%s&app_secret=%s&grant_type=authorization_code&code=%s",
                        jdConfig.getTokenUrl(), jdConfig.getAppKey(), jdConfig.getAppSecret(), request.getCode());

                WebClient webClient = webClientBuilder.build();
                Mono<Map> responseMono = webClient.get()
                        .uri(tokenUrl)
                        .retrieve()
                        .bodyToMono(Map.class);

                tokenResponse = responseMono.block();
            }
            
            if (tokenResponse == null) {
                log.error("京东API返回空响应");
                return JdCallbackResult.failure("京东API返回空响应");
            }
            
            log.info("京东API响应: {}", tokenResponse);
            
            // 3. 解析响应数据
            String remoteATStr = (String) tokenResponse.get("access_token");
            Integer expiresIn = (Integer) tokenResponse.get("expires_in");
            String refreshToken = (String) tokenResponse.get("refresh_token");
            String scope = (String) tokenResponse.get("scope");
            String xid = (String) tokenResponse.get("xid");
            
            if (remoteATStr == null || xid == null) {
                log.error("京东API响应缺少必要字段");
                return JdCallbackResult.failure("京东API响应缺少必要字段");
            }
            
            // 4. 保存京东访问令牌到数据库
            //4.1 创建空的trainJdAccessToken
            TrainJdAccessToken remoteTrainJdATEntity = createAccessTokenEntity(request, remoteATStr, expiresIn, refreshToken, scope, xid);
            


            // 检查是否已存在相同xid的记录
            // TODO 这地方是重点：
            TrainJdAccessToken existingToken = trainJdAccessTokenMapper.findByXid(xid);
            boolean isJdUserExist = false;
            Long userId = null;

            if (existingToken != null) {
                // 更新现有记录
                trainJdAccessTokenMapper.updateByXid(remoteTrainJdATEntity);
                log.info("更新已存在的京东访问令牌，xid: {}", xid);
                isJdUserExist = true;
                userId = existingToken.getUserId(); // 获取现有记录的userId
            } else {
                // 插入新记录
                trainJdAccessTokenMapper.insert(remoteTrainJdATEntity);
                log.info("插入新的京东访问令牌，xid: {}", xid);
                isJdUserExist = false;
                // 新记录的userId为null，因为还没有用户关联
            }




            // 5. 启动异步商品同步任务
            try {
                log.info("开始异步同步京东商品数据，teamId: {}, remoteATStr: {}", request.getTeamId(), remoteATStr);
                jdNewSyncService.sync(xid, remoteATStr, request.getTeamId(), request.getUsername());
                log.info("异步商品同步任务进行中...");
            } catch (Exception e) {
                log.error("启动异步商品同步任务时发生异常", e);
                // 异步同步失败不影响主流程，继续执行
            }

            // 6. 返回成功结果
            return JdCallbackResult.success(true, true, remoteATStr, isJdUserExist, userId, xid);
            
        } catch (Exception e) {
            log.error("处理京东回调时发生异常", e);
            return JdCallbackResult.failure("处理京东回调时发生异常: " + e.getMessage());
        }
    }

    /**
     * 创建京东访问令牌实体对象
     *
     * @param request 回调请求
     * @param accessToken 访问令牌
     * @param expiresIn 过期时间（秒）
     * @param refreshToken 刷新令牌
     * @param scope 权限范围
     * @param xid 唯一标识
     * @return 京东访问令牌实体
     */
    private  TrainJdAccessToken  createAccessTokenEntity(JdCallbackRequest request, String accessToken,
                                                      Integer expiresIn, String refreshToken,
                                                      String scope, String xid) {
        // 常量定义
        final Long DEFAULT_SHOP_ID = -1L;
        final Integer AUTHORIZED_STATUS = 1;
        final Integer SYNC_NOT_STARTED = 0;
        final Long INITIAL_VERSION = 0L;

        LocalDateTime now = LocalDateTime.now();

        TrainJdAccessToken entity = new TrainJdAccessToken();
        entity.setUserId(request.getUserId());
        entity.setTeamId(request.getTeamId());
        entity.setAccessToken(accessToken);
        entity.setExpiresTime(now.plusSeconds(expiresIn != null ? expiresIn : 7200)); // 默认2小时
        entity.setRefreshToken(refreshToken);
        entity.setScope(scope);
        entity.setXid(xid);
        entity.setShopId(DEFAULT_SHOP_ID); // 默认店铺ID
        entity.setIsAuthorize(AUTHORIZED_STATUS); // 设置为已授权
        entity.setIsSyncComplete(SYNC_NOT_STARTED); // 设置为未开始同步（异步任务还未开始）
        entity.setCreator(request.getUsername());
        entity.setUpdater(request.getUsername());
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        entity.setVersion(INITIAL_VERSION);

        return entity;
    }

    /**
     * 创建Mock模式的token响应数据
     *
     * @param request 回调请求
     * @return Mock的token响应数据
     */
    private Map<String, Object> createMockTokenResponse(JdCallbackRequest request) {
        // 尝试从数据库获取现有的token数据作为Mock数据的基础
        String mockXid = "o*AASsPGRxBX6sjhq5v-zzb_U1YjI5ZfqeRIRBiYKyLkaMCc4Rqxs";
        TrainJdAccessToken existingToken = trainJdAccessTokenMapper.findByXid(mockXid);

        Map<String, Object> mockResponse = new java.util.HashMap<>();

        if (existingToken != null) {
            // 如果存在现有token，使用其数据
            log.info("使用现有token数据构造Mock响应，xid: {}", existingToken.getXid());
            mockResponse.put("access_token", existingToken.getAccessToken());
            mockResponse.put("expires_in", 7200); // 2小时
            mockResponse.put("refresh_token", existingToken.getRefreshToken());
            mockResponse.put("scope", existingToken.getScope());
            mockResponse.put("xid", existingToken.getXid());
        } else {
            // 如果不存在，创建新的Mock数据
            log.info("创建新的Mock响应数据，teamId: {}", request.getTeamId());
            mockResponse.put("access_token", "mock_access_token_" + System.currentTimeMillis());
            mockResponse.put("expires_in", 7200); // 2小时
            mockResponse.put("refresh_token", "mock_refresh_token_" + System.currentTimeMillis());
            mockResponse.put("scope", "read,write");
            mockResponse.put("xid", mockXid);
        }

        log.info("Mock响应数据构造完成: {}", mockResponse);
        return mockResponse;
    }

}
