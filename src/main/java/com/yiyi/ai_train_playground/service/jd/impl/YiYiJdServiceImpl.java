package com.yiyi.ai_train_playground.service.jd.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.domain.ware.WareReadService.response.searchWare4Valid.Ware;
import com.jd.open.api.sdk.domain.ware.SkuReadService.response.searchSkuList.Sku;
import com.jd.open.api.sdk.request.ware.WareReadSearchWare4ValidRequest;
import com.jd.open.api.sdk.request.ware.SkuReadSearchSkuListRequest;
import com.jd.open.api.sdk.response.ware.WareReadSearchWare4ValidResponse;
import com.jd.open.api.sdk.response.ware.SkuReadSearchSkuListResponse;
import com.yiyi.ai_train_playground.config.JdClientFactory;
import com.yiyi.ai_train_playground.entity.TrainJdProducts;
import com.yiyi.ai_train_playground.entity.TrainJdSku;
import com.yiyi.ai_train_playground.entity.TrainJdSkus;
import com.yiyi.ai_train_playground.service.jd.YiYiJdService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 易易京东服务实现类
 * 
 * <AUTHOR> Assistant
 * 加载数据有2种格式：
 * 1、一种是原生态的。比如:List<Ware>,这种需要后面再转
 * 2、一种是加工过的。比如List<TrainProducts>，这种先转了，后面就不用转了。这种更加清晰明了
 * </Ware>
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class YiYiJdServiceImpl implements YiYiJdService {
    
    private final JdClientFactory jdClientFactory;
    private final ObjectMapper objectMapper;
    
    @Override
    public List<TrainJdProducts> getWare4ValidProductList(String accessToken, Integer pageNo, Integer pageSize) {
        log.info("开始调用京东商品接口（动态accessToken），pageNo={}, pageSize={}", pageNo, pageSize);
        
        // 验证accessToken
        if (!jdClientFactory.isValidAccessToken(accessToken)) {
            log.warn("提供的accessToken无效，长度: {}", accessToken != null ? accessToken.length() : 0);
            return new ArrayList<>();
        }
        
        try {
            // 1. 使用工厂创建带有指定accessToken的JdClient
            JdClient jdClient = jdClientFactory.createClient(accessToken);
            
            // 2. 执行商品查询，获取Ware列表
            List<Ware> wareList = executeProductQuery(jdClient, pageNo, pageSize, accessToken);
            
            // 3. 转换为TrainJdProducts对象列表
            List<TrainJdProducts> products = new ArrayList<>();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            
            for (Ware ware : wareList) {
                //直接在源头就转了，不用到后面再费劲了
                TrainJdProducts product = convertWareToTrainJdProducts(ware, formatter);
                if (product != null) {
                    products.add(product);
                }
            }
            
            return products;
            
        } catch (Exception e) {
            log.error("调用京东商品接口失败（动态accessToken）", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<TrainJdProducts> getWare4ValidProductList(String accessToken) {
        return getWare4ValidProductList(accessToken, 1, 10);
    }
    
    @Override
    public List<TrainJdProducts> getWare4ValidProductList(Integer pageNo, Integer pageSize) {
        log.info("开始调用京东商品接口（默认accessToken），pageNo={}, pageSize={}", pageNo, pageSize);
        
        try {
            // 1. 使用工厂创建默认JdClient
            JdClient jdClient = jdClientFactory.createDefaultClient();
            
            // 2. 执行商品查询，获取Ware列表
            List<Ware> wareList = executeProductQuery(jdClient, pageNo, pageSize, "默认配置");
            
            // 3. 转换为TrainJdProducts对象列表
            List<TrainJdProducts> products = new ArrayList<>();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            
            for (Ware ware : wareList) {
                TrainJdProducts product = convertWareToTrainJdProducts(ware, formatter);
                if (product != null) {
                    products.add(product);
                }
            }
            
            return products;
            
        } catch (Exception e) {
            log.error("调用京东商品接口失败（默认accessToken）", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<TrainJdProducts> getWare4ValidProductList() {
        return getWare4ValidProductList(1, 10);
    }
    
    @Override
    public List<Sku> getSkuList(String accessToken, Long wareId, Integer page, Integer pageSize) {
        log.info("开始调用京东SKU接口，wareId={}, page={}, pageSize={}", wareId, page, pageSize);
        
        // 验证accessToken
        if (!jdClientFactory.isValidAccessToken(accessToken)) {
            log.warn("提供的accessToken无效，长度: {}", accessToken != null ? accessToken.length() : 0);
            return new ArrayList<>();
        }
        
        // 验证wareId
        if (wareId == null) {
            log.warn("wareId不能为空");
            return new ArrayList<>();
        }
        
        try {
            // 1. 使用工厂创建带有指定accessToken的JdClient
            JdClient jdClient = jdClientFactory.createClient(accessToken);
            
            // 2. 创建请求对象
            SkuReadSearchSkuListRequest request = new SkuReadSearchSkuListRequest();
            
            // 3. 设置请求参数
            request.setWareId(wareId.toString());
            request.setSkuStatuValue("1"); // SKU状态：1-有效
            request.setField("barCode,categoryId,created,enable,jdPrice,logo,modified,skuId,skuName,status,stockNum,wareId,wareTitle,saleAttrs,features,multiCateProps,props");
            request.setPageNo(page != null ? page : 1);
            request.setPageSize(pageSize != null ? pageSize : 50);
            
            log.info("京东SKU API请求参数：wareId={}, pageNo={}, pageSize={}", 
                    request.getWareId(), request.getPageNo(), request.getPageSize());
            
            // 4. 执行请求
            SkuReadSearchSkuListResponse response = jdClient.execute(request);
            
            if (response == null || response.getPage() == null) {
                log.warn("京东SKU API返回为空");
                return new ArrayList<>();
            }
            
            // 5. 获取SKU列表
            List<Sku> originalSkuList = response.getPage().getData();
            
            if (originalSkuList == null || originalSkuList.isEmpty()) {
                log.info("京东SKU API返回空的SKU列表");
                return new ArrayList<>();
            }
            
            log.info("京东SKU接口调用成功，获取到 {} 个SKU", originalSkuList.size());
            return originalSkuList;
            
        } catch (Exception e) {
            log.error("调用京东SKU接口失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<TrainJdSku> getTrainJdSkuList(String accessToken, Long wareId, Integer page, Integer pageSize) {
        log.info("开始调用京东SKU接口并转换为TrainJdSku，wareId={}, page={}, pageSize={}", wareId, page, pageSize);

        // 验证accessToken
        if (!jdClientFactory.isValidAccessToken(accessToken)) {
            log.warn("提供的accessToken无效，长度: {}", accessToken != null ? accessToken.length() : 0);
            return new ArrayList<>();
        }

        // 验证wareId
        if (wareId == null) {
            log.warn("wareId不能为空");
            return new ArrayList<>();
        }

        try {
            // 1. 使用工厂创建带有指定accessToken的JdClient
            JdClient jdClient = jdClientFactory.createClient(accessToken);

            // 2. 创建请求对象
            SkuReadSearchSkuListRequest request = new SkuReadSearchSkuListRequest();

            // 3. 设置请求参数
            request.setWareId(wareId.toString());
            request.setSkuStatuValue("1"); // SKU状态：1-有效
            request.setField("barCode,categoryId,created,enable,jdPrice,logo,modified,skuId,skuName,status,stockNum,wareId,wareTitle,saleAttrs,features,multiCateProps,props");
            request.setPageNo(page != null ? page : 1);
            request.setPageSize(pageSize != null ? pageSize : 50);

            log.info("京东SKU API请求参数：wareId={}, pageNo={}, pageSize={}",
                    request.getWareId(), request.getPageNo(), request.getPageSize());

            // 4. 执行请求
            SkuReadSearchSkuListResponse response = jdClient.execute(request);

            if (response == null || response.getPage() == null) {
                log.warn("京东SKU API返回为空");
                return new ArrayList<>();
            }

            // 5. 获取SKU列表
            List<Sku> originalSkuList = response.getPage().getData();

            if (originalSkuList == null || originalSkuList.isEmpty()) {
                log.info("京东SKU API返回空的SKU列表");
                return new ArrayList<>();
            }

            // 6. 转换为TrainJdSku对象列表
            List<TrainJdSku> trainJdSkuList = new ArrayList<>();

            for (Sku sku : originalSkuList) {
                TrainJdSku trainJdSku = convertSkuToTrainJdSku(sku);
                if (trainJdSku != null) {
                    trainJdSkuList.add(trainJdSku);
                }
            }

            log.info("京东SKU接口调用成功，获取到 {} 个SKU，转换为 {} 个TrainJdSku",
                    originalSkuList.size(), trainJdSkuList.size());
            return trainJdSkuList;

        } catch (Exception e) {
            log.error("调用京东SKU接口并转换为TrainJdSku失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Ware> getWareList(String accessToken, Integer pageNo, Integer pageSize) {
        log.info("开始调用京东商品接口获取原始Ware列表，pageNo={}, pageSize={}", pageNo, pageSize);
        
        // 验证accessToken
        if (!jdClientFactory.isValidAccessToken(accessToken)) {
            log.warn("提供的accessToken无效，长度: {}", accessToken != null ? accessToken.length() : 0);
            return new ArrayList<>();
        }
        
        try {
            // 1. 使用工厂创建带有指定accessToken的JdClient
            JdClient jdClient = jdClientFactory.createClient(accessToken);
            
            // 2. 执行商品查询，直接返回Ware列表
            return executeProductQuery(jdClient, pageNo, pageSize, accessToken);
            
        } catch (Exception e) {
            log.error("调用京东商品接口获取Ware列表失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 执行商品查询的核心逻辑
     * 
     * @param jdClient JdClient实例
     * @param pageNo 页码
     * @param pageSize 页大小
     * @param tokenInfo 令牌信息（用于日志）
     * @return 商品列表
     */
    private List<Ware> executeProductQuery(JdClient jdClient, Integer pageNo, Integer pageSize, String tokenInfo) {
        try {
            // 1. 创建请求对象
            WareReadSearchWare4ValidRequest request = new WareReadSearchWare4ValidRequest();
            
            // 2. 设置查询字段（参考测试方法中的字段列表）
            request.setField("brandId,brandName,categoryId,categorySecId,colType,costPrice,created,height,jdPrice,length,logo,marketPrice,modified,offlineTime,onlineTime,shopId,spuId,stockNum,templateId,title,wareId,features,multiCateProps,wareStatus,weight,width,wrap,wareLocation,introduction,mobileDesc,fitCaseHtmlApp");
            
            // 3. 设置分页参数
            request.setPageNo(pageNo != null ? pageNo : 1);
            request.setPageSize(pageSize != null ? pageSize : 10);
            
            log.info("京东API请求参数：pageNo={}, pageSize={}, accessToken={}", 
                    request.getPageNo(), request.getPageSize(), tokenInfo);
            
            // 4. 执行请求
            WareReadSearchWare4ValidResponse response = jdClient.execute(request);
            
            if (response == null || response.getPage() == null) {
                log.warn("京东API返回为空");
                return new ArrayList<>();
            }
            
            // 5. 直接从响应中获取数据
            List<Ware> wareList = response.getPage().getData();
            
            if (wareList == null || wareList.isEmpty()) {
                log.info("京东API返回空的商品列表");
                return new ArrayList<>();
            }
            
            log.info("京东商品接口调用成功，获取到 {} 个商品", wareList.size());
            return wareList;
            
        } catch (Exception e) {
            log.error("执行商品查询失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 将Ware对象转换为TrainJdProducts对象
     * 
     * @param ware 京东商品对象
     * @param formatter 日期格式化器
     * @return TrainJdProducts对象
     */
    public TrainJdProducts convertWareToTrainJdProducts(Ware ware, DateTimeFormatter formatter) {
        try {
            TrainJdProducts product = new TrainJdProducts();
            
            // 设置业务字段
            product.setBrandId(ware.getBrandId());
            product.setBrandName(ware.getBrandName());
            product.setCategoryId(ware.getCategoryId());
            product.setCategorySecId(ware.getCategorySecId());
            product.setColType(ware.getColType());
            product.setCostPrice(ware.getCostPrice());
            product.setCreated(parseDateTime(ware.getCreated(), formatter));
            product.setHeight(convertToBigDecimal(ware.getHeight()));
            product.setJdPrice(convertToBigDecimal(ware.getJdPrice()));
            product.setLength(convertToBigDecimal(ware.getLength()));
            product.setLogo(ware.getLogo());
            product.setMarketPrice(convertToBigDecimal(ware.getMarketPrice()));
            product.setModified(parseDateTime(ware.getModified(), formatter));
            product.setOfflineTime(parseDateTime(ware.getOfflineTime(), formatter));
            product.setOnlineTime(parseDateTime(ware.getOnlineTime(), formatter));
            product.setShopId(ware.getShopId());
            product.setSpuId(ware.getSpuId());
            product.setStockNum(convertToInteger(ware.getStockNum()));
            product.setTemplateId(ware.getTemplateId());
            product.setTitle(ware.getTitle());
            product.setWareId(ware.getWareId());
            product.setWareStatus(convertToInteger(ware.getWareStatus()));
            product.setWeight(convertToBigDecimal(ware.getWeight()));
            product.setWidth(convertToBigDecimal(ware.getWidth()));
            product.setWrap(ware.getWrap());
            product.setWareLocation(ware.getWareLocation());
            product.setIntroduction(ware.getIntroduction());
            product.setMobileDesc(ware.getMobileDesc());
            product.setFitCaseHtmlApp(ware.getFitCaseHtmlApp());

            // 转换features和multiCateProps为JSON字符串
            product.setFeatures(convertWareFeaturesToJson(ware.getFeatures()));
            product.setMultiCateProps(convertWareMultiCatePropsToJson(ware.getMultiCateProps()));
            
            return product;
            
        } catch (Exception e) {
            log.error("转换Ware对象时出错: {}", e.getMessage());
            return null;
        }
    }



    /**
     * 将Ware的multiCateProps字段转换为JSON字符串
     *
     * @param multiCateProps Ware对象的multiCateProps字段
     * @return JSON字符串
     */
    private String convertWareMultiCatePropsToJson(Object multiCateProps) {
        if (multiCateProps == null) {
            return null;
        }

        try {
            if (multiCateProps instanceof java.util.Set) {
                @SuppressWarnings("unchecked")
                java.util.Set<Object> mcpSet = (java.util.Set<Object>) multiCateProps;
                List<Map<String, Object>> jsonList = new ArrayList<>();

                for (Object item : mcpSet) {
                    Map<String, Object> mcpMap = new HashMap<>();
                    mcpMap.put("attrId", getFieldAsString(item, "attrId"));
                    mcpMap.put("attrValues", getFieldValue(item, "attrValues"));
                    mcpMap.put("attrValueAlias", getFieldValue(item, "attrValueAlias"));
                    mcpMap.put("expands", getFieldValue(item, "expands"));
                    mcpMap.put("units", getFieldAsString(item, "units"));
                    jsonList.add(mcpMap);
                }

                return objectMapper.writeValueAsString(jsonList);
            }
        } catch (Exception e) {
            log.error("转换multiCateProps为JSON时出错: {}", e.getMessage());
        }

        return null;
    }


    private String convertWareFeaturesToJson(Object features) {
        if (features == null) {
            return null;
        }

        try {
            if (features instanceof java.util.Set) {
                @SuppressWarnings("unchecked")
                java.util.Set<Object> featureSet = (java.util.Set<Object>) features;
                List<Map<String, Object>> jsonList = new ArrayList<>();

                for (Object item : featureSet) {
                    Map<String, Object> featureMap = new HashMap<>();
                    featureMap.put("featureKey", getFieldAsString(item, "featureKey"));
                    featureMap.put("featureValue", getFieldValue(item, "featureValue"));
                    featureMap.put("featureCn", getFieldAsString(item, "featureCn"));
                    jsonList.add(featureMap);
                }

                return objectMapper.writeValueAsString(jsonList);
            }
        } catch (Exception e) {
            log.error("转换features为JSON时出错: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 从对象中获取字段值并转换为字符串
     */
    private String getFieldAsString(Object obj, String fieldName) {
        Object value = getFieldValue(obj, fieldName);
        return value != null ? value.toString() : null;
    }

    /**
     * 从对象中获取字段值
     */
    private Object getFieldValue(Object obj, String fieldName) {
        if (obj == null) {
            return null;
        }

        try {
            java.lang.reflect.Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(obj);
        } catch (Exception e) {
            log.debug("无法获取字段 {} 的值: {}", fieldName, e.getMessage());
            return null;
        }
    }

    /**
     * 将Sku对象转换为TrainJdSku对象
     * 参考JdProductSyncServiceImpl.convertSkuToTrainJdSku方法
     *
     * @param sku 京东SKU对象
     * @return TrainJdSku对象
     */
    private TrainJdSku convertSkuToTrainJdSku(Sku sku) {
        try {
            TrainJdSku trainJdSku = new TrainJdSku();

            // 设置基本字段
            trainJdSku.setBarCode(sku.getBarCode());
            trainJdSku.setCategoryId(sku.getCategoryId() != null ? Integer.valueOf(sku.getCategoryId().intValue()) : null);
            trainJdSku.setCreated(convertDateToLocalDateTime(sku.getCreated()));
            trainJdSku.setEnable(sku.getEnable());
            trainJdSku.setJdPrice(sku.getJdPrice() != null ? new java.math.BigDecimal(sku.getJdPrice().toString()) : null);
            trainJdSku.setLogo(sku.getLogo());
            trainJdSku.setModified(convertDateToLocalDateTime(sku.getModified()));
            trainJdSku.setSkuId(sku.getSkuId());
            trainJdSku.setSkuName(sku.getSkuName());
            trainJdSku.setStatus(sku.getStatus());
            trainJdSku.setStockNum(sku.getStockNum());
            trainJdSku.setWareId(sku.getWareId());
            trainJdSku.setWareTitle(sku.getWareTitle());

            return trainJdSku;

        } catch (Exception e) {
            log.error("转换Sku对象为TrainJdSku时出错: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 将java.util.Date转换为LocalDateTime
     */
    private LocalDateTime convertDateToLocalDateTime(java.util.Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant()
                .atZone(java.time.ZoneId.systemDefault())
                .toLocalDateTime();
    }

    /**
     * 将Sku对象转换为TrainJdSkus对象
     *
     * @param sku 京东SKU对象
     * @param formatter 日期格式化器
     * @return TrainJdSkus对象
     */
    private TrainJdSkus convertSkuToTrainJdSkus(Sku sku, DateTimeFormatter formatter) {
        try {
            TrainJdSkus trainJdSku = new TrainJdSkus();
            
            // 设置SKU字段
            trainJdSku.setSkuId(sku.getSkuId());
            trainJdSku.setWareId(sku.getWareId());
            trainJdSku.setSkuName(sku.getSkuName());
            trainJdSku.setWareTitle(sku.getWareTitle());
            trainJdSku.setBarCode(sku.getBarCode());
            trainJdSku.setCategoryId(sku.getCategoryId());
            trainJdSku.setJdPrice(convertToDouble(sku.getJdPrice()));
            trainJdSku.setLogo(sku.getLogo());
            trainJdSku.setStatus(convertToInteger(sku.getStatus()));
            trainJdSku.setEnable(convertToInteger(sku.getEnable()));
            trainJdSku.setStockNum(sku.getStockNum());
            trainJdSku.setCreated(parseDateTime(sku.getCreated(), formatter));
            trainJdSku.setModified(parseDateTime(sku.getModified(), formatter));
            
            return trainJdSku;
            
        } catch (Exception e) {
            log.error("转换Sku对象时出错: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析日期时间字符串
     * 
     * @param dateTimeStr 日期时间字符串
     * @param formatter 格式化器
     * @return LocalDateTime对象
     */
    private LocalDateTime parseDateTime(String dateTimeStr, DateTimeFormatter formatter) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateTimeStr, formatter);
        } catch (Exception e) {
            log.debug("日期解析失败: {}", dateTimeStr);
            return null;
        }
    }
    
    /**
     * 解析日期时间字符串（从Date对象）
     * 
     * @param date 日期对象
     * @param formatter 格式化器
     * @return LocalDateTime对象
     */
    private LocalDateTime parseDateTime(java.util.Date date, DateTimeFormatter formatter) {
        if (date == null) {
            return null;
        }
        try {
            return date.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
        } catch (Exception e) {
            log.debug("日期解析失败: {}", date);
            return null;
        }
    }
    
    /**
     * 转换为BigDecimal
     * 
     * @param value 数值对象
     * @return BigDecimal对象
     */
    private BigDecimal convertToBigDecimal(Object value) {
        if (value == null) {
            return null;
        }
        try {
            if (value instanceof BigDecimal) {
                return (BigDecimal) value;
            } else if (value instanceof Number) {
                return new BigDecimal(value.toString());
            } else {
                return new BigDecimal(value.toString());
            }
        } catch (Exception e) {
            log.debug("BigDecimal转换失败: {}", value);
            return null;
        }
    }
    
    /**
     * 转换为Integer
     * 
     * @param value 数值对象
     * @return Integer对象
     */
    private Integer convertToInteger(Object value) {
        if (value == null) {
            return null;
        }
        try {
            if (value instanceof Integer) {
                return (Integer) value;
            } else if (value instanceof Number) {
                return ((Number) value).intValue();
            } else {
                return Integer.parseInt(value.toString());
            }
        } catch (Exception e) {
            log.debug("Integer转换失败: {}", value);
            return null;
        }
    }
    
    /**
     * 转换为Double
     * 
     * @param value 数值对象
     * @return Double对象
     */
    private Double convertToDouble(Object value) {
        if (value == null) {
            return null;
        }
        try {
            if (value instanceof Double) {
                return (Double) value;
            } else if (value instanceof Number) {
                return ((Number) value).doubleValue();
            } else {
                return Double.parseDouble(value.toString());
            }
        } catch (Exception e) {
            log.debug("Double转换失败: {}", value);
            return null;
        }
    }
} 