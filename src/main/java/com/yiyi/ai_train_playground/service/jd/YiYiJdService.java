package com.yiyi.ai_train_playground.service.jd;

import com.jd.open.api.sdk.domain.ware.SkuReadService.response.searchSkuList.Sku;
import com.jd.open.api.sdk.domain.ware.WareReadService.response.searchWare4Valid.Ware;
import com.yiyi.ai_train_playground.entity.TrainJdProducts;
import com.yiyi.ai_train_playground.entity.TrainJdSku;
import java.util.List;

/**
 * 易易京东服务接口
 * 专门负责调用京东的商品获取方法
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
public interface YiYiJdService {
    
    /**
     * 获取有效商品列表（使用动态accessToken）
     * 调用京东的 jingdong.ware.read.searchWare4Valid 接口
     * 
     * @param accessToken 动态的访问令牌
     * @param pageNo 页码，从1开始
     * @param pageSize 每页数量，默认10，最大30
     * @return 京东商品列表
     */
    List<TrainJdProducts> getWare4ValidProductList(String accessToken, Integer pageNo, Integer pageSize);
    
    /**
     * 获取有效商品列表（使用动态accessToken和默认分页参数）
     * 
     * @param accessToken 动态的访问令牌
     * @return 京东商品列表
     */
    List<TrainJdProducts> getWare4ValidProductList(String accessToken);
    
    /**
     * 获取有效商品列表（使用配置文件中的默认accessToken）
     * 调用京东的 jingdong.ware.read.searchWare4Valid 接口
     * 
     * @param pageNo 页码，从1开始
     * @param pageSize 每页数量，默认10，最大30
     * @return 京东商品列表
     */
    List<TrainJdProducts> getWare4ValidProductList(Integer pageNo, Integer pageSize);
    
    /**
     * 获取有效商品列表（使用配置文件中的默认accessToken和默认分页参数）
     * 
     * @return 京东商品列表
     */
    List<TrainJdProducts> getWare4ValidProductList();
    
    /**
     * 获取指定商品的SKU列表
     * 调用京东的 jingdong.sku.read.searchSkuList 接口
     *
     * @param accessToken 访问令牌
     * @param wareId 商品ID
     * @param page 页码，从1开始
     * @param pageSize 每页数量
     * @return SKU列表
     */
    List<Sku> getSkuList(String accessToken, Long wareId, Integer page, Integer pageSize);

    /**
     * 获取指定商品的SKU列表并转换为TrainJdSku对象
     * 调用京东的 jingdong.sku.read.searchSkuList 接口
     *
     * @param accessToken 访问令牌
     * @param wareId 商品ID
     * @param page 页码，从1开始
     * @param pageSize 每页数量
     * @return TrainJdSku列表
     */
    List<TrainJdSku> getTrainJdSkuList(String accessToken, Long wareId, Integer page, Integer pageSize);
    
    /**
     * 获取有效商品原始Ware列表（使用动态accessToken）
     * 调用京东的 jingdong.ware.read.searchWare4Valid 接口
     * 
     * @param accessToken 动态的访问令牌
     * @param pageNo 页码，从1开始
     * @param pageSize 每页数量，默认10，最大30
     * @return 京东原始Ware列表
     */
    List<Ware> getWareList(String accessToken, Integer pageNo, Integer pageSize);
} 