package com.yiyi.ai_train_playground.service.jd;

import com.jd.open.api.sdk.domain.ware.SkuReadService.response.searchSkuList.Sku;
import java.util.List;

/**
 * 从JSON文件读取并转换为Sku的服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
public interface GetSkuFromJsonService {
    
    /**
     * 从指定的JSON文件读取数据并转换为Sku列表
     *
     * @return Sku列表
     */
    List<Sku> getSkuListFromJson();

    /**
     * 从指定的JSON文件读取数据并根据wareId筛选转换为Sku列表
     *
     * @param wareId 商品ID，用于筛选SKU
     * @return 筛选后的Sku列表
     */
    List<Sku> getSkuListFromJson(Long wareId);
}
