package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.service.ConverterServiceByLLM;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 京东商品同步服务实现类
 * 仅保留图片转换相关方法，供 JdImg2VecScheduler 使用
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JdProductSyncServiceUtil {
    
    private final ConverterServiceByLLM converterService;

    /**
     * 供 JdImg2VecScheduler 调用的公共方法
     * 避免反射调用导致的代理问题
     *
     * @param imageUrl 图像URL
     * @return LLM转换服务的返回结果
     */
    public String convertImageToMarkdown(String imageUrl) {
        return getImgMd(imageUrl);
    }

    /**
     * 调用LLM转换服务进行图像到Markdown转换的私有方法
     *
     * @param imageUrl 图像URL
     * @return LLM转换服务的返回结果
     */
    private String getImgMd(String imageUrl) {
        try {
            log.info("开始调用LLM转换服务进行图像转MD处理，图像URL: {}", imageUrl);

            // 检查 converterService 是否为 null
            if (converterService == null) {
                log.error("ConverterServiceByLLM 服务未正确注入，converterService 为 null");
                throw new RuntimeException("ConverterServiceByLLM 服务未正确注入");
            }

            // 记录服务实例信息
            log.debug("ConverterServiceByLLM 实例信息: class={}, hashCode={}",
                    converterService.getClass().getName(), converterService.hashCode());

            // 调用LLM转换服务 - 使用正确的方法名
            String prmtTemplateKW = "img_md_converter:SU";
            String systemPrompt = "你是一个专业的图像识别助手，请将图像内容转换为详细的Markdown格式描述。";
            String userPrompt = "请识别并描述这张图片的内容，转换为Markdown格式。";
            String result = converterService.convertImg(prmtTemplateKW, systemPrompt, userPrompt, imageUrl);

            log.info("LLM转换服务调用完成，图像URL: {}, 返回结果长度: {}",
                    imageUrl, result != null ? result.length() : 0);

            return result;

        } catch (Exception e) {
            log.error("调用LLM转换服务进行图像转MD处理时发生异常，图像URL: {}", imageUrl, e);
            throw new RuntimeException("图像转MD处理失败: " + e.getMessage(), e);
        }
    }

}
