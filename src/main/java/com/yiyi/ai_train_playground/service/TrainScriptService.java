package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.ScriptCreateRequest;
import com.yiyi.ai_train_playground.dto.ScriptDetailDTO;
import com.yiyi.ai_train_playground.dto.ScriptListDTO;
import com.yiyi.ai_train_playground.dto.ScriptQueryRequest;
import com.yiyi.ai_train_playground.dto.ScriptUpdateRequest;
import com.yiyi.ai_train_playground.entity.TrainScript;

/**
 * 剧本服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-02
 */
public interface TrainScriptService {
    
    /**
     * 分页查询剧本列表
     * 
     * @param request 查询请求参数
     * @param teamId 团队ID
     * @return 分页结果
     */
    PageResult<ScriptListDTO> getScriptList(ScriptQueryRequest request, Long teamId);
    
    /**
     * 根据ID查询剧本详情
     * 
     * @param id 剧本ID
     * @param teamId 团队ID
     * @return 剧本详情
     */
    TrainScript getScriptById(Long id, Long teamId);
    
    /**
     * 创建剧本
     * 
     * @param script 剧本信息
     * @return 是否成功
     */
    boolean createScript(TrainScript script);
    
    /**
     * 更新剧本
     *
     * @param script 剧本信息
     * @return 是否成功
     */
    boolean updateScript(TrainScript script);

    /**
     * 更新剧本及相关数据
     *
     * @param request 剧本更新请求
     * @param teamId 团队ID
     * @param updater 更新者
     * @return 是否成功
     */
    boolean updateScriptWithRelatedData(ScriptUpdateRequest request, Long teamId, String updater);
    
    /**
     * 删除剧本
     *
     * @param id 剧本ID
     * @param teamId 团队ID
     * @return 是否成功
     */
    boolean deleteScript(Long id, Long teamId);

    /**
     * 批量删除剧本
     *
     * @param ids 剧本ID列表
     * @param teamId 团队ID
     * @return 是否成功
     */
    boolean batchDeleteScripts(String ids, Long teamId);

    /**
     * 根据ID查询剧本详情
     *
     * @param id 剧本ID
     * @param teamId 团队ID
     * @return 剧本详情
     */
    ScriptDetailDTO getScriptDetail(Long id, Long teamId);

    /**
     * 创建剧本及关联数据
     *
     * @param request 剧本创建请求
     * @param teamId 团队ID
     * @param creator 创建人
     * @return 创建的剧本ID
     */
    Long createScriptWithRelatedData(ScriptCreateRequest request, Long teamId, String creator);
}
