package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.entity.EvaluationGroup;
import java.util.List;
import java.util.Map;

public interface EvaluationGroupService {
    Map<String, Object> getGroupTree(String groupTitle, Long teamId);
    
    boolean save(EvaluationGroup evaluationGroup);
    
    boolean update(EvaluationGroup evaluationGroup);
    
    boolean deleteByIds(String ids, Long teamId);
    
    List<EvaluationGroup> getEvaluationGroups(String groupTitle, Long teamId);
}
