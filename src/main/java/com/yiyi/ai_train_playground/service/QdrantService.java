package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.model.VectorData;
import com.yiyi.ai_train_playground.model.VectorSearchRequest;
import com.yiyi.ai_train_playground.model.VectorSearchResult;

import java.util.List;
import java.util.Map;

/**
 * Qdrant向量数据库服务接口
 */
public interface QdrantService {
    
    /**
     * 创建集合
     * @param collectionName 集合名称
     * @param vectorSize 向量维度
     * @param distance 距离度量方式
     * @return 是否成功
     */
    boolean createCollection(String collectionName, int vectorSize, String distance);
    
    /**
     * 删除集合
     * @param collectionName 集合名称
     * @return 是否成功
     */
    boolean deleteCollection(String collectionName);
    
    /**
     * 检查集合是否存在
     * @param collectionName 集合名称
     * @return 是否存在
     */
    boolean collectionExists(String collectionName);
    
    /**
     * 获取集合信息
     * @param collectionName 集合名称
     * @return 集合信息
     */
    Map<String, Object> getCollectionInfo(String collectionName);
    
    /**
     * 插入单个向量
     * @param collectionName 集合名称
     * @param vectorData 向量数据
     * @return 是否成功
     */
    boolean insertVector(String collectionName, VectorData vectorData);
    
    /**
     * 批量插入向量
     * @param collectionName 集合名称
     * @param vectorDataList 向量数据列表
     * @return 是否成功
     */
    boolean insertVectors(String collectionName, List<VectorData> vectorDataList);
    
    /**
     * 搜索向量
     * @param request 搜索请求
     * @return 搜索结果
     */
    VectorSearchResult searchVectors(VectorSearchRequest request);
    
    /**
     * 根据ID获取向量
     * @param collectionName 集合名称
     * @param id 向量ID
     * @return 向量数据
     */
    VectorData getVectorById(String collectionName, String id);
    
    /**
     * 根据ID删除向量
     * @param collectionName 集合名称
     * @param id 向量ID
     * @return 是否成功
     */
    boolean deleteVector(String collectionName, String id);
    
    /**
     * 批量删除向量
     * @param collectionName 集合名称
     * @param ids 向量ID列表
     * @return 是否成功
     */
    boolean deleteVectors(String collectionName, List<String> ids);
    
    /**
     * 更新向量载荷
     * @param collectionName 集合名称
     * @param id 向量ID
     * @param payload 新的载荷数据
     * @return 是否成功
     */
    boolean updatePayload(String collectionName, String id, Map<String, Object> payload);
    
    /**
     * 获取集合统计信息
     * @param collectionName 集合名称
     * @return 统计信息
     */
    Map<String, Object> getCollectionStats(String collectionName);
    
    /**
     * 根据payload条件查询向量ID列表
     * @param collectionName 集合名称
     * @param payloadConditions payload查询条件
     * @return 匹配的向量ID列表
     */
    List<String> getVectorIdsByPayload(String collectionName, Map<String, Object> payloadConditions);
} 