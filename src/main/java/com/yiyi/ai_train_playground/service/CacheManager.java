package com.yiyi.ai_train_playground.service;

import java.time.Duration;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 缓存管理器接口
 * 抽象缓存操作，支持Redis等具体实现
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-13
 */
public interface CacheManager {
    
    // ==================== Value 操作 ====================
    
    /**
     * 存储对象值
     * 
     * @param key 缓存键
     * @param value 缓存值
     */
    void put(String key, Object value);
    
    /**
     * 存储对象值（带过期时间）
     * 
     * @param key 缓存键
     * @param value 缓存值
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    void put(String key, Object value, long timeout, TimeUnit unit);
    
    /**
     * 存储对象值（带过期时间）
     * 
     * @param key 缓存键
     * @param value 缓存值
     * @param duration 过期时间
     */
    void put(String key, Object value, Duration duration);
    
    /**
     * 获取对象值
     * 
     * @param key 缓存键
     * @return 缓存值，可能为null
     */
    Object get(String key);
    
    /**
     * 获取指定类型的对象值
     * 
     * @param <T> 返回类型
     * @param key 缓存键
     * @param type 期望的返回类型
     * @return 缓存值，如果不存在或类型不匹配则为空
     */
    <T> Optional<T> get(String key, Class<T> type);
    
    // ==================== Hash 操作 ====================
    
    /**
     * 批量存储Hash字段
     * 
     * @param key Hash键
     * @param hash 字段映射
     */
    void putHash(String key, Map<String, Object> hash);
    
    /**
     * 批量存储Hash字段（带过期时间）
     * 
     * @param key Hash键
     * @param hash 字段映射
     * @param timeout 过期时间
     * @param unit 时间单位
     */
    void putHash(String key, Map<String, Object> hash, long timeout, TimeUnit unit);
    
    /**
     * 获取Hash所有字段
     * 
     * @param key Hash键
     * @return 字段映射，如果不存在则为空Map
     */
    Map<Object, Object> getHash(String key);
    
    /**
     * 获取Hash单个字段
     * 
     * @param key Hash键
     * @param hashKey 字段键
     * @return 字段值，可能为null
     */
    Object getHashField(String key, String hashKey);
    
    // ==================== Key 操作 ====================
    
    /**
     * 检查键是否存在
     * 
     * @param key 缓存键
     * @return 是否存在
     */
    boolean exists(String key);
    
    /**
     * 删除键
     * 
     * @param key 缓存键
     * @return 是否删除成功
     */
    boolean delete(String key);
    
    /**
     * 设置键的过期时间
     * 
     * @param key 缓存键
     * @param timeout 过期时间
     * @param unit 时间单位
     * @return 是否设置成功
     */
    boolean expire(String key, long timeout, TimeUnit unit);
    
    /**
     * 设置键的过期时间
     * 
     * @param key 缓存键
     * @param duration 过期时间
     * @return 是否设置成功
     */
    boolean expire(String key, Duration duration);
    
    /**
     * 获取键的剩余生存时间
     * 
     * @param key 缓存键
     * @return 剩余生存时间（秒），-1表示永不过期，-2表示键不存在
     */
    long getExpire(String key);
}