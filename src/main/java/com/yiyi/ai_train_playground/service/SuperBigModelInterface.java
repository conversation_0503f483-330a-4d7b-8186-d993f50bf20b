package com.yiyi.ai_train_playground.service;

import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import reactor.core.publisher.Flux;

import java.util.List;

public interface SuperBigModelInterface {

    /**
     * Normal模型非流式对话接口
     * @param messages 完整的对话消息列表
     * @return 大模型回复
     */
    String ntns(List<ChatMessage> messages);
    
    /**
     * Thinking模型非流式对话接口
     * @param messages 完整的对话消息列表
     * @return 大模型回复
     */
    String tns(List<ChatMessage> messages);
    
    /**
     * Normal模型流式对话接口
     * @param messages 完整的对话消息列表
     * @return 大模型回复流
     */
    Flux<String> nts(List<ChatMessage> messages);
    
    /**
     * Thinking模型流式对话接口
     * @param messages 完整的对话消息列表
     * @return 大模型回复流
     */
    Flux<String> ts(List<ChatMessage> messages);
    
    /**
     * Thinking模型一次性流式对话接口（无会话管理）
     * @param systemPrompt 系统提示词
     * @param userPrompt 用户输入
     * @return 大模型回复流
     */
    Flux<String> tsOnce(String systemPrompt, String userPrompt);
    
    /**
     * Thinking模型一次性非流式对话接口（无会话管理）
     * @param systemPrompt 系统提示词
     * @param userPrompt 用户输入
     * @return 大模型回复
     */
    String tnsOnce(String systemPrompt, String userPrompt);
    
    /**
     * Normal模型一次性非流式对话接口（无会话管理）
     * @param systemPrompt 系统提示词
     * @param userPrompt 用户输入
     * @return 大模型回复
     */
    String ntnsOnce(String systemPrompt, String userPrompt);
    
    /**
     * Normal模型一次性流式对话接口（无会话管理）
     * @param systemPrompt 系统提示词
     * @param userPrompt 用户输入
     * @return 大模型回复流
     */
    Flux<String> ntsOnce(String systemPrompt, String userPrompt);
    
    /**
     * 图像对话接口（流式）
     * @param imageUrl 图像URL
     * @param prompt 用户输入
     * @param systemPrompt 系统提示词
     * @return 大模型回复流
     */
    Flux<String> imageChatWithStream(String imageUrl, String prompt, String systemPrompt);

    /**
     * 图像对话接口（非流式）
     * @param imageUrl 图像URL
     * @param prompt 用户输入
     * @param systemPrompt 系统提示词
     * @return 大模型回复
     */
    String imageChat(String imageUrl, String prompt, String systemPrompt);

    /**
     * 文本向量化接口
     * @param texts 待向量化的文本列表
     * @return 向量化结果，每个文本对应一个向量
     */
    List<List<Double>> embed(List<String> texts);

    /**
     * Normal模型带上下文的非流式对话接口
     * @param messages 完整的对话消息列表
     * @param contextId 上下文ID
     * @return 大模型回复
     */
    String ntnsWithCtx(List<ChatMessage> messages, String contextId);
}