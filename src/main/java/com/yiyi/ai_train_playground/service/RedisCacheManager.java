package com.yiyi.ai_train_playground.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * Redis缓存管理器实现类
 * 基于RedisTemplate提供具体的缓存操作实现
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-13
 */
@Slf4j
@Service
public class RedisCacheManager implements CacheManager {
    
    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;
    
    @Autowired
    public RedisCacheManager(RedisTemplate<String, Object> redisTemplate, 
                           ObjectMapper objectMapper) {
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
    }
    
    // ==================== Value 操作 ====================
    
    @Override
    public void put(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            log.debug("成功存储缓存: key={}", key);
        } catch (Exception e) {
            log.error("存储缓存失败: key={}", key, e);
            throw new RuntimeException("存储缓存失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public void put(String key, Object value, long timeout, TimeUnit unit) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout, unit);
            log.debug("成功存储缓存（带过期时间）: key={}, timeout={} {}", key, timeout, unit);
        } catch (Exception e) {
            log.error("存储缓存（带过期时间）失败: key={}, timeout={} {}", key, timeout, unit, e);
            throw new RuntimeException("存储缓存失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public void put(String key, Object value, Duration duration) {
        put(key, value, duration.toMillis(), TimeUnit.MILLISECONDS);
    }
    
    @Override
    public Object get(String key) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            log.debug("获取缓存: key={}, found={}", key, value != null);
            return value;
        } catch (Exception e) {
            log.error("获取缓存失败: key={}", key, e);
            return null;
        }
    }
    
    @Override
    @SuppressWarnings("unchecked")
    public <T> Optional<T> get(String key, Class<T> type) {
        try {
            Object value = get(key);
            if (value == null) {
                return Optional.empty();
            }
            
            // 如果类型直接匹配，直接转换
            if (type.isInstance(value)) {
                return Optional.of(type.cast(value));
            }
            
            // 尝试JSON序列化/反序列化转换
            try {
                String json = objectMapper.writeValueAsString(value);
                T result = objectMapper.readValue(json, type);
                return Optional.of(result);
            } catch (Exception serializationException) {
                log.warn("缓存值类型转换失败: key={}, expectedType={}, actualType={}", 
                        key, type.getSimpleName(), value.getClass().getSimpleName(), serializationException);
                return Optional.empty();
            }
            
        } catch (Exception e) {
            log.error("获取指定类型缓存失败: key={}, type={}", key, type.getSimpleName(), e);
            return Optional.empty();
        }
    }
    
    // ==================== Hash 操作 ====================
    
    @Override
    public void putHash(String key, Map<String, Object> hash) {
        try {
            redisTemplate.opsForHash().putAll(key, hash);
            log.debug("成功存储Hash缓存: key={}, fieldCount={}", key, hash.size());
        } catch (Exception e) {
            log.error("存储Hash缓存失败: key={}", key, e);
            throw new RuntimeException("存储Hash缓存失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public void putHash(String key, Map<String, Object> hash, long timeout, TimeUnit unit) {
        try {
            putHash(key, hash);
            expire(key, timeout, unit);
            log.debug("成功存储Hash缓存（带过期时间）: key={}, fieldCount={}, timeout={} {}", 
                     key, hash.size(), timeout, unit);
        } catch (Exception e) {
            log.error("存储Hash缓存（带过期时间）失败: key={}, timeout={} {}", key, timeout, unit, e);
            throw new RuntimeException("存储Hash缓存失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public Map<Object, Object> getHash(String key) {
        try {
            Map<Object, Object> hash = redisTemplate.opsForHash().entries(key);
            log.debug("获取Hash缓存: key={}, fieldCount={}", key, hash.size());
            return hash != null ? hash : new HashMap<>();
        } catch (Exception e) {
            log.error("获取Hash缓存失败: key={}", key, e);
            return new HashMap<>();
        }
    }
    
    @Override
    public Object getHashField(String key, String hashKey) {
        try {
            Object value = redisTemplate.opsForHash().get(key, hashKey);
            log.debug("获取Hash字段: key={}, hashKey={}, found={}", key, hashKey, value != null);
            return value;
        } catch (Exception e) {
            log.error("获取Hash字段失败: key={}, hashKey={}", key, hashKey, e);
            return null;
        }
    }
    
    // ==================== Key 操作 ====================
    
    @Override
    public boolean exists(String key) {
        try {
            Boolean exists = redisTemplate.hasKey(key);
            boolean result = Boolean.TRUE.equals(exists);
            log.debug("检查键是否存在: key={}, exists={}", key, result);
            return result;
        } catch (Exception e) {
            log.error("检查键是否存在失败: key={}", key, e);
            return false;
        }
    }
    
    @Override
    public boolean delete(String key) {
        try {
            Boolean deleted = redisTemplate.delete(key);
            boolean result = Boolean.TRUE.equals(deleted);
            log.debug("删除键: key={}, deleted={}", key, result);
            return result;
        } catch (Exception e) {
            log.error("删除键失败: key={}", key, e);
            return false;
        }
    }
    
    @Override
    public boolean expire(String key, long timeout, TimeUnit unit) {
        try {
            Boolean result = redisTemplate.expire(key, timeout, unit);
            boolean success = Boolean.TRUE.equals(result);
            log.debug("设置键过期时间: key={}, timeout={} {}, success={}", key, timeout, unit, success);
            return success;
        } catch (Exception e) {
            log.error("设置键过期时间失败: key={}, timeout={} {}", key, timeout, unit, e);
            return false;
        }
    }
    
    @Override
    public boolean expire(String key, Duration duration) {
        return expire(key, duration.toMillis(), TimeUnit.MILLISECONDS);
    }
    
    @Override
    public long getExpire(String key) {
        try {
            Long expire = redisTemplate.getExpire(key);
            long result = expire != null ? expire : -2;
            log.debug("获取键剩余生存时间: key={}, expire={}", key, result);
            return result;
        } catch (Exception e) {
            log.error("获取键剩余生存时间失败: key={}", key, e);
            return -2;
        }
    }
}