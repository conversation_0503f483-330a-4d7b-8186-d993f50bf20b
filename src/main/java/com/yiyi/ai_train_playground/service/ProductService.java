package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.dto.ProductListDTO;
import com.yiyi.ai_train_playground.dto.ProductDetailDTO;
import com.yiyi.ai_train_playground.dto.ProductUpdateDTO;
import com.yiyi.ai_train_playground.dto.PageRequest;
import com.yiyi.ai_train_playground.dto.PageResult;
import java.util.List;

public interface ProductService {
    
    /**
     * 获取商品列表
     * @param externalProductName 商品名称（可选）
     * @param token JWT令牌
     * @return 商品列表
     */
    List<ProductListDTO> getProductList(String externalProductName, String token);
    
    /**
     * 分页获取商品列表
     * @param externalProductName 商品名称（可选）
     * @param pageRequest 分页参数
     * @param token JWT令牌
     * @return 分页商品列表
     */
    PageResult<ProductListDTO> getProductListWithPage(String externalProductName, PageRequest pageRequest, String token);
    
    /**
     * 根据ID获取商品详情
     * @param productId 商品ID
     * @param token JWT令牌
     * @return 商品详情
     */
    ProductDetailDTO getProductById(Long productId, String token);
    
    /**
     * 更新商品信息
     * @param productUpdateDTO 商品更新信息
     * @param token JWT令牌
     */
    void updateProduct(ProductUpdateDTO productUpdateDTO, String token);
    
    /**
     * 更新商品信息并触发异步向量化处理
     * @param productUpdateDTO 商品更新信息
     * @param token JWT令牌
     */
    void updateProductWithAsyncProcessing(ProductUpdateDTO productUpdateDTO, String token);
    
    /**
     * 删除商品
     * @param productId 商品ID
     * @param token JWT令牌
     */
    void deleteProduct(Long productId, String token);
    
    /**
     * 更新商品学习状态和清理后的详情
     * @param externalProductId 外部商品ID
     * @param learningStatus 学习状态（2表示已学习）
     * @param cleanedDetail 清理后的商品详情
     * @param token JWT令牌
     */
    void updateLearningStatus(String externalProductId, Integer learningStatus, String cleanedDetail, String token);
} 