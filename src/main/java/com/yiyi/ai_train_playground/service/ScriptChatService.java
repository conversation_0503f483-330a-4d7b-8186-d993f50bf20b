package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.model.ScriptChatMessage;
import jakarta.websocket.Session;

public interface ScriptChatService {
    /**
     * 处理初始化消息
     * @param message 消息
     * @param session WebSocket会话
     */
    void handleInitMessage(ScriptChatMessage message, Session session);
    
    /**
     * 处理客服消息
     * @param message 消息
     * @param session WebSocket会话
     */
    void handleUserMessage(ScriptChatMessage message, Session session);
    
    /**
     * 构建系统消息
     * @param message 消息
     * @return 构建后的系统消息
     */
    String buildSystemMessage(ScriptChatMessage message);
} 