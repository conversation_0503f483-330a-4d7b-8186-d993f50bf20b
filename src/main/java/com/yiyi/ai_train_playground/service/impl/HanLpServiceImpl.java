package com.yiyi.ai_train_playground.service.impl;

import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.seg.common.Term;

import com.yiyi.ai_train_playground.model.TextChunk;
import com.yiyi.ai_train_playground.service.HanLpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.regex.Pattern;

/**
 * HanLP自然语言处理服务实现类
 */
@Slf4j
@Service
public class HanLpServiceImpl implements HanLpService {

    // 数量词性模式
    private static final Set<String> QUANTITY_NATURES = Set.of(
        "m", "mq", "mg", "ml", "mt", "ms", "mv", "mp", "mc", "md"
    );
    
    // 数字词性模式
    private static final Set<String> NUMBER_NATURES = Set.of(
        "num", "numeral", "cardinal", "ordinal"
    );
    
    // 单位词性模式
    private static final Set<String> UNIT_NATURES = Set.of(
        "q", "qg", "qt", "qv", "ql", "qm", "qd", "qc"
    );
    
    // 不可分割的语义单元模式（正则表达式）
    private static final List<Pattern> SEMANTIC_UNIT_PATTERNS = Arrays.asList(
        Pattern.compile("\\d+(\\.\\d+)?[a-zA-Z]*[克公斤千克吨磅盎司斤两钱]"), // 重量单位
        Pattern.compile("\\d+(\\.\\d+)?[毫厘分寸尺丈里米千米公里英里]"), // 长度单位
        Pattern.compile("\\d+(\\.\\d+)?[平方立方][米尺寸]"), // 面积体积单位
        Pattern.compile("\\d+(\\.\\d+)?[毫升升公升加仑]"), // 体积单位
        Pattern.compile("\\d+(\\.\\d+)?[个只支条根张片块]"), // 数量单位
        Pattern.compile("\\d+(\\.\\d+)?[元角分块毛钱]"), // 货币单位
        Pattern.compile("\\d+(\\.\\d+)?[度摄氏华氏]"), // 温度单位
        Pattern.compile("\\d+(\\.\\d+)?[秒分钟小时天周月年]"), // 时间单位
        Pattern.compile("\\d{4}[年]\\d{1,2}[月]\\d{1,2}[日]"), // 日期
        Pattern.compile("\\d{1,2}[点时]\\d{1,2}[分]"), // 时间
        Pattern.compile("[A-Za-z0-9]+[型号版本代]"), // 型号版本
        Pattern.compile("第\\d+[章节条款项]") // 章节编号
    );

    @PostConstruct
    public void init() {
        try {
            // 测试HanLP是否能正常工作
            List<Term> testResult = HanLP.segment("测试");
            log.info("HanLP服务初始化成功，测试分词结果: {}", testResult);
        } catch (Exception e) {
            log.error("HanLP服务初始化失败: {}", e.getMessage());
            // 不抛出异常，允许应用继续启动
        }
    }

    @Override
    public List<TextChunk> semanticChunking(String text, int chunkSize, int overlapSize) {
        if (text == null || text.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        if (chunkSize <= 0) {
            throw new IllegalArgumentException("分块大小必须大于0");
        }
        
        if (overlapSize < 0) {
            throw new IllegalArgumentException("重叠字符数不能为负数");
        }
        
        if (overlapSize >= chunkSize) {
            throw new IllegalArgumentException("重叠字符数不能大于等于分块大小");
        }

        log.info("开始语义分块: 文本长度={}, 分块大小={}, 重叠字符数={}", 
                text.length(), chunkSize, overlapSize);

        List<TextChunk> chunks = new ArrayList<>();
        
        // 对整个文本进行分词
        List<Term> terms = HanLP.segment(text);
        log.debug("分词完成，共{}个词", terms.size());
        
        // 构建语义单元列表
        List<SemanticUnitInfo> semanticUnits = buildSemanticUnits(text, terms);
        log.debug("构建语义单元完成，共{}个单元", semanticUnits.size());
        
        // 执行智能分块
        int chunkIndex = 0;
        int currentPos = 0;
        
        while (currentPos < text.length()) {
            // 计算当前分块的结束位置
            int chunkEndPos = Math.min(currentPos + chunkSize, text.length());
            
            // 调整分块边界，确保不分割语义单元
            chunkEndPos = adjustChunkBoundary(text, semanticUnits, currentPos, chunkEndPos);
            
            // 创建分块
            String chunkContent = text.substring(currentPos, chunkEndPos);
            List<TextChunk.SemanticUnit> chunkSemanticUnits = 
                getSemanticUnitsInRange(semanticUnits, currentPos, chunkEndPos);
            
            int actualOverlapLength = 0;
            if (chunkIndex > 0 && overlapSize > 0) {
                // 计算实际重叠长度
                int overlapStart = Math.max(0, currentPos - overlapSize);
                actualOverlapLength = currentPos - overlapStart;
            }
            
            TextChunk chunk = TextChunk.builder()
                    .content(chunkContent)
                    .index(chunkIndex)
                    .startPosition(currentPos)
                    .endPosition(chunkEndPos)
                    .length(chunkContent.length())
                    .overlapLength(actualOverlapLength)
                    .semanticUnits(chunkSemanticUnits)
                    .build();
            
            chunks.add(chunk);
            log.debug("创建分块 {}: 位置 {}-{}, 长度 {}", 
                    chunkIndex, currentPos, chunkEndPos, chunkContent.length());
            
            // 如果当前分块已经到达文本末尾，退出循环
            if (chunkEndPos >= text.length()) {
                break;
            }
            
            // 计算下一个分块的起始位置
            int nextPos = chunkEndPos - overlapSize;
            
            // 确保有进展，避免无限循环
            if (nextPos <= currentPos) {
                nextPos = currentPos + 1;
            }
            
            currentPos = nextPos;
            chunkIndex++;
        }
        
        log.info("语义分块完成，共生成{}个分块", chunks.size());
        return chunks;
    }

    @Override
    public List<TextChunk.SemanticUnit> segment(String text) {
        if (text == null || text.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        List<Term> terms = HanLP.segment(text);
        List<TextChunk.SemanticUnit> units = new ArrayList<>();
        
        int position = 0;
        for (Term term : terms) {
            int startPos = text.indexOf(term.word, position);
            int endPos = startPos + term.word.length();
            
            TextChunk.SemanticUnit unit = TextChunk.SemanticUnit.builder()
                    .word(term.word)
                    .nature(term.nature.toString())
                    .startPos(startPos)
                    .endPos(endPos)
                    .build();
            
            units.add(unit);
            position = endPos;
        }
        
        return units;
    }

    @Override
    public List<String> extractKeywords(String text, int topK) {
        if (text == null || text.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            List<String> keywords = HanLP.extractKeyword(text, topK);
            return keywords != null ? keywords : new ArrayList<>();
        } catch (Exception e) {
            log.error("提取关键词失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public String extractSummary(String text, int maxSentences) {
        if (text == null || text.trim().isEmpty()) {
            return "";
        }
        
        try {
            List<String> sentences = HanLP.extractSummary(text, maxSentences);
            return sentences != null ? String.join("", sentences) : "";
        } catch (Exception e) {
            log.error("提取摘要失败", e);
            return "";
        }
    }

    /**
     * 构建语义单元信息列表
     */
    private List<SemanticUnitInfo> buildSemanticUnits(String text, List<Term> terms) {
        List<SemanticUnitInfo> units = new ArrayList<>();
        int position = 0;
        
        for (int i = 0; i < terms.size(); i++) {
            Term term = terms.get(i);
            int startPos = text.indexOf(term.word, position);
            
            // 如果找不到词，使用当前位置
            if (startPos == -1) {
                log.warn("无法在文本中找到词语: '{}' at position {}, 使用当前位置", term.word, position);
                startPos = Math.min(position, text.length());
            }
            
            int endPos = Math.min(startPos + term.word.length(), text.length());
            
            // 检查是否为不可分割的语义单元
            boolean isIndivisible = isIndivisibleSemanticUnit(term, terms, i);
            
            SemanticUnitInfo unit = new SemanticUnitInfo();
            unit.word = term.word;
            unit.nature = term.nature.toString();
            unit.startPos = startPos;
            unit.endPos = endPos;
            unit.isIndivisible = isIndivisible;
            
            units.add(unit);
            position = endPos;
        }
        
        // 合并相邻的不可分割单元
        return mergeIndivisibleUnits(text, units);
    }

    /**
     * 判断是否为不可分割的语义单元
     */
    private boolean isIndivisibleSemanticUnit(Term term, List<Term> terms, int index) {
        String word = term.word;
        String nature = term.nature.toString();
        
        // 检查是否匹配预定义的语义单元模式
        for (Pattern pattern : SEMANTIC_UNIT_PATTERNS) {
            if (pattern.matcher(word).matches()) {
                return true;
            }
        }
        
        // 检查数量词组合（数字+单位）
        if (NUMBER_NATURES.contains(nature) && index + 1 < terms.size()) {
            Term nextTerm = terms.get(index + 1);
            if (QUANTITY_NATURES.contains(nextTerm.nature.toString()) || 
                UNIT_NATURES.contains(nextTerm.nature.toString())) {
                return true;
            }
        }
        
        // 检查专有名词
        if (nature.equals("nr") || nature.equals("ns") || nature.equals("nt")) {
            return true;
        }
        
        return false;
    }

    /**
     * 合并相邻的不可分割单元
     */
    private List<SemanticUnitInfo> mergeIndivisibleUnits(String text, List<SemanticUnitInfo> units) {
        List<SemanticUnitInfo> mergedUnits = new ArrayList<>();
        
        for (int i = 0; i < units.size(); i++) {
            SemanticUnitInfo current = units.get(i);
            
            // 验证当前单元的位置是否有效
            if (current.startPos < 0 || current.endPos < 0 || 
                current.startPos >= text.length() || current.endPos > text.length() ||
                current.startPos >= current.endPos) {
                log.warn("无效的语义单元位置: word='{}', startPos={}, endPos={}, textLength={}", 
                        current.word, current.startPos, current.endPos, text.length());
                // 跳过无效的单元
                continue;
            }
            
            if (current.isIndivisible && i + 1 < units.size()) {
                SemanticUnitInfo next = units.get(i + 1);
                
                // 验证下一个单元的位置是否有效
                if (next.startPos < 0 || next.endPos < 0 || 
                    next.startPos >= text.length() || next.endPos > text.length() ||
                    next.startPos >= next.endPos) {
                    log.warn("无效的下一个语义单元位置: word='{}', startPos={}, endPos={}, textLength={}", 
                            next.word, next.startPos, next.endPos, text.length());
                    // 添加当前单元，但跳过合并
                    mergedUnits.add(current);
                    continue;
                }
                
                // 如果下一个单元也是不可分割的，并且它们在语义上相关，则合并
                if (next.isIndivisible && isSemanticRelated(current, next)) {
                    try {
                        // 验证合并的索引范围
                        int mergeStartPos = current.startPos;
                        int mergeEndPos = next.endPos;
                        
                        if (mergeStartPos >= 0 && mergeEndPos <= text.length() && mergeStartPos < mergeEndPos) {
                            // 合并单元
                            SemanticUnitInfo merged = new SemanticUnitInfo();
                            merged.word = text.substring(mergeStartPos, mergeEndPos);
                            merged.nature = current.nature + "+" + next.nature;
                            merged.startPos = mergeStartPos;
                            merged.endPos = mergeEndPos;
                            merged.isIndivisible = true;
                            
                            mergedUnits.add(merged);
                            i++; // 跳过下一个单元
                            continue;
                        } else {
                            log.warn("合并时索引范围无效: startPos={}, endPos={}, textLength={}", 
                                    mergeStartPos, mergeEndPos, text.length());
                        }
                    } catch (Exception e) {
                        log.error("合并语义单元时出错: current='{}', next='{}'", current.word, next.word, e);
                    }
                }
            }
            
            mergedUnits.add(current);
        }
        
        return mergedUnits;
    }

    /**
     * 判断两个语义单元是否在语义上相关
     */
    private boolean isSemanticRelated(SemanticUnitInfo unit1, SemanticUnitInfo unit2) {
        // 数字+单位的组合
        if ((NUMBER_NATURES.contains(unit1.nature) && 
             (QUANTITY_NATURES.contains(unit2.nature) || UNIT_NATURES.contains(unit2.nature)))) {
            return true;
        }
        
        // 相同类型的专有名词
        if (unit1.nature.equals(unit2.nature) && 
            (unit1.nature.equals("nr") || unit1.nature.equals("ns") || unit1.nature.equals("nt"))) {
            return true;
        }
        
        return false;
    }

    /**
     * 调整分块边界，确保不分割语义单元
     */
    private int adjustChunkBoundary(String text, List<SemanticUnitInfo> semanticUnits, 
                                   int startPos, int endPos) {
        // 找到在边界附近的语义单元
        for (SemanticUnitInfo unit : semanticUnits) {
            // 如果语义单元跨越了分块边界
            if (unit.startPos < endPos && unit.endPos > endPos) {
                if (unit.isIndivisible) {
                    // 如果是不可分割的单元，调整边界
                    if (unit.startPos - startPos > endPos - unit.endPos) {
                        // 如果单元的大部分在当前分块中，包含整个单元
                        return unit.endPos;
                    } else {
                        // 否则，将整个单元移到下一个分块
                        return unit.startPos;
                    }
                }
            }
        }
        
        // 如果没有跨越边界的不可分割单元，尝试在句子边界分割
        return adjustAtSentenceBoundary(text, startPos, endPos);
    }

    /**
     * 在句子边界调整分块
     */
    private int adjustAtSentenceBoundary(String text, int startPos, int endPos) {
        // 常见的句子结束符
        String[] sentenceEnders = {"。", "！", "？", "；", "\n", ".", "!", "?", ";"};
        
        // 从理想结束位置向前查找句子边界
        for (int i = endPos - 1; i > startPos + (endPos - startPos) / 2; i--) {
            char ch = text.charAt(i);
            for (String ender : sentenceEnders) {
                if (String.valueOf(ch).equals(ender)) {
                    return i + 1;
                }
            }
        }
        
        return endPos;
    }

    /**
     * 获取指定范围内的语义单元
     */
    private List<TextChunk.SemanticUnit> getSemanticUnitsInRange(
            List<SemanticUnitInfo> semanticUnits, int startPos, int endPos) {
        List<TextChunk.SemanticUnit> result = new ArrayList<>();
        
        for (SemanticUnitInfo unit : semanticUnits) {
            if (unit.startPos >= startPos && unit.endPos <= endPos) {
                TextChunk.SemanticUnit chunkUnit = TextChunk.SemanticUnit.builder()
                        .word(unit.word)
                        .nature(unit.nature)
                        .startPos(unit.startPos - startPos) // 相对于分块的位置
                        .endPos(unit.endPos - startPos)
                        .build();
                result.add(chunkUnit);
            }
        }
        
        return result;
    }

    /**
     * 内部语义单元信息类
     */
    private static class SemanticUnitInfo {
        String word;
        String nature;
        int startPos;
        int endPos;
        boolean isIndivisible;
    }
} 