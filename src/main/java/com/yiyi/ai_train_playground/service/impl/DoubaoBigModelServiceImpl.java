package com.yiyi.ai_train_playground.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionContentPart;
import com.volcengine.ark.runtime.model.context.CreateContextRequest;

import com.volcengine.ark.runtime.model.context.chat.ContextChatCompletionRequest;
import com.volcengine.ark.runtime.model.embeddings.EmbeddingRequest;
import com.volcengine.ark.runtime.model.embeddings.EmbeddingResult;
import com.volcengine.ark.runtime.service.ArkService;
import com.volcengine.ark.runtime.exception.ArkHttpException;
import com.yiyi.ai_train_playground.config.DoubaoConfig;
import com.yiyi.ai_train_playground.config.DoubaiBigModelConfig;
import com.yiyi.ai_train_playground.model.ContextResult;
import com.yiyi.ai_train_playground.service.SuperBigModelInterface;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import jakarta.annotation.PreDestroy;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DoubaoBigModelServiceImpl implements SuperBigModelInterface {
    private final DoubaoConfig config;
    private final DoubaiBigModelConfig doubaiBigModelConfig;
    private final ArkService arkService;
    private final ObjectMapper objectMapper;

    @Autowired
    public DoubaoBigModelServiceImpl(DoubaoConfig config, DoubaiBigModelConfig doubaiBigModelConfig, ObjectMapper objectMapper) {
        this.config = config;
        this.doubaiBigModelConfig = doubaiBigModelConfig;
        this.objectMapper = objectMapper;

        // 从配置文件获取连接池参数
        DoubaoConfig.ConnectionPool poolConfig = config.getConnectionPool();
        if (poolConfig == null) {
            poolConfig = new DoubaoConfig.ConnectionPool(); // 使用默认值
        }

        // 自建连接池，使用配置文件中的参数
        ConnectionPool connectionPool = new ConnectionPool(
                poolConfig.getMaxIdle(),
                poolConfig.getKeepAliveDuration(),
                TimeUnit.MINUTES
        );

        Dispatcher dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(poolConfig.getMaxRequests());
        dispatcher.setMaxRequestsPerHost(poolConfig.getMaxRequestsPerHost());

        this.arkService = ArkService.builder()
                .dispatcher(dispatcher)
                .connectionPool(connectionPool)
                .baseUrl(config.getUrl())
                .apiKey(doubaiBigModelConfig.getApiKey())
                .build();

        log.info("豆包大模型服务初始化完成，连接池配置: maxIdle={}, maxRequests={}, maxRequestsPerHost={}",
                poolConfig.getMaxIdle(), poolConfig.getMaxRequests(), poolConfig.getMaxRequestsPerHost());
    }

    /**
     * 通用非流式聊天方法
     */
    private String chatWithModel(String modelName, List<ChatMessage> messages) {
        try {
            ChatCompletionRequest request = ChatCompletionRequest.builder()
                    .model(modelName)
                    .messages(messages)
                    .build();

            String content = arkService.createChatCompletion(request)
                    .getChoices()
                    .get(0)
                    .getMessage()
                    .getContent().toString();

            return content;
        } catch (ArkHttpException e) {
            throw new RuntimeException("调用豆包API失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通用流式聊天方法
     */
    private Flux<String> streamChatWithModel(String modelName, List<ChatMessage> messages) {
        ChatCompletionRequest request = ChatCompletionRequest.builder()
                .model(modelName)
                .messages(messages)
                .stream(true)
                .build();

        return Flux.create(sink -> {
            try {
                arkService.streamChatCompletion(request)
                        .doOnError(e -> {
                            sink.error(new RuntimeException("调用豆包API失败: " + e.getMessage(), e));
                        })
                        .blockingForEach(choice -> {
                            if (choice.getChoices().size() > 0) {
                                String content = choice.getChoices().get(0).getMessage().getContent().toString();

                                // 直接返回文本内容，不包含进度信息
                                sink.next(content);

                                if (choice.getChoices().get(0).getFinishReason() != null) {
                                    sink.complete();
                                }
                            }
                        });
            } catch (Exception e) {
                sink.error(new RuntimeException("调用豆包API失败: " + e.getMessage(), e));
            }
        });
    }

    @Override
    public String ntns(List<ChatMessage> messages) {
        return chatWithModel(config.getNormal().getModel().getName(), messages);
    }

    @Override
    public String tns(List<ChatMessage> messages) {
        return chatWithModel(config.getThink().getModel().getName(), messages);
    }

    @Override
    public Flux<String> nts(List<ChatMessage> messages) {
        return streamChatWithModel(config.getNormal().getModel().getName(), messages);
    }

    @Override
    public Flux<String> ts(List<ChatMessage> messages) {
        return streamChatWithModel(config.getThink().getModel().getName(), messages);
    }

    @Override
    public Flux<String> tsOnce(String systemPrompt, String userPrompt) {
        // 创建独立的消息列表，不与会话管理关联
        List<ChatMessage> messages = new ArrayList<>();

        // 添加系统提示词
        if (systemPrompt != null && !systemPrompt.isEmpty()) {
            messages.add(ChatMessage.builder()
                    .role(ChatMessageRole.SYSTEM)
                    .content(systemPrompt)
                    .build());
        }

        // 添加用户消息
        messages.add(ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .content(userPrompt)
                .build());

        ChatCompletionRequest request = ChatCompletionRequest.builder()
                .model(config.getThink().getModel().getName())
                .messages(messages)
                .stream(true)
                .build();

        return Flux.create(sink -> {
            StringBuilder fullResponse = new StringBuilder();
            AtomicInteger tokenCount = new AtomicInteger(0);
            final int totalToken = config.getEstimateToken() != null ? config.getEstimateToken() : 500;

            try {
                arkService.streamChatCompletion(request)
                        .doOnError(e -> {
                            sink.error(new RuntimeException("调用豆包API失败: " + e.getMessage(), e));
                        })
                        .blockingForEach(choice -> {
                            if (choice.getChoices().size() > 0) {
                                String content = choice.getChoices().get(0).getMessage().getContent().toString();
                                fullResponse.append(content);

                                // 累加token数量并计算进度
                                tokenCount.addAndGet(content.length());
                                int progress = Math.min(100, Math.round((float) tokenCount.get() * 100 / totalToken));

                                // 构建包含进度的响应
                                Map<String, Object> response = new HashMap<>();
                                response.put("content", content);
                                response.put("progress", progress);

                                try {
                                    sink.next(objectMapper.writeValueAsString(response));
                                } catch (Exception e) {
                                    log.error("序列化响应失败", e);
                                    sink.next(content);
                                }

                                if (choice.getChoices().get(0).getFinishReason() != null) {
                                    sink.complete();
                                }
                            }
                        });
            } catch (Exception e) {
                sink.error(new RuntimeException("调用豆包API失败: " + e.getMessage(), e));
            }
        });
    }

    @Override
    public String tnsOnce(String systemPrompt, String userPrompt) {
        return chatWithModelOnce(config.getThink().getModel().getName(), systemPrompt, userPrompt);
    }

    @Override
    public String ntnsOnce(String systemPrompt, String userPrompt) {
        return chatWithModelOnce(config.getNormal().getModel().getName(), systemPrompt, userPrompt);
    }

    @Override
    public Flux<String> ntsOnce(String systemPrompt, String userPrompt) {
        return streamChatWithModelOnce(config.getNormal().getModel().getName(), systemPrompt, userPrompt);
    }

    /**
     * 一次性非流式聊天方法（无会话管理）
     */
    private String chatWithModelOnce(String modelName, String systemPrompt, String userPrompt) {
        try {
            List<ChatMessage> messages = new ArrayList<>();

            // 添加系统提示词
            if (systemPrompt != null && !systemPrompt.isEmpty()) {
                messages.add(ChatMessage.builder()
                        .role(ChatMessageRole.SYSTEM)
                        .content(systemPrompt)
                        .build());
            }

            // 添加用户消息
            if (userPrompt != null && !userPrompt.isEmpty()) {
                messages.add(ChatMessage.builder()
                        .role(ChatMessageRole.USER)
                        .content(userPrompt)
                        .build());
            }

            ChatCompletionRequest request = ChatCompletionRequest.builder()
                    .model(modelName)
                    .messages(messages)
                    .build();

            String content = arkService.createChatCompletion(request)
                    .getChoices()
                    .get(0)
                    .getMessage()
                    .getContent().toString();

            return content;
        } catch (ArkHttpException e) {
            throw new RuntimeException("调用豆包API失败: " + e.getMessage(), e);
        }
    }

    /**
     * 一次性流式聊天方法（无会话管理）
     */
    private Flux<String> streamChatWithModelOnce(String modelName, String systemPrompt, String userPrompt) {
        List<ChatMessage> messages = new ArrayList<>();

        // 添加系统提示词
        if (systemPrompt != null && !systemPrompt.isEmpty()) {
            messages.add(ChatMessage.builder()
                    .role(ChatMessageRole.SYSTEM)
                    .content(systemPrompt)
                    .build());
        }

        // 添加用户消息
        messages.add(ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .content(userPrompt)
                .build());

        ChatCompletionRequest request = ChatCompletionRequest.builder()
                .model(modelName)
                .messages(messages)
                .stream(true)
                .build();

        return Flux.create(sink -> {
            try {
                arkService.streamChatCompletion(request)
                        .doOnError(e -> {
                            sink.error(new RuntimeException("调用豆包API失败: " + e.getMessage(), e));
                        })
                        .blockingForEach(choice -> {
                            if (choice.getChoices().size() > 0) {
                                String content = choice.getChoices().get(0).getMessage().getContent().toString();

                                // 直接返回文本内容，不包含进度信息
                                sink.next(content);

                                if (choice.getChoices().get(0).getFinishReason() != null) {
                                    sink.complete();
                                }
                            }
                        });
            } catch (Exception e) {
                sink.error(new RuntimeException("调用豆包API失败: " + e.getMessage(), e));
            }
        });
    }

    @Override
    public Flux<String> imageChatWithStream(String imageUrl, String prompt, String systemPrompt) {
        try {
            List<ChatMessage> messages = new ArrayList<>();

            // 如果有系统提示词，添加系统提示词
            if (systemPrompt != null && !systemPrompt.isEmpty()) {
                messages.add(ChatMessage.builder()
                        .role(ChatMessageRole.SYSTEM)
                        .content(systemPrompt)
                        .build());
            }

            // 构建图像消息
            List<ChatCompletionContentPart> contentParts = new ArrayList<>();

            // 添加文本内容
            contentParts.add(ChatCompletionContentPart.builder()
                    .type("text")
                    .text(prompt)
                    .build());

            // 添加图像内容
            contentParts.add(ChatCompletionContentPart.builder()
                    .type("image_url")
                    .imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL(imageUrl))
                    .build());

            // 构建用户消息
            messages.add(ChatMessage.builder()
                    .role(ChatMessageRole.USER)
                    .multiContent(contentParts)
                    .build());

            ChatCompletionRequest request = ChatCompletionRequest.builder()
                    .model(config.getImage().getModel().getName())
                    .messages(messages)
                    .stream(true)
                    .build();

            return Flux.create(sink -> {
                try {
                    arkService.streamChatCompletion(request)
                            .doOnError(e -> {
                                sink.error(new RuntimeException("调用豆包API失败: " + e.getMessage(), e));
                            })
                            .blockingForEach(choice -> {
                                if (choice.getChoices().size() > 0) {
                                    String content = choice.getChoices().get(0).getMessage().getContent().toString();

                                    // 直接返回文本内容，不包含进度信息
                                    sink.next(content);

                                    if (choice.getChoices().get(0).getFinishReason() != null) {
                                        sink.complete();
                                    }
                                }
                            });
                } catch (Exception e) {
                    sink.error(new RuntimeException("调用豆包API失败: " + e.getMessage(), e));
                }
            });
        } catch (ArkHttpException e) {
            return Flux.error(new RuntimeException("调用豆包API失败: " + e.getMessage(), e));
        }
    }

    /**
     * 非流式图像对话方法
     */
    @Override
    public String imageChat(String imageUrl, String prompt, String systemPrompt) {
        try {
            List<ChatMessage> messages = new ArrayList<>();

            // 如果有系统提示词，添加系统提示词
            if (systemPrompt != null && !systemPrompt.isEmpty()) {
                messages.add(ChatMessage.builder()
                        .role(ChatMessageRole.SYSTEM)
                        .content(systemPrompt)
                        .build());
            }

            // 构建图像消息
            List<ChatCompletionContentPart> contentParts = new ArrayList<>();

            // 添加文本内容
            contentParts.add(ChatCompletionContentPart.builder()
                    .type("text")
                    .text(prompt)
                    .build());

            // 添加图像内容
            contentParts.add(ChatCompletionContentPart.builder()
                    .type("image_url")
                    .imageUrl(new ChatCompletionContentPart.ChatCompletionContentPartImageURL(imageUrl))
                    .build());

            // 构建用户消息
            messages.add(ChatMessage.builder()
                    .role(ChatMessageRole.USER)
                    .multiContent(contentParts)
                    .build());

            ChatCompletionRequest request = ChatCompletionRequest.builder()
                    .model(config.getImage().getModel().getName())
                    .messages(messages)
                    .build();

            String responseContent = arkService.createChatCompletion(request)
                    .getChoices()
                    .get(0)
                    .getMessage()
                    .getContent().toString();

            return responseContent;
        } catch (ArkHttpException e) {
            throw new RuntimeException("调用豆包API失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<List<Double>> embed(List<String> texts) {
        try {
            log.info("开始向量化文本，数量: {}", texts.size());

            EmbeddingRequest embeddingRequest = EmbeddingRequest.builder()
                    .model(config.getEmbed().getModelName())
                    .input(texts)
                    .build();

            EmbeddingResult result = arkService.createEmbeddings(embeddingRequest);

            // 将结果转换为List<List<Double>>
            List<List<Double>> embeddings = result.getData().stream()
                    .map(embedding -> embedding.getEmbedding())
                    .collect(Collectors.toList());

            log.info("文本向量化完成，返回 {} 个向量，每个向量维度: {}",
                    embeddings.size(),
                    embeddings.isEmpty() ? 0 : embeddings.get(0).size());

            return embeddings;
        } catch (ArkHttpException e) {
            log.error("调用豆包向量化API失败", e);
            throw new RuntimeException("调用豆包向量化API失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成上下文ID的方法
     * @param systemPrompt 系统提示词
     * @return 上下文结果对象
     */
    public ContextResult generateContextId(String systemPrompt) {
        try {
            log.info("开始生成上下文ID，系统提示词长度: {}", systemPrompt.length());

            List<ChatMessage> messagesForReqList = new ArrayList<>();

            ChatMessage elementForMessagesForReqList0 = ChatMessage.builder()
                    .role(ChatMessageRole.SYSTEM)
                    .content(systemPrompt)
                    .build();
            messagesForReqList.add(elementForMessagesForReqList0);

            CreateContextRequest req = CreateContextRequest.builder()
                    .model(config.getNormal().getEndpoint().getName())
                    .mode("common_prefix")
                    .messages(messagesForReqList)
                    .build();

            String resp = arkService.createContext(req).toString();
            log.info("豆包返回的上下文创建结果: {}", resp);

            // 解析响应字符串，提取关键信息
            ContextResult contextResult = parseContextResponse(resp);

            log.info("上下文ID生成成功: {}", contextResult.getId());
            return contextResult;
        } catch (ArkHttpException e) {
            log.error("生成上下文ID失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成上下文ID失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析豆包返回的上下文创建响应
     * @param resp 响应字符串
     * @return 解析后的上下文结果
     */
    private ContextResult parseContextResponse(String resp) {
        try {
            // 使用正则表达式解析响应字符串
            // 示例: CreateContextResult{id='ctx-20250630111148-ptcg9', model='ep-20250629195408-gtv9c', mode='common_prefix', ttl=86400, ...}

            String id = extractValue(resp, "id='([^']+)'");
            String model = extractValue(resp, "model='([^']+)'");
            String mode = extractValue(resp, "mode='([^']+)'");
            String ttlStr = extractValue(resp, "ttl=(\\d+)");
            String promptTokensStr = extractValue(resp, "promptTokens=(\\d+)");
            String completionTokensStr = extractValue(resp, "completionTokens=(\\d+)");
            String totalTokensStr = extractValue(resp, "totalTokens=(\\d+)");
            String cachedTokensStr = extractValue(resp, "cachedTokens=(\\d+)");

            ContextResult.Usage usage = ContextResult.Usage.builder()
                    .promptTokens(promptTokensStr != null ? Integer.parseInt(promptTokensStr) : 0)
                    .completionTokens(completionTokensStr != null ? Integer.parseInt(completionTokensStr) : 0)
                    .totalTokens(totalTokensStr != null ? Integer.parseInt(totalTokensStr) : 0)
                    .promptTokensDetails(ContextResult.PromptTokensDetails.builder()
                            .cachedTokens(cachedTokensStr != null ? Integer.parseInt(cachedTokensStr) : 0)
                            .build())
                    .build();

            return ContextResult.builder()
                    .id(id)
                    .model(model)
                    .mode(mode)
                    .ttl(ttlStr != null ? Integer.parseInt(ttlStr) : 86400)
                    .usage(usage)
                    .build();
        } catch (Exception e) {
            log.error("解析上下文响应失败: {}", e.getMessage(), e);
            throw new RuntimeException("解析上下文响应失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从响应字符串中提取指定模式的值
     * @param text 文本
     * @param pattern 正则表达式模式
     * @return 提取的值，如果未找到则返回null
     */
    private String extractValue(String text, String pattern) {
        java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = p.matcher(text);
        return m.find() ? m.group(1) : null;
    }

    @Override
    public String ntnsWithCtx(List<ChatMessage> messages, String contextId) {
        try {
            log.info("开始调用带上下文的Normal模型，contextId: {}, 消息数量: {}", contextId, messages.size());

            ContextChatCompletionRequest req =
                    ContextChatCompletionRequest.builder()
                            .contextId(contextId)
                            .model(config.getNormal().getEndpoint().getName())
                            .messages(messages)
                            .build();

            String content = arkService.createContextChatCompletion(req)
                    .getChoices()
                    .get(0)
                    .getMessage()
                    .getContent().toString();


            log.info("带上下文的Normal模型调用完成，响应长度: {}", content.length());
            return content;
        } catch (ArkHttpException e) {
            log.error("调用带上下文的豆包API失败: {}", e.getMessage(), e);
            throw new RuntimeException("调用豆包API失败: " + e.getMessage(), e);
        }
    }

    @PreDestroy
    public void destroy() {
        if (arkService != null) {
            arkService.shutdownExecutor();
        }
    }
}