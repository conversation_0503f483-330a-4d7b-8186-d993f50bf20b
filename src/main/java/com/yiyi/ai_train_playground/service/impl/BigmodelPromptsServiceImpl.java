package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.mapper.BigmodelPromptsMapper;
import com.yiyi.ai_train_playground.service.BigmodelPromptsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class BigmodelPromptsServiceImpl implements BigmodelPromptsService {
    
    @Autowired
    private BigmodelPromptsMapper bigmodelPromptsMapper;
    
    @Override
    public List<String> getPromptsByKeyword(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            throw new IllegalArgumentException("关键词不能为空");
        }
        
        // 检查是否包含冒号
        if (!keyword.contains(":")) {
            throw new IllegalArgumentException("关键词格式错误，应为：keyword:SU");
        }
        
        String[] parts = keyword.split(":", 2);
        if (parts.length != 2) {
            throw new IllegalArgumentException("关键词格式错误，应为：keyword:SU");
        }
        
        String keywordPart = parts[0].trim();
        String typePart = parts[1].trim();
        
        if (keywordPart.isEmpty()) {
            throw new IllegalArgumentException("关键词不能为空");
        }
        
        if (typePart.isEmpty()) {
            throw new IllegalArgumentException("提示词类型不能为空，应包含S或U");
        }
        
        // 验证类型参数，必须包含S或U中至少一个
        boolean hasS = typePart.contains("S");
        boolean hasU = typePart.contains("U");
        
        if (!hasS && !hasU) {
            throw new IllegalArgumentException("提示词类型错误，必须包含S（系统提示词）或U（用户提示词）中的至少一个");
        }
        
        log.info("查询提示词：keyword={}, hasS={}, hasU={}", keywordPart, hasS, hasU);
        
        List<String> result = new ArrayList<>();
        
        try {
            if (hasS && hasU) {
                // 查询系统和用户提示词
                result = bigmodelPromptsMapper.findBothPromptsByKeyword(keywordPart);
            } else if (hasS) {
                // 只查询系统提示词
                result = bigmodelPromptsMapper.findSysPromptsByKeyword(keywordPart);
            } else {
                // 只查询用户提示词
                result = bigmodelPromptsMapper.findUsrPromptsByKeyword(keywordPart);
            }
            
            log.info("查询结果：keyword={}, 返回{}条提示词", keywordPart, result.size());
            return result;
            
        } catch (Exception e) {
            log.error("查询提示词失败：keyword={}", keywordPart, e);
            throw new RuntimeException("查询提示词失败：" + e.getMessage());
        }
    }
} 