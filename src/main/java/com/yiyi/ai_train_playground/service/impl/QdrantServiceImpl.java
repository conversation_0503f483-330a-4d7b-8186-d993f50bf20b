package com.yiyi.ai_train_playground.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.model.VectorData;
import com.yiyi.ai_train_playground.model.VectorSearchRequest;
import com.yiyi.ai_train_playground.model.VectorSearchResult;
import com.yiyi.ai_train_playground.service.QdrantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Qdrant向量数据库服务接口
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QdrantServiceImpl implements QdrantService {

    private final WebClient qdrantWebClient;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean createCollection(String collectionName, int vectorSize, String distance) {
        try {
            log.info("Creating collection: {}, vector size: {}, distance: {}", collectionName, vectorSize, distance);
            
            Map<String, Object> vectorConfig = new HashMap<>();
            vectorConfig.put("size", vectorSize);
            vectorConfig.put("distance", capitalizeDistance(distance));
            
            Map<String, Object> request = new HashMap<>();
            request.put("vectors", vectorConfig);
            
            String response = qdrantWebClient
                    .put()
                    .uri("/collections/{collection_name}", collectionName)
                    .bodyValue(request)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();
                    
            log.debug("Create collection response: {}", response);
            return true;
        } catch (WebClientResponseException e) {
            log.error("Failed to create collection: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            return false;
        } catch (Exception e) {
            log.error("Create collection exception", e);
            return false;
        }
    }

    @Override
    public boolean deleteCollection(String collectionName) {
        try {
            log.info("Deleting collection: {}", collectionName);
            
            String response = qdrantWebClient
                    .delete()
                    .uri("/collections/{collection_name}", collectionName)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();
                    
            log.debug("Delete collection response: {}", response);
            return true;
        } catch (WebClientResponseException e) {
            log.error("Failed to delete collection: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            return false;
        } catch (Exception e) {
            log.error("Delete collection exception", e);
            return false;
        }
    }

    @Override
    public boolean collectionExists(String collectionName) {
        try {
            log.info("Checking if collection exists: {}", collectionName);
            
            qdrantWebClient
                    .get()
                    .uri("/collections/{collection_name}", collectionName)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();
                    
            return true;
        } catch (WebClientResponseException e) {
            if (e.getStatusCode() == HttpStatus.NOT_FOUND) {
                return false;
            }
            log.error("Failed to check collection existence: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            return false;
        } catch (Exception e) {
            log.error("Check collection existence exception", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getCollectionInfo(String collectionName) {
        try {
            log.info("Getting collection info: {}", collectionName);
            
            String response = qdrantWebClient
                    .get()
                    .uri("/collections/{collection_name}", collectionName)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();
                    
            JsonNode jsonNode = objectMapper.readTree(response);
            JsonNode result = jsonNode.get("result");
            
            Map<String, Object> info = new HashMap<>();
            info.put("name", collectionName);
            info.put("status", result.get("status").asText());
            
            JsonNode config = result.get("config");
            if (config != null) {
                JsonNode params = config.get("params");
                if (params != null) {
                    JsonNode vectors = params.get("vectors");
                    if (vectors != null) {
                        info.put("vectorSize", vectors.get("size").asInt());
                        info.put("distance", vectors.get("distance").asText());
                    }
                }
            }
            
            JsonNode pointsCount = result.get("points_count");
            if (pointsCount != null) {
                info.put("pointsCount", pointsCount.asLong());
            }
            
            return info;
        } catch (Exception e) {
            log.error("Failed to get collection info", e);
            return new HashMap<>();
        }
    }

    @Override
    public boolean insertVector(String collectionName, VectorData vectorData) {
        try {
            log.info("Inserting vector: collection={}, ID={}", collectionName, vectorData.getId());
            
            Map<String, Object> point = new HashMap<>();
            
            Object pointId = null;
            try {
//                pointId = Integer.parseInt(vectorData.getId());
                pointId = Long.valueOf(vectorData.getId());
            } catch (NumberFormatException e) {
                pointId = vectorData.getId();
            }
            
            point.put("id", pointId);
            point.put("vector", vectorData.getVector());
            if (vectorData.getPayload() != null && !vectorData.getPayload().isEmpty()) {
                point.put("payload", vectorData.getPayload());
            }
            
            Map<String, Object> request = new HashMap<>();
            request.put("points", Collections.singletonList(point));
            
            log.debug("Sending request to Qdrant: {}", request);
            
            String response = qdrantWebClient
                    .put()
                    .uri("/collections/{collection_name}/points", collectionName)
                    .bodyValue(request)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();
                    
            log.debug("Insert vector response: {}", response);
            return true;
        } catch (WebClientResponseException e) {
            log.error("Failed to insert vector - HTTP Status: {}, Response: {}", 
                     e.getStatusCode(), e.getResponseBodyAsString());
            return false;
        } catch (Exception e) {
            log.error("Failed to insert vector", e);
            return false;
        }
    }

    @Override
    public boolean insertVectors(String collectionName, List<VectorData> vectorDataList) {
        try {
            log.info("Batch inserting vectors: collection={}, count={}", collectionName, vectorDataList.size());
            
            // Log first vector details for debugging
            if (!vectorDataList.isEmpty()) {
                VectorData firstVector = vectorDataList.get(0);
                log.debug("First vector details - ID: {}, vector size: {}, payload keys: {}", 
                        firstVector.getId(), 
                        firstVector.getVector() != null ? firstVector.getVector().size() : "null",
                        firstVector.getPayload() != null ? firstVector.getPayload().keySet() : "null");
            }
            
            List<Map<String, Object>> points = vectorDataList.stream()
                    .map(vectorData -> {
                        Map<String, Object> point = new HashMap<>();
                        
                        // UUID格式的ID直接使用字符串，无需转换
                        String pointId = vectorData.getId();
//                        Long pointId = vectorData.getId();

                        point.put("id", pointId);
                        point.put("vector", vectorData.getVector());
                        if (vectorData.getPayload() != null && !vectorData.getPayload().isEmpty()) {
                            point.put("payload", vectorData.getPayload());
                        }
                        return point;
                    })
                    .collect(Collectors.toList());
            
            Map<String, Object> request = new HashMap<>();
            request.put("points", points);
            
            // Log request summary for debugging
            log.debug("Qdrant request summary - points count: {}, first point ID: {}, first vector size: {}", 
                    points.size(),
                    points.isEmpty() ? "none" : points.get(0).get("id"),
                    points.isEmpty() ? 0 : 
                        (points.get(0).get("vector") instanceof List ? 
                            ((List<?>)points.get(0).get("vector")).size() : "not_list"));
            
            String response = qdrantWebClient
                    .put()
                    .uri("/collections/{collection_name}/points", collectionName)
                    .bodyValue(request)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(60))
                    .block();
                    
            log.debug("Batch insert vectors response: {}", response);
            return true;
        } catch (WebClientResponseException e) {
            log.error("WebClient error during batch insert: status={}, body={}", 
                    e.getStatusCode(), e.getResponseBodyAsString());
            return false;
        } catch (Exception e) {
            log.error("Failed to batch insert vectors", e);
            return false;
        }
    }

    @Override
    public VectorSearchResult searchVectors(VectorSearchRequest request) {
        try {
            log.info("Searching vectors: collection={}, limit={}", request.getCollectionName(), request.getLimit());
            
            Map<String, Object> searchRequest = new HashMap<>();
            searchRequest.put("vector", request.getVector());
            searchRequest.put("limit", request.getLimit());
            searchRequest.put("with_payload", request.getWithPayload());
            searchRequest.put("with_vector", request.getWithVector());
            
            if (request.getScoreThreshold() != null) {
                searchRequest.put("score_threshold", request.getScoreThreshold());
            }
            
            if (request.getFilter() != null && !request.getFilter().isEmpty()) {
                Map<String, Object> qdrantFilter = convertToQdrantFilter(request.getFilter());
                searchRequest.put("filter", qdrantFilter);
                log.debug("Applied filter: {}", qdrantFilter);
            }
            
            long startTime = System.currentTimeMillis();
            String response = qdrantWebClient
                    .post()
                    .uri("/collections/{collection_name}/points/search", request.getCollectionName())
                    .bodyValue(searchRequest)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();
            long duration = System.currentTimeMillis() - startTime;
            
            JsonNode jsonNode = objectMapper.readTree(response);
            JsonNode result = jsonNode.get("result");
            
            List<VectorData> results = new ArrayList<>();
            if (result != null && result.isArray()) {
                for (JsonNode node : result) {
                    VectorData vectorData = new VectorData();
                    vectorData.setId(node.get("id").asText());
//                    vectorData.setId(Long.valueOf(node.get("id").asText()));
                    vectorData.setScore((float) node.get("score").asDouble());
                    
                    if (node.has("vector")) {
                        JsonNode vectorNode = node.get("vector");
                        List<Float> vector = new ArrayList<>();
                        for (JsonNode v : vectorNode) {
                            vector.add((float) v.asDouble());
                        }
                        vectorData.setVector(vector);
                    }
                    
                    if (node.has("payload")) {
                        JsonNode payloadNode = node.get("payload");
                        Map<String, Object> payload = objectMapper.convertValue(payloadNode, Map.class);
                        vectorData.setPayload(payload);
                    }
                    
                    results.add(vectorData);
                }
            }
            
            VectorSearchResult searchResult = new VectorSearchResult();
            searchResult.setResults(results);
            searchResult.setDuration(duration);
            searchResult.setTotal(results.size());
            
            return searchResult;
        } catch (Exception e) {
            log.error("Failed to search vectors", e);
            return new VectorSearchResult();
        }
    }

    @Override
    public VectorData getVectorById(String collectionName, String id) {
        try {
            log.info("Getting vector by ID: collection={}, ID={}", collectionName, id);
            
            String response = qdrantWebClient
                    .get()
                    .uri("/collections/{collection_name}/points/{id}", collectionName, id)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();
                    
            JsonNode jsonNode = objectMapper.readTree(response);
            JsonNode result = jsonNode.get("result");
            
            if (result != null && !result.isNull()) {
                VectorData vectorData = new VectorData();
                vectorData.setId(result.get("id").asText());
//                vectorData.setId(Long.valueOf(result.get("id").asText()));

                if (result.has("vector")) {
                    JsonNode vectorNode = result.get("vector");
                    List<Float> vector = new ArrayList<>();
                    for (JsonNode v : vectorNode) {
                        vector.add((float) v.asDouble());
                    }
                    vectorData.setVector(vector);
                }
                
                if (result.has("payload")) {
                    JsonNode payloadNode = result.get("payload");
                    Map<String, Object> payload = objectMapper.convertValue(payloadNode, Map.class);
                    vectorData.setPayload(payload);
                }
                
                return vectorData;
            }
            
            return null;
        } catch (WebClientResponseException e) {
            if (e.getStatusCode() == HttpStatus.NOT_FOUND) {
                return null;
            }
            log.error("Failed to get vector by ID: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            return null;
        } catch (Exception e) {
            log.error("Get vector by ID exception", e);
            return null;
        }
    }

    @Override
    public boolean deleteVector(String collectionName, String id) {
        try {
            log.info("Deleting vector: collection={}, ID={}", collectionName, id);
            
            // 使用与insertVectors相同的ID处理逻辑
            Object pointId = id;
            if (id.matches("^\\d+$")) {
                // 只有纯数字的ID才转换为整数
                try {
                    pointId = Integer.parseInt(id);
                } catch (NumberFormatException e) {
                    pointId = id;
                }
            } else {
                // 包含非数字字符的ID保持为字符串
                pointId = id;
            }
            
            Map<String, Object> request = new HashMap<>();
            request.put("points", Collections.singletonList(pointId));
            
            String response = qdrantWebClient
                    .post()
                    .uri("/collections/{collection_name}/points/delete", collectionName)
                    .bodyValue(request)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();
                    
            log.debug("Delete vector response: {}", response);
            return true;
        } catch (Exception e) {
            log.error("Failed to delete vector", e);
            return false;
        }
    }

    @Override
    public boolean deleteVectors(String collectionName, List<String> ids) {
        try {
            log.info("Batch deleting vectors: collection={}, count={}", collectionName, ids.size());
            
            // 使用与insertVectors相同的ID处理逻辑
            List<Object> pointIds = new ArrayList<>();
            for (String id : ids) {
                if (id.matches("^\\d+$")) {
                    // 只有纯数字的ID才转换为整数
                    try {
                        pointIds.add(Integer.parseInt(id));
                    } catch (NumberFormatException e) {
                        pointIds.add(id);
                    }
                } else {
                    // 包含非数字字符的ID保持为字符串
                    pointIds.add(id);
                }
            }
            
            Map<String, Object> request = new HashMap<>();
            request.put("points", pointIds);
            
            String response = qdrantWebClient
                    .post()
                    .uri("/collections/{collection_name}/points/delete", collectionName)
                    .bodyValue(request)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();
                    
            log.debug("Batch delete vectors response: {}", response);
            return true;
        } catch (Exception e) {
            log.error("Failed to batch delete vectors", e);
            return false;
        }
    }

    @Override
    public boolean updatePayload(String collectionName, String id, Map<String, Object> payload) {
        try {
            log.info("Updating payload: collection={}, ID={}, payload={}", collectionName, id, payload);
            
            // 处理ID：如果是数字字符串，转换为整数；否则保持原样（可能是UUID）
            Object pointId = id;
            try {
                // 尝试将字符串ID转换为整数
                pointId = Integer.parseInt(id);
            } catch (NumberFormatException e) {
                // 如果不是数字，保持原字符串（可能是UUID）
                pointId = id;
            }
            
            // 根据Qdrant官方API文档，正确的JSON格式
            Map<String, Object> request = new HashMap<>();
            request.put("payload", payload);
            request.put("points", Collections.singletonList(pointId));
            
            String response = qdrantWebClient
                    .post()
                    .uri("/collections/{collection_name}/points/payload", collectionName)
                    .bodyValue(request)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();
                    
            log.debug("Update payload response: {}", response);
            return true;
        } catch (Exception e) {
            log.error("Failed to update payload", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getCollectionStats(String collectionName) {
        try {
            log.info("Getting collection stats: {}", collectionName);
            
            String response = qdrantWebClient
                    .get()
                    .uri("/collections/{collection_name}", collectionName)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(10))
                    .block();
                    
            JsonNode jsonNode = objectMapper.readTree(response);
            JsonNode result = jsonNode.get("result");
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("status", result.get("status").asText());
            
            JsonNode pointsCount = result.get("points_count");
            if (pointsCount != null) {
                stats.put("pointsCount", pointsCount.asLong());
            }
            
            JsonNode config = result.get("config");
            if (config != null) {
                JsonNode optimizer = config.get("optimizer_config");
                if (optimizer != null) {
                    stats.put("optimizerStatus", "OK");
                }
            }
            
            stats.put("indexedVectorsCount", pointsCount != null ? pointsCount.asLong() : 0);
            stats.put("segmentsCount", 1);
            
            return stats;
        } catch (Exception e) {
            log.error("Failed to get collection stats", e);
            return new HashMap<>();
        }
    }

    /**
     * 将距离字符串转换为Qdrant期望的格式（首字母大写）
     */
    private String capitalizeDistance(String distance) {
        if (distance == null || distance.trim().isEmpty()) {
            return "Cosine"; // 默认值
        }
        
        String normalized = distance.trim().toLowerCase();
        switch (normalized) {
            case "cosine":
                return "Cosine";
            case "dot":
                return "Dot";
            case "euclid":
            case "euclidean":
                return "Euclid";
            case "manhattan":
                return "Manhattan";
            default:
                // 如果是未知的距离类型，尝试首字母大写
                return Character.toUpperCase(normalized.charAt(0)) + normalized.substring(1);
        }
    }

    /**
     * 将简单的key-value过滤器转换为Qdrant标准格式
     * 输入: {"category": "batch_test", "value": 100}
     * 输出: {"must": [{"key": "category", "match": {"value": "batch_test"}}, {"key": "value", "match": {"value": 100}}]}
     */
    private Map<String, Object> convertToQdrantFilter(Map<String, Object> simpleFilter) {
        List<Map<String, Object>> mustConditions = new ArrayList<>();
        
        for (Map.Entry<String, Object> entry : simpleFilter.entrySet()) {
            Map<String, Object> condition = new HashMap<>();
            condition.put("key", entry.getKey());
            
            Map<String, Object> match = new HashMap<>();
            match.put("value", entry.getValue());
            condition.put("match", match);
            
            mustConditions.add(condition);
        }
        
        Map<String, Object> qdrantFilter = new HashMap<>();
        qdrantFilter.put("must", mustConditions);
        
        return qdrantFilter;
    }
    
    @Override
    public List<String> getVectorIdsByPayload(String collectionName, Map<String, Object> payloadConditions) {
        try {
            log.info("查询向量ID，集合: {}, 条件: {}", collectionName, payloadConditions);
            
            // 构建查询请求
            Map<String, Object> request = new HashMap<>();
            request.put("filter", convertToQdrantFilter(payloadConditions));
            request.put("limit", 10000); // 设置较大的限制以获取所有匹配的向量
            request.put("with_payload", false); // 只需要ID，不需要payload
            request.put("with_vector", false); // 不需要向量数据
            
            String response = qdrantWebClient
                    .post()
                    .uri("/collections/{collection_name}/points/scroll", collectionName)
                    .bodyValue(request)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(30))
                    .block();
            
            log.debug("查询向量ID响应: {}", response);
            
            // 解析响应
            JsonNode jsonNode = objectMapper.readTree(response);
            JsonNode result = jsonNode.get("result");
            JsonNode points = result.get("points");
            
            List<String> vectorIds = new ArrayList<>();
            if (points != null && points.isArray()) {
                for (JsonNode point : points) {
                    JsonNode idNode = point.get("id");
                    if (idNode != null) {
                        vectorIds.add(idNode.asText());
                    }
                }
            }
            
            log.info("找到匹配的向量ID数量: {}", vectorIds.size());
            return vectorIds;
            
        } catch (Exception e) {
            log.error("查询向量ID失败，集合: {}, 条件: {}", collectionName, payloadConditions, e);
            return new ArrayList<>();
        }
    }
} 