package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.service.BigmodelPromptsService;
import com.yiyi.ai_train_playground.service.CacheManager;
import com.yiyi.ai_train_playground.service.ConverterServiceByLLM;
import com.yiyi.ai_train_playground.service.SuperBigModelInterface;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 基于大语言模型的转换服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ConverterServiceByLLMImpl implements ConverterServiceByLLM {

    private final BigmodelPromptsService bigmodelPromptsService;
    private final SuperBigModelInterface doubaoBigModelService;
    private final CacheManager cacheManager;

    // 缓存过期时间：1小时
    private static final long CACHE_TTL_HOURS = 1;
    
    @Override
    public String convertText(String prmtTemplateKW, String systemPrompt, String userPrompt) {
        log.info("开始基于LLM的转换处理: prmtTemplateKW={}, systemPrompt长度={}, userPrompt长度={}", 
                prmtTemplateKW, 
                systemPrompt != null ? systemPrompt.length() : 0,
                userPrompt != null ? userPrompt.length() : 0);
        
        try {
            // 一、入参验证
            if (prmtTemplateKW == null || prmtTemplateKW.trim().isEmpty()) {
                throw new IllegalArgumentException("提示词模板关键词不能为空");
            }
            if (systemPrompt == null) {
                throw new IllegalArgumentException("系统提示词不能为null");
            }
            if (userPrompt == null) {
                throw new IllegalArgumentException("用户提示词不能为null");
            }
            
            // 二、调用BigmodelPromptsService.getPromptsByKeyword获取提示词模板（带缓存）
            log.info("获取提示词模板: {}", prmtTemplateKW);
            List<String> promptTemplates = getPromptTemplatesWithCache(prmtTemplateKW);
            
            if (promptTemplates == null || promptTemplates.isEmpty()) {
                log.error("未找到关键词 {} 对应的提示词模板", prmtTemplateKW);
                throw new RuntimeException("未找到提示词模板: " + prmtTemplateKW);
            }
            
            if (promptTemplates.size() < 2) {
                log.error("提示词模板数量不足，期望至少2个，实际获得{}个", promptTemplates.size());
                throw new RuntimeException("提示词模板数量不足，需要至少2个模板（系统提示词模板和用户提示词模板）");
            }
            
            log.info("成功获取{}个提示词模板", promptTemplates.size());
            
            // 2.1 替换第一个元素中的{$systemPrompt}变量
            String systemTemplate = promptTemplates.get(0);
            String sysPrmtReplaced = systemTemplate.replace("{$systemPrompt}", systemPrompt);
            log.info("系统提示词模板替换完成: 原长度={}, 替换后长度={}", 
                    systemTemplate.length(), sysPrmtReplaced.length());
            
            // 2.2 替换第二个元素中的{$userPrompt}变量
            String userTemplate = promptTemplates.get(1);
            String usrPrmtReplaced = userTemplate.replace("{$userPrompt}", userPrompt);
            log.info("用户提示词模板替换完成: 原长度={}, 替换后长度={}", 
                    userTemplate.length(), usrPrmtReplaced.length());
            
            // 三、调用DoubaoBigModelService.ntnsOnce进行处理
            log.info("开始调用豆包大模型进行转换处理");
            String result = doubaoBigModelService.ntnsOnce(sysPrmtReplaced, usrPrmtReplaced);
            
            if (result == null) {
                log.warn("大模型返回结果为null");
                throw new RuntimeException("大模型处理失败，返回结果为null");
            }
            
            log.info("LLM转换处理完成: 结果长度={}", result.length());
            return result;
            
        } catch (IllegalArgumentException e) {
            log.error("参数验证失败: {}", e.getMessage());
            throw e;
        } catch (RuntimeException e) {
            log.error("LLM转换处理失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("LLM转换处理发生未知异常", e);
            throw new RuntimeException("LLM转换处理失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String convertImg(String prmtTemplateKW, String systemPrompt, String userPrompt, String imageUrl) {
        log.info("开始基于LLM的图像转换处理: prmtTemplateKW={}, systemPrompt长度={}, userPrompt长度={}, imageUrl={}",
                prmtTemplateKW,
                systemPrompt != null ? systemPrompt.length() : 0,
                userPrompt != null ? userPrompt.length() : 0,
                imageUrl);

        try {
            // 一、入参验证
            if (prmtTemplateKW == null || prmtTemplateKW.trim().isEmpty()) {
                throw new IllegalArgumentException("提示词模板关键词不能为空");
            }
            if (systemPrompt == null) {
                throw new IllegalArgumentException("系统提示词不能为null");
            }
            if (userPrompt == null) {
                throw new IllegalArgumentException("用户提示词不能为null");
            }
            if (imageUrl == null || imageUrl.trim().isEmpty()) {
                throw new IllegalArgumentException("图像URL不能为空");
            }

            // 二、调用BigmodelPromptsService.getPromptsByKeyword获取提示词模板（带缓存）
            log.info("获取提示词模板: {}", prmtTemplateKW);
            List<String> promptTemplates = getPromptTemplatesWithCache(prmtTemplateKW);

            if (promptTemplates == null || promptTemplates.isEmpty()) {
                log.error("未找到关键词 {} 对应的提示词模板", prmtTemplateKW);
                throw new RuntimeException("未找到提示词模板: " + prmtTemplateKW);
            }

            if (promptTemplates.size() < 2) {
                log.error("提示词模板数量不足，期望至少2个，实际: {}", promptTemplates.size());
                throw new RuntimeException("提示词模板数量不足，需要系统提示词和用户提示词模板");
            }

            // 2.1、完成替换。将数组中第一个元素进行replace,将其中的变量{$systemPrompt}用systemPrompt的值替换
            String sysPrmtTemplate = promptTemplates.get(0);
            String sysPrmtReplaced = sysPrmtTemplate.replace("{$systemPrompt}", systemPrompt);
            log.info("系统提示词模板替换完成: 原长度={}, 替换后长度={}",
                    sysPrmtTemplate.length(), sysPrmtReplaced.length());

            // 2.2、将数组中第二个元素进行replace,将其中的变量{$userPrompt}用userPrompt的值替换
            String usrPrmtTemplate = promptTemplates.get(1);
            String usrPrmtReplaced = usrPrmtTemplate.replace("{$userPrompt}", userPrompt);
            log.info("用户提示词模板替换完成: 原长度={}, 替换后长度={}",
                    usrPrmtTemplate.length(), usrPrmtReplaced.length());

            // 三、调用DoubaoBigModelService.imageChat进行处理
            log.info("开始调用豆包大模型进行图像转换处理");
            String result = doubaoBigModelService.imageChat(imageUrl, usrPrmtReplaced, sysPrmtReplaced);

            if (result == null) {
                log.warn("大模型返回结果为null");
                throw new RuntimeException("大模型处理失败，返回结果为null");
            }

            log.info("LLM图像转换处理完成: 结果长度={}", result.length());
            return result;

        } catch (IllegalArgumentException e) {
            log.error("参数验证失败: {}", e.getMessage());
            throw e;
        } catch (RuntimeException e) {
            log.error("LLM图像转换处理失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("LLM图像转换处理发生未知异常", e);
            throw new RuntimeException("LLM图像转换处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 带缓存的获取提示词模板方法
     *
     * @param prmtTemplateKW 提示词模板关键词
     * @return 提示词模板列表
     */
    @SuppressWarnings("unchecked")
    private List<String> getPromptTemplatesWithCache(String prmtTemplateKW) {
        // 构建缓存键
        String cacheKey = "prompt_templates:" + prmtTemplateKW;

        try {
            // 1. 先从缓存中获取
            log.debug("尝试从缓存获取提示词模板: key={}", cacheKey);
            Object cachedObj = cacheManager.get(cacheKey);

            if (cachedObj != null && cachedObj instanceof List) {
                List<String> templates = (List<String>) cachedObj;
                log.info("从缓存中获取到提示词模板: key={}, 模板数量={}", cacheKey, templates.size());
                return templates;
            }

            // 2. 缓存中没有，从数据库获取
            log.info("缓存中未找到提示词模板，从数据库获取: key={}", cacheKey);
            List<String> promptTemplates = bigmodelPromptsService.getPromptsByKeyword(prmtTemplateKW);

            // 3. 将结果放入缓存
            if (promptTemplates != null && !promptTemplates.isEmpty()) {
                cacheManager.put(cacheKey, promptTemplates, CACHE_TTL_HOURS, TimeUnit.HOURS);
                log.info("提示词模板已缓存: key={}, 模板数量={}, 过期时间={}小时",
                        cacheKey, promptTemplates.size(), CACHE_TTL_HOURS);
            } else {
                log.warn("获取到的提示词模板为空，不进行缓存: key={}", cacheKey);
            }

            return promptTemplates;

        } catch (Exception e) {
            log.error("获取提示词模板时发生异常，降级到直接数据库查询: key={}", cacheKey, e);
            // 缓存操作失败时，降级到直接从数据库获取
            return bigmodelPromptsService.getPromptsByKeyword(prmtTemplateKW);
        }
    }
}
