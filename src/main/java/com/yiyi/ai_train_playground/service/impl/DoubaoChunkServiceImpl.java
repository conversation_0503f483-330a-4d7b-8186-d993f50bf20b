package com.yiyi.ai_train_playground.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.service.BigmodelPromptsService;
import com.yiyi.ai_train_playground.service.DoubaoChunkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class DoubaoChunkServiceImpl implements DoubaoChunkService {
    
    private final DoubaoBigModelServiceImpl doubaoBigModelService;
    private final BigmodelPromptsService bigmodelPromptsService;
    private final ObjectMapper objectMapper;
    
    @Override
    public List<String> chunkText(String toBeChunkedString, Integer chunkSize, Integer overlap) {
        try {
            log.info("开始文本分块，文本长度: {}, 分块大小: {}, 重叠字数: {}", 
                    toBeChunkedString.length(), chunkSize, overlap);
            
            // 1. 获取提示词模板
            List<String> prompts = bigmodelPromptsService.getPromptsByKeyword("chunk:SU");
            if (prompts == null || prompts.size() < 2) {
                throw new RuntimeException("获取chunk提示词失败，期望至少2个提示词");
            }
            
            String systemPrompt = prompts.get(0);
            String userPromptTemplate = prompts.get(1);
            
            // 2. 替换用户提示词模板中的变量
            String userPrompt = userPromptTemplate
                    .replace("{{chunkSize}}", String.valueOf(chunkSize))
                    .replace("{{overlap}}", String.valueOf(overlap))
                    .replace("{{ knowledge_base_raw }}", toBeChunkedString);
            
            log.debug("系统提示词: {}", systemPrompt);
            log.debug("用户提示词: {}", userPrompt);
            
            // 3. 调用豆包大模型
            String respString = doubaoBigModelService.ntnsOnce(systemPrompt, userPrompt);
            log.info("豆包返回结果长度: {}", respString.length());
            log.debug("豆包返回内容: {}", respString);
            
            // 4. 解析JSON数组
            List<String> chunks = objectMapper.readValue(respString, new TypeReference<List<String>>() {});
            
            log.info("文本分块完成，共生成 {} 个块", chunks.size());
            return chunks;
            
        } catch (Exception e) {
            log.error("文本分块失败", e);
            throw new RuntimeException("文本分块失败: " + e.getMessage(), e);
        }
    }
} 