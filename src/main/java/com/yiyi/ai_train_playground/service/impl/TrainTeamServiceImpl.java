package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.entity.TrainTeam;
import com.yiyi.ai_train_playground.entity.TrainTeamShops;
import com.yiyi.ai_train_playground.mapper.TrainTeamMapper;
import com.yiyi.ai_train_playground.mapper.TrainTeamShopsMapper;
import com.yiyi.ai_train_playground.service.TrainTeamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class TrainTeamServiceImpl implements TrainTeamService {

    private final TrainTeamMapper trainTeamMapper;
    private final TrainTeamShopsMapper trainTeamShopsMapper;

    @Autowired
    public TrainTeamServiceImpl(TrainTeamMapper trainTeamMapper, TrainTeamShopsMapper trainTeamShopsMapper) {
        this.trainTeamMapper = trainTeamMapper;
        this.trainTeamShopsMapper = trainTeamShopsMapper;
    }

    @Override
    public TrainTeam getTeamById(Long teamId) {
        return trainTeamMapper.findById(teamId);
    }

    @Override
    public TrainTeam findByShopId(Long shopId) {
        if (shopId == null) {
            log.warn("店铺ID不能为空");
            return null;
        }

        try {
            log.debug("根据店铺ID查询团队信息: shopId={}", shopId);

            // 先根据店铺ID查询团队店铺关系
            TrainTeamShops teamShops = trainTeamShopsMapper.findByShopId(shopId);
            if (teamShops == null) {
                log.debug("未找到店铺ID对应的团队店铺关系: shopId={}", shopId);
                return null;
            }

            // 再根据团队ID查询团队信息
            TrainTeam team = trainTeamMapper.findById(teamShops.getTeamId());
            log.debug("根据店铺ID查询团队信息完成: shopId={}, teamId={}, 找到={}",
                    shopId, teamShops.getTeamId(), team != null);
            return team;
        } catch (Exception e) {
            log.error("根据店铺ID查询团队信息失败: shopId={}", shopId, e);
            return null;
        }
    }

    @Override
    public List<TrainTeam> findAll() {
        try {
            log.debug("查询所有团队信息");
            List<TrainTeam> teams = trainTeamMapper.findAll();
            log.debug("查询所有团队信息完成，找到 {} 个团队", teams.size());
            return teams;
        } catch (Exception e) {
            log.error("查询所有团队信息失败", e);
            return new ArrayList<>();
        }
    }
}