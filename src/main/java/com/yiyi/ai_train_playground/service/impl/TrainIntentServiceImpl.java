package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.dto.IntentDTO;
import com.yiyi.ai_train_playground.dto.IntentGroupDTO;
import com.yiyi.ai_train_playground.entity.TrainIntent;
import com.yiyi.ai_train_playground.mapper.TrainIntentMapper;
import com.yiyi.ai_train_playground.service.TrainIntentService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class TrainIntentServiceImpl implements TrainIntentService {

    private final TrainIntentMapper trainIntentMapper;

    public TrainIntentServiceImpl(TrainIntentMapper trainIntentMapper) {
        this.trainIntentMapper = trainIntentMapper;
    }

    @Override
    public List<IntentGroupDTO> getIntentGroups() {
        // 获取所有父级意图
        List<TrainIntent> parentIntents = trainIntentMapper.findParentIntents();
        List<IntentGroupDTO> result = new ArrayList<>();

        // 遍历父级意图，构建返回数据结构
        for (TrainIntent parent : parentIntents) {
            IntentGroupDTO groupDTO = new IntentGroupDTO();
            groupDTO.setName(parent.getName());

            // 获取子意图
            List<TrainIntent> children = trainIntentMapper.findChildIntents(parent.getId());
            List<IntentDTO> childDTOs = new ArrayList<>();

            // 转换子意图为DTO
            for (TrainIntent child : children) {
                IntentDTO intentDTO = new IntentDTO();
                intentDTO.setId(child.getId());
                intentDTO.setName(child.getName());
                intentDTO.setDescription(child.getDescription());
                intentDTO.setCreateTime(child.getCreateTime());
                intentDTO.setUpdateTime(child.getUpdateTime());
                childDTOs.add(intentDTO);
            }

            groupDTO.setSubArr(childDTOs);
            result.add(groupDTO);
        }

        return result;
    }
} 