package com.yiyi.ai_train_playground.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.yiyi.ai_train_playground.config.OssConfig;
import com.yiyi.ai_train_playground.enums.FileType;
import com.yiyi.ai_train_playground.service.OssService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class OssServiceImpl implements OssService {
    private final OSS ossClient;
    private final OssConfig ossConfig;
    private static final String TEMP_PATH = "temp/";
    private static final String PROC_PATH = "proc/";

    public OssServiceImpl(OSS ossClient, OssConfig ossConfig) {
        this.ossClient = ossClient;
        this.ossConfig = ossConfig;
    }

    @Override
    public String upload(MultipartFile file, Integer type, Long teamId) {
        try {
            if (file == null || file.isEmpty()) {
                throw new RuntimeException("上传文件不能为空");
            }
            if (teamId == null) {
                throw new RuntimeException("团队ID不能为空");
            }

            FileType fileType = FileType.getByCode(type);
            if (fileType == null) {
                throw new RuntimeException("文件类型不正确");
            }

            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.trim().isEmpty()) {
                throw new RuntimeException("文件名不能为空");
            }

            // 构建新的文件名：前缀_teamId_时间戳_原文件名
            String timestamp = String.valueOf(System.currentTimeMillis());
            String fileName = fileType.getPrefix() + teamId + "_" + timestamp + "_" + originalFilename;
            
            // 构建OSS文件路径
            String ossFilePath = TEMP_PATH + fileType.getPath() + fileName;
            
            // 创建上传文件的元信息
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(file.getContentType());
            metadata.setContentLength(file.getSize());
            
            // 设置文件过期时间
            Date expiration = new Date(System.currentTimeMillis() + ossConfig.getTempFileExpiration() * 3600 * 1000);
            metadata.setExpirationTime(expiration);

            // 上传文件
            PutObjectRequest putObjectRequest = new PutObjectRequest(ossConfig.getBucketName(), ossFilePath, file.getInputStream(), metadata);
            ossClient.putObject(putObjectRequest);
            
            // 设置文件访问权限为公共读
            ossClient.setObjectAcl(ossConfig.getBucketName(), ossFilePath, CannedAccessControlList.PublicRead);

            // 生成访问URL
            return String.format("https://%s.%s/%s", ossConfig.getBucketName(), ossConfig.getEndpoint(), ossFilePath);
        } catch (IOException e) {
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<String> geneProc(String[] fileNames) {
        List<String> urls = new ArrayList<>();
        for (String fileName : fileNames) {
            try {
                // 根据文件名前缀判断文件类型和路径
                FileType fileType = null;
                for (FileType type : FileType.values()) {
                    if (fileName.startsWith(type.getPrefix())) {
                        fileType = type;
                        break;
                    }
                }
                
                if (fileType == null) {
                    throw new RuntimeException("无效的文件名前缀: " + fileName);
                }

                String sourcePath = TEMP_PATH + fileType.getPath() + fileName;
                String targetPath = PROC_PATH + fileName;

                // 复制文件到proc目录
                ossClient.copyObject(ossConfig.getBucketName(), sourcePath, ossConfig.getBucketName(), targetPath);
                
                // 删除临时文件
                ossClient.deleteObject(ossConfig.getBucketName(), sourcePath);
                
                // 设置永久文件的访问权限为公共读
                ossClient.setObjectAcl(ossConfig.getBucketName(), targetPath, CannedAccessControlList.PublicRead);

                // 生成永久文件的访问URL
                String url = String.format("https://%s.%s/%s", ossConfig.getBucketName(), ossConfig.getEndpoint(), targetPath);
                urls.add(url);
            } catch (Exception e) {
                throw new RuntimeException("处理文件失败: " + fileName + ", 错误: " + e.getMessage(), e);
            }
        }
        return urls;
    }
} 