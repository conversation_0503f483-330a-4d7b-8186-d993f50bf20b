package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.dto.ProductListDTO;
import com.yiyi.ai_train_playground.dto.ProductDetailDTO;
import com.yiyi.ai_train_playground.dto.ProductUpdateDTO;
import com.yiyi.ai_train_playground.dto.PageRequest;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.mapper.TrainProductMapper;
import com.yiyi.ai_train_playground.service.ProductService;
import com.yiyi.ai_train_playground.service.jd.AsyncProductProcessingService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
public class ProductServiceImpl implements ProductService {
    
    @Autowired
    private TrainProductMapper trainProductMapper;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    private AsyncProductProcessingService asyncProductProcessingService;
    
    @Override
    public List<ProductListDTO> getProductList(String externalProductName, String token) {
        // 从JWT中获取团队ID
        Long teamId = jwtUtil.getTeamIdFromToken(token);
        if (teamId == null) {
            throw new RuntimeException("无法获取团队信息");
        }
        
        log.info("查询商品列表：teamId={}, externalProductName={}", teamId, externalProductName);
        
        return trainProductMapper.findProductList(teamId, externalProductName);
    }
    
    @Override
    public PageResult<ProductListDTO> getProductListWithPage(String externalProductName, PageRequest pageRequest, String token) {
        // 从JWT中获取团队ID
        Long teamId = jwtUtil.getTeamIdFromToken(token);
        if (teamId == null) {
            throw new RuntimeException("无法获取团队信息");
        }
        
        log.info("分页查询商品列表：teamId={}, externalProductName={}, page={}, size={}", 
                teamId, externalProductName, pageRequest.getPage(), pageRequest.getSize());
        
        // 查询总数
        Long total = trainProductMapper.countProducts(teamId, externalProductName);
        
        // 查询分页数据
        List<ProductListDTO> records = trainProductMapper.findProductListWithPage(
                teamId, 
                externalProductName, 
                pageRequest.getOffset(), 
                pageRequest.getSize(),
                pageRequest.getSort(),
                pageRequest.getOrder()
        );
        
        return new PageResult<>(records, total, pageRequest.getPage(), pageRequest.getSize());
    }
    
    @Override
    public ProductDetailDTO getProductById(Long productId, String token) {
        if (productId == null) {
            throw new RuntimeException("商品ID不能为空");
        }
        
        // 从JWT中获取团队ID
        Long teamId = jwtUtil.getTeamIdFromToken(token);
        if (teamId == null) {
            throw new RuntimeException("无法获取团队信息");
        }
        
        log.info("查询商品详情：productId={}, teamId={}", productId, teamId);
        
        ProductDetailDTO product = trainProductMapper.findProductById(productId, teamId);
        if (product == null) {
            throw new RuntimeException("商品不存在或无权限访问");
        }
        
        return product;
    }
    
    @Override
    @Transactional
    public void updateProduct(ProductUpdateDTO productUpdateDTO, String token) {
        if (productUpdateDTO.getId() == null) {
            throw new RuntimeException("商品ID不能为空");
        }
        
        // 从JWT中获取团队ID和用户ID
        Long teamId = jwtUtil.getTeamIdFromToken(token);
        Long userId = jwtUtil.getUserIdFromToken(token);
        if (teamId == null) {
            throw new RuntimeException("无法获取团队信息");
        }
        
        // 检查商品是否存在
        if (trainProductMapper.existsById(productUpdateDTO.getId(), teamId) == 0) {
            throw new RuntimeException("商品不存在或无权限修改");
        }
        
        log.info("更新商品：productId={}, teamId={}, userId={}", productUpdateDTO.getId(), teamId, userId);
        
        // 更新商品信息
        int affectedRows = trainProductMapper.updateProduct(
                productUpdateDTO.getId(),
                teamId,
                productUpdateDTO.getExternalProductId(),
                productUpdateDTO.getExternalProductName(),
                productUpdateDTO.getExternalProductLink(),
                productUpdateDTO.getExternalProductImage(),
                productUpdateDTO.getExternalProductDetail(),
                productUpdateDTO.getExternalProductDetailCleaned(),
                productUpdateDTO.getStatus(),
                productUpdateDTO.getCategory(),
                productUpdateDTO.getTags(),
                productUpdateDTO.getPlatform(),
                productUpdateDTO.getShopId(),
                productUpdateDTO.getShopName(),
                userId != null ? userId.toString() : "unknown"
        );
        
        if (affectedRows == 0) {
            throw new RuntimeException("更新商品失败");
        }
        
        log.info("商品更新成功：productId={}", productUpdateDTO.getId());
    }
    
    @Override
    @Transactional
    public void deleteProduct(Long productId, String token) {
        if (productId == null) {
            throw new RuntimeException("商品ID不能为空");
        }
        
        // 从JWT中获取团队ID
        Long teamId = jwtUtil.getTeamIdFromToken(token);
        if (teamId == null) {
            throw new RuntimeException("无法获取团队信息");
        }
        
        // 检查商品是否存在
        if (trainProductMapper.existsById(productId, teamId) == 0) {
            throw new RuntimeException("商品不存在或无权限删除");
        }
        
        log.info("删除商品：productId={}, teamId={}", productId, teamId);
        
        int affectedRows = trainProductMapper.deleteProduct(productId, teamId);
        if (affectedRows == 0) {
            throw new RuntimeException("删除商品失败");
        }
        
        log.info("商品删除成功：productId={}", productId);
    }
    
    @Override
    @Transactional
    public void updateProductWithAsyncProcessing(ProductUpdateDTO productUpdateDTO, String token) {
        // 1. 先正常更新商品信息
        updateProduct(productUpdateDTO, token);
        
        // 2. 启动异步向量化处理
        if (productUpdateDTO.getExternalProductDetail() != null && 
            !productUpdateDTO.getExternalProductDetail().trim().isEmpty()) {
            
            // 从JWT中获取团队ID
            Long teamId = jwtUtil.getTeamIdFromToken(token);
            
            log.info("启动异步商品详情处理，商品ID: {}, 团队ID: {}", 
                    productUpdateDTO.getExternalProductId(), teamId);
            
            asyncProductProcessingService.processProductDetailAsync(
                    productUpdateDTO.getExternalProductId(),
                    teamId != null ? teamId.toString() : "unknown",
                    productUpdateDTO.getExternalProductDetail()
            );
        } else {
            log.warn("商品详情为空，跳过异步处理，商品ID: {}", productUpdateDTO.getExternalProductId());
        }
    }
    
    @Override
    @Transactional
    public void updateLearningStatus(String externalProductId, Integer learningStatus, String cleanedDetail, String token) {
        if (externalProductId == null || externalProductId.trim().isEmpty()) {
            throw new RuntimeException("外部商品ID不能为空");
        }
        
        // 从JWT中获取团队ID和用户ID
        Long teamId = jwtUtil.getTeamIdFromToken(token);
        Long userId = jwtUtil.getUserIdFromToken(token);
        if (teamId == null) {
            throw new RuntimeException("无法获取团队信息");
        }
        
        log.info("更新商品学习状态：externalProductId={}, learningStatus={}, teamId={}", 
                externalProductId, learningStatus, teamId);
        
        // 更新学习状态和清理后的详情
        int affectedRows = trainProductMapper.updateLearningStatus(
                externalProductId,
                teamId,
                learningStatus,
                cleanedDetail,
                userId != null ? userId.toString() : "system"
        );
        
        if (affectedRows == 0) {
            throw new RuntimeException("更新商品学习状态失败，未找到匹配的商品");
        }
        
        log.info("商品学习状态更新成功：externalProductId={}, learningStatus={}", externalProductId, learningStatus);
    }
} 