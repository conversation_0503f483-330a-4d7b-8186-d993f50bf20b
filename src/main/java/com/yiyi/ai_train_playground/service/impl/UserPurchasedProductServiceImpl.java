package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.entity.UserPurchasedProduct;
import com.yiyi.ai_train_playground.mapper.UserPurchasedProductMapper;
import com.yiyi.ai_train_playground.service.UserPurchasedProductService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class UserPurchasedProductServiceImpl implements UserPurchasedProductService {

    private final UserPurchasedProductMapper userPurchasedProductMapper;

    public UserPurchasedProductServiceImpl(UserPurchasedProductMapper userPurchasedProductMapper) {
        this.userPurchasedProductMapper = userPurchasedProductMapper;
    }

    @Override
    @Transactional
    public List<UserPurchasedProduct> findByTeamId(Long teamId) {
        return userPurchasedProductMapper.findByTeamId(teamId);
    }
} 