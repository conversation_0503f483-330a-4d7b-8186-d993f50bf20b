package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.entity.TrainTeamShops;
import com.yiyi.ai_train_playground.entity.TrainTeamShopWithToken;
import com.yiyi.ai_train_playground.mapper.TrainTeamShopsMapper;
import com.yiyi.ai_train_playground.service.TrainTeamShopsService;

import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 团队店铺服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrainTeamShopsServiceImpl implements TrainTeamShopsService {
    
    private final TrainTeamShopsMapper trainTeamShopsMapper;
    
    @Override
    public TrainTeamShops findByTeamIdAndShopType(Long teamId, Integer shopType) {
        if (teamId == null || shopType == null) {
            log.warn("团队ID和店铺类型不能为空: teamId={}, shopType={}", teamId, shopType);
            return null;
        }
        
        try {
            log.debug("根据团队ID和店铺类型查询店铺信息: teamId={}, shopType={}", teamId, shopType);
            TrainTeamShops shop = trainTeamShopsMapper.findByTeamIdAndShopType(teamId, shopType);
            log.debug("根据团队ID和店铺类型查询店铺信息完成: teamId={}, shopType={}, 找到={}", 
                    teamId, shopType, shop != null);
            return shop;
        } catch (Exception e) {
            log.error("根据团队ID和店铺类型查询店铺信息失败: teamId={}, shopType={}", teamId, shopType, e);
            return null;
        }
    }
    
    @Override
    public TrainTeamShops findByShopId(Long shopId) {
        if (shopId == null) {
            log.warn("店铺ID不能为空");
            return null;
        }
        
        try {
            log.debug("根据店铺ID查询店铺信息: shopId={}", shopId);
            TrainTeamShops shop = trainTeamShopsMapper.findByShopId(shopId);
            log.debug("根据店铺ID查询店铺信息完成: shopId={}, 找到={}", shopId, shop != null);
            return shop;
        } catch (Exception e) {
            log.error("根据店铺ID查询店铺信息失败: shopId={}", shopId, e);
            return null;
        }
    }
    
    @Override
    public boolean insert(TrainTeamShops trainTeamShops) {
        if (trainTeamShops == null) {
            log.warn("团队店铺信息不能为空");
            return false;
        }
        
        try {
            log.debug("插入团队店铺信息: teamId={}, shopId={}, shopType={}", 
                    trainTeamShops.getTeamId(), trainTeamShops.getShopId(), trainTeamShops.getShopType());
            int result = trainTeamShopsMapper.insert(trainTeamShops);
            boolean success = result > 0;
            log.debug("插入团队店铺信息完成: teamId={}, shopId={}, 结果={}", 
                    trainTeamShops.getTeamId(), trainTeamShops.getShopId(), success);
            return success;
        } catch (Exception e) {
            log.error("插入团队店铺信息失败: teamId={}, shopId={}", 
                    trainTeamShops.getTeamId(), trainTeamShops.getShopId(), e);
            return false;
        }
    }
    
    @Override
    public boolean updateAuthorizationStatus(Long teamId, Integer shopType, Boolean isAuthorize, 
                                           Integer isSyncComplete, String updater) {
        if (teamId == null || shopType == null) {
            log.warn("团队ID和店铺类型不能为空: teamId={}, shopType={}", teamId, shopType);
            return false;
        }
        
        try {
            log.debug("更新店铺授权状态和同步状态: teamId={}, shopType={}, isAuthorize={}, isSyncComplete={}", 
                    teamId, shopType, isAuthorize, isSyncComplete);
            int result = trainTeamShopsMapper.updateAuthorizationStatus(teamId, shopType, isAuthorize, 
                    isSyncComplete, updater);
            boolean success = result > 0;
            log.debug("更新店铺授权状态和同步状态完成: teamId={}, shopType={}, 结果={}", 
                    teamId, shopType, success);
            return success;
        } catch (Exception e) {
            log.error("更新店铺授权状态和同步状态失败: teamId={}, shopType={}", teamId, shopType, e);
            return false;
        }
    }
    
    @Override
    public boolean updateByShopId(Long shopId, Boolean isAuthorize, Integer isSyncComplete, String updater) {
        if (shopId == null) {
            log.warn("店铺ID不能为空");
            return false;
        }
        
        try {
            log.debug("根据店铺ID更新授权状态和同步状态: shopId={}, isAuthorize={}, isSyncComplete={}", 
                    shopId, isAuthorize, isSyncComplete);
            int result = trainTeamShopsMapper.updateByShopId(shopId, isAuthorize, isSyncComplete, updater);
            boolean success = result > 0;
            log.debug("根据店铺ID更新授权状态和同步状态完成: shopId={}, 结果={}", shopId, success);
            return success;
        } catch (Exception e) {
            log.error("根据店铺ID更新授权状态和同步状态失败: shopId={}", shopId, e);
            return false;
        }
    }
    
    @Override
    public boolean updateEntityByShopId(TrainTeamShops trainTeamShops) {
        if (trainTeamShops == null || trainTeamShops.getShopId() == null) {
            log.warn("团队店铺信息或店铺ID不能为空");
            return false;
        }
        
        try {
            log.debug("根据店铺ID更新团队店铺信息: shopId={}, teamId={}", 
                    trainTeamShops.getShopId(), trainTeamShops.getTeamId());
            int result = trainTeamShopsMapper.updateEntityByShopId(trainTeamShops);
            boolean success = result > 0;
            log.debug("根据店铺ID更新团队店铺信息完成: shopId={}, 结果={}", 
                    trainTeamShops.getShopId(), success);
            return success;
        } catch (Exception e) {
            log.error("根据店铺ID更新团队店铺信息失败: shopId={}", trainTeamShops.getShopId(), e);
            return false;
        }
    }
    
    @Override
    public boolean existsByShopId(Long shopId) {
        TrainTeamShops shop = findByShopId(shopId);
        return shop != null;
    }
    
    @Override
    public boolean saveOrUpdate(TrainTeamShops trainTeamShops) {
        if (trainTeamShops == null || trainTeamShops.getShopId() == null) {
            log.warn("团队店铺信息或店铺ID不能为空");
            return false;
        }
        
        try {
            // 检查是否已存在
            TrainTeamShops existingShop = findByShopId(trainTeamShops.getShopId());
            if (existingShop != null) {
                // 更新现有记录
                log.debug("更新现有团队店铺信息: shopId={}", trainTeamShops.getShopId());
                return updateEntityByShopId(trainTeamShops);
            } else {
                // 插入新记录
                log.debug("插入新团队店铺信息: shopId={}", trainTeamShops.getShopId());
                return insert(trainTeamShops);
            }
        } catch (Exception e) {
            log.error("保存或更新团队店铺信息失败: shopId={}", trainTeamShops.getShopId(), e);
            return false;
        }
    }

    @Override
    public TrainTeamShopWithToken selectShopAndTokenByShopId(String shopId) {
        if (shopId == null || shopId.trim().isEmpty()) {
            log.warn("店铺ID不能为空");
            return null;
        }

        try {
            log.debug("根据店铺ID查询店铺信息及对应的京东token信息: shopId={}", shopId);
            TrainTeamShopWithToken result = trainTeamShopsMapper.selectShopAndTokenByShopId(shopId);
            log.debug("根据店铺ID查询店铺信息及对应的京东token信息完成: shopId={}, 找到={}",
                    shopId, result != null);
            return result;
        } catch (Exception e) {
            log.error("根据店铺ID查询店铺信息及对应的京东token信息失败: shopId={}", shopId, e);
            return null;
        }
    }

    @Override
    public List<TrainTeamShopWithToken> selectShopAndTokenByTeamId(String teamId) {
        if (teamId == null || teamId.trim().isEmpty()) {
            log.warn("团队ID不能为空");
            return null;
        }

        try {
            log.debug("根据团队ID查询店铺列表信息及对应的京东token信息: teamId={}", teamId);
            List<TrainTeamShopWithToken> result = trainTeamShopsMapper.selectShopAndTokenByShopIdlist(teamId);
            log.debug("根据团队ID查询店铺列表信息及对应的京东token信息完成: teamId={}, 找到数量={}",
                    teamId, result != null ? result.size() : 0);
            return result;
        } catch (Exception e) {
            log.error("根据团队ID查询店铺列表信息及对应的京东token信息失败: teamId={}", teamId, e);
            return null;
        }
    }
}
