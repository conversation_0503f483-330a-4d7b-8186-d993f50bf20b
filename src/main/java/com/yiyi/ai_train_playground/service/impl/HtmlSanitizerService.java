package com.yiyi.ai_train_playground.service.impl;


import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Safelist;
import org.owasp.html.HtmlPolicyBuilder;
import org.owasp.html.PolicyFactory;
import org.springframework.stereotype.Service;
import org.apache.commons.text.StringEscapeUtils;

@Service
public class HtmlSanitizerService {

    // 组合清洗策略
    public String sanitizeAndExtractText(String html) {
        // 1. 解码HTML实体（处理嵌套编码）
        String decodedHtml = decodeHtmlEntities(html);
        
        // 2. Jsoup基础清洗
        String jsoupCleaned = jsoupSanitize(decodedHtml);

        // 3. OWASP深度清洗
        String owaspCleaned = owaspSanitize(jsoupCleaned);

        // 4. 转换为纯文本并优化
        return htmlToPlainText(owaspCleaned);
    }
    
    /**
     * 递归解码HTML实体编码
     */
    private String decodeHtmlEntities(String html) {
        if (html == null || html.isEmpty()) {
            return html;
        }
        
        String decoded = html;
        String previous;
        
        // 递归解码直到没有更多的HTML实体
        do {
            previous = decoded;
            // 使用Apache Commons Text进行HTML实体解码
            decoded = StringEscapeUtils.unescapeHtml4(decoded);
        } while (!decoded.equals(previous) && decoded.contains("&"));
        
        return decoded;
    }

    private String jsoupSanitize(String html) {
        // 使用更宽松的Safelist，允许更多元素用于文本提取
        Safelist safelist = Safelist.relaxed()
                .addTags("span", "div", "u") // 添加更多文本标签
                .addAttributes("img", "width", "height", "alt")
                .preserveRelativeLinks(false)
                .removeProtocols("a", "href", "javascript");

        return Jsoup.clean(html, safelist);
    }

    private String owaspSanitize(String html) {
        PolicyFactory policy = new HtmlPolicyBuilder()
                .allowElements("h1", "h2", "h3", "p", "ul", "ol", "li", "b", "strong", "i", "em", "br", "img", "span", "div", "u")
                .allowAttributes("src", "alt", "width", "height").onElements("img")
                .allowTextIn("script", "style") // 禁止执行但允许文本内容
                .toFactory();

        return policy.sanitize(html);
    }

    public String htmlToPlainText(String html) {
        // 1. 使用Jsoup解析为文档
        Document doc = Jsoup.parse(html);

        // 2. 提取纯文本
        String text = doc.text();

        // 3. 优化文本格式
        return text.replaceAll("\\s+", " ")  // 合并连续空格
                .replaceAll("\\p{C}", "") // 移除控制字符
                .trim();
    }
}