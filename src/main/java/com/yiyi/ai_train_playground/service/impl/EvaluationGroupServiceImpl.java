package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.dto.AllGroupsHead;
import com.yiyi.ai_train_playground.dto.OfficialGroupHead;
import com.yiyi.ai_train_playground.dto.EvaluationGroupDTO;
import com.yiyi.ai_train_playground.entity.EvaluationGroup;
import com.yiyi.ai_train_playground.mapper.EvaluationGroupMapper;
import com.yiyi.ai_train_playground.service.EvaluationGroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EvaluationGroupServiceImpl implements EvaluationGroupService {

    private final EvaluationGroupMapper evaluationGroupMapper;

    public EvaluationGroupServiceImpl(EvaluationGroupMapper evaluationGroupMapper) {
        this.evaluationGroupMapper = evaluationGroupMapper;
    }

    @Override
    public Map<String, Object> getGroupTree(String groupTitle, Long teamId) {
        // 获取所有分组
        List<EvaluationGroup> allGroups = evaluationGroupMapper.selectList(groupTitle, teamId);
        
        // 创建根节点 "全部评价"
        EvaluationGroup allGroupsHead = createRootGroup();
        
        // 创建ID到分组的映射，同时去重
        Map<Long, EvaluationGroup> groupMap = allGroups.stream()
            .collect(Collectors.toMap(
                EvaluationGroup::getId,
                group -> {
                    group.setSubGroups(new ArrayList<>());
                    return group;
                },
                (existing, replacement) -> {
                    if (existing.getFullPath() != null && replacement.getFullPath() != null &&
                        existing.getFullPath().length() <= replacement.getFullPath().length()) {
                        return existing;
                    }
                    return replacement;
                }
            ));

        // 构建树形结构
        List<EvaluationGroup> rootGroups = new ArrayList<>();
        for (EvaluationGroup group : groupMap.values()) {
            if (group.getParentId() == null) {
                rootGroups.add(group);
            } else {
                EvaluationGroup parent = groupMap.get(group.getParentId());
                if (parent != null) {
                    parent.getSubGroups().add(group);
                }
            }
        }

        // 按类型分组：官方预设、用户自定义、未分组
        Map<String, List<EvaluationGroup>> groupedByType = rootGroups.stream()
            .collect(Collectors.groupingBy(group -> {
                if (Boolean.TRUE.equals(group.getIsOfficial())) {
                    return "official";
                } else if (group.getTeamId() != null && group.getTeamId() > 0) {
                    return "user";
                } else {
                    return "unGrouped";
                }
            }));

        // 按指定顺序添加到根节点：1.未分组 2.用户自定义分组 3.官方预设分组
        List<EvaluationGroup> orderedGroups = new ArrayList<>();
        // 1. 未分组
        orderedGroups.addAll(groupedByType.getOrDefault("unGrouped", Collections.emptyList()));
        // 2. 用户自定义分组
        List<EvaluationGroup> userGroups = groupedByType.getOrDefault("user", Collections.emptyList());
        if (!userGroups.isEmpty()) {
            userGroups.sort((g1, g2) -> {
                // 按创建时间倒序
                LocalDateTime time1 = g1.getCreateTime();
                LocalDateTime time2 = g2.getCreateTime();
                if (time1 == null && time2 == null) return 0;
                if (time1 == null) return 1;
                if (time2 == null) return -1;
                return time2.compareTo(time1);
            });
            orderedGroups.addAll(userGroups);
        }
        // 3. 官方预设分组
        List<EvaluationGroup> officialGroups = groupedByType.getOrDefault("official", Collections.emptyList());
        if (!officialGroups.isEmpty()) {
            officialGroups.sort((g1, g2) -> {
                // 按创建时间倒序
                LocalDateTime time1 = g1.getCreateTime();
                LocalDateTime time2 = g2.getCreateTime();
                if (time1 == null && time2 == null) return 0;
                if (time1 == null) return 1;
                if (time2 == null) return -1;
                return time2.compareTo(time1);
            });
            orderedGroups.addAll(officialGroups);
        }

        // 递归排序子分组
        sortSubGroupsRecursively(orderedGroups);

        allGroupsHead.setSubGroups(orderedGroups);
        
        // 转换为DTO并返回
        EvaluationGroupDTO rootDTO = EvaluationGroupDTO.fromEntity(allGroupsHead);
        return Collections.singletonMap("groups", Collections.singletonList(rootDTO));
    }

    private void sortSubGroupsRecursively(List<EvaluationGroup> groups) {
        for (EvaluationGroup group : groups) {
            if (group.getSubGroups() != null && !group.getSubGroups().isEmpty()) {
                group.getSubGroups().sort((g1, g2) -> {
                    LocalDateTime time1 = g1.getCreateTime();
                    LocalDateTime time2 = g2.getCreateTime();
                    if (time1 == null && time2 == null) return 0;
                    if (time1 == null) return 1;
                    if (time2 == null) return -1;
                    return time2.compareTo(time1);
                });
                sortSubGroupsRecursively(group.getSubGroups());
            }
        }
    }

    private EvaluationGroup createRootGroup() {
        EvaluationGroup root = new EvaluationGroup();
        root.setId(-1L);
        root.setTeamId(0L);
        root.setGroupTitle("全部评价");
        root.setIsOfficial(false);
        root.setSubGroups(new ArrayList<>());
        return root;
    }

    @Override
    @Transactional
    public boolean save(EvaluationGroup evaluationGroup) {
        return evaluationGroupMapper.insert(evaluationGroup) > 0;
    }

    @Override
    @Transactional
    public boolean update(EvaluationGroup evaluationGroup) {
        return evaluationGroupMapper.update(evaluationGroup) > 0;
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids, Long teamId) {
        return evaluationGroupMapper.deleteByIds(ids, teamId) > 0;
    }

    @Override
    public List<EvaluationGroup> getEvaluationGroups(String groupTitle, Long teamId) {
        return evaluationGroupMapper.selectList(groupTitle, teamId);
    }
}
