package com.yiyi.ai_train_playground.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.yiyi.ai_train_playground.entity.TrainTeam;
import com.yiyi.ai_train_playground.model.ScriptChatMessage;
import com.yiyi.ai_train_playground.service.ScriptChatService;
import com.yiyi.ai_train_playground.service.TrainTeamService;
import com.yiyi.ai_train_playground.service.SuperBigModelInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.websocket.Session;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ScriptChatServiceImpl implements ScriptChatService {

    private final SuperBigModelInterface doubaiBigModelService;
    private final ObjectMapper objectMapper;
    private final TrainTeamService trainTeamService;
    
    private static final String ROBOT_NAME = "试用剧本";

    public ScriptChatServiceImpl(SuperBigModelInterface doubaiBigModelService, 
                               ObjectMapper objectMapper,
                               TrainTeamService trainTeamService) {
        this.doubaiBigModelService = doubaiBigModelService;
        this.objectMapper = objectMapper;
        this.trainTeamService = trainTeamService;
    }

    @Override
    public void handleInitMessage(ScriptChatMessage message, Session session) {
        try {
            // 构建系统消息
            String systemMessage = buildSystemMessage(message);
            log.info("构建的系统消息: {}", systemMessage);
            
            // 发送会话ID给客户端
            Map<String, Object> sessionInfo = new HashMap<>();
            sessionInfo.put("type", "SESSION_INFO");
            sessionInfo.put("sessionId", message.getSessionId());
            
            try {
                if (session.isOpen()) {
                    session.getBasicRemote().sendText(objectMapper.writeValueAsString(sessionInfo));
                }
            } catch (IOException e) {
                log.error("发送会话ID失败", e);
            }
            
            // 获取产品列表和团队信息
            final List<Map<String, Object>> productInfoList = new ArrayList<>();
            if (message.getProductList() != null && !message.getProductList().isEmpty()) {
                for (Map<String, Object> product : message.getProductList()) {
                    Map<String, Object> productInfo = new HashMap<>();
                    productInfo.put("productId", product.get("externalProductId"));
                    productInfo.put("productName", product.get("externalProductName"));
                    productInfo.put("productLink", product.get("externalProductLink"));
                    productInfo.put("productImage", product.get("externalProductImage"));
                    productInfoList.add(productInfo);
                }
            }
            
            // 获取团队名称
            final String teamName = getTeamName(message.getTeamId());
            
            // 创建消息列表用于调用大模型
            List<ChatMessage> messages = new ArrayList<>();
            messages.add(ChatMessage.builder()
                    .role(ChatMessageRole.SYSTEM)
                    .content(systemMessage)
                    .build());
            messages.add(ChatMessage.builder()
                    .role(ChatMessageRole.USER)
                    .content("请开始对话")
                    .build());
            
            // 使用豆包大模型服务生成首次回复，使用nts提高速度
            doubaiBigModelService.nts(messages)
                .subscribe(
                    response -> {
                        try {
                            if (session.isOpen()) {
                                // 解析响应
                                try {
                                    JsonNode responseNode = objectMapper.readTree(response);
                                    String content = responseNode.has("content") ? responseNode.get("content").asText() : response;
                                    int progress = responseNode.has("progress") ? responseNode.get("progress").asInt() : 0;
                                    
                                    // 构建返回给客户端的消息
                                    Map<String, Object> clientResponse = new HashMap<>();
                                    clientResponse.put("type", "ROBOT_MESSAGE");
                                    clientResponse.put("content", content);
                                    clientResponse.put("progress", progress);
                                    clientResponse.put("robotName", ROBOT_NAME);
                                    clientResponse.put("teamName", teamName);
                                    
                                    // 仅第一次消息发送产品列表
                                    if (progress <= 5 && !productInfoList.isEmpty()) {
                                        clientResponse.put("productList", productInfoList);
                                    }
                                    
                                    session.getBasicRemote().sendText(objectMapper.writeValueAsString(clientResponse));
                                } catch (Exception e) {
                                    // 如果解析失败，直接发送原始响应
                                    session.getBasicRemote().sendText(response);
                                }
                            }
                        } catch (IOException e) {
                            log.error("发送消息失败", e);
                        }
                    },
                    error -> log.error("调用豆包大模型失败", error),
                    () -> log.info("首次消息生成完成")
                );
        } catch (Exception e) {
            log.error("处理初始化消息失败", e);
        }
    }

    @Override
    public void handleUserMessage(ScriptChatMessage message, Session session) {
        try {
            // 获取用户消息内容
            String content = message.getContent();
            String sessionId = message.getSessionId();
            
            if (content == null || content.trim().isEmpty()) {
                log.warn("用户消息内容为空");
                return;
            }
            
            if (sessionId == null || sessionId.trim().isEmpty()) {
                log.warn("会话ID为空");
                return;
            }
            
            // 获取团队名称
            final String teamName = getTeamName(message.getTeamId());
            
            // 创建简单的消息列表用于调用大模型（重新构建系统提示词）
            List<ChatMessage> messages = new ArrayList<>();
            String systemMessage = buildSystemMessage(message);
            messages.add(ChatMessage.builder()
                    .role(ChatMessageRole.SYSTEM)
                    .content(systemMessage)
                    .build());
            messages.add(ChatMessage.builder()
                    .role(ChatMessageRole.USER)
                    .content(content)
                    .build());
            
            // 调用豆包大模型服务
            doubaiBigModelService.nts(messages)
                .subscribe(
                    response -> {
                        try {
                            if (session.isOpen()) {
                                // 解析响应
                                try {
                                    JsonNode responseNode = objectMapper.readTree(response);
                                    String responseContent = responseNode.has("content") ? responseNode.get("content").asText() : response;
                                    int progress = responseNode.has("progress") ? responseNode.get("progress").asInt() : 0;
                                    
                                    // 构建返回给客户端的消息
                                    Map<String, Object> clientResponse = new HashMap<>();
                                    clientResponse.put("type", "ROBOT_MESSAGE");
                                    clientResponse.put("content", responseContent);
                                    clientResponse.put("progress", progress);
                                    clientResponse.put("robotName", ROBOT_NAME);
                                    clientResponse.put("teamName", teamName);
                                    
                                    session.getBasicRemote().sendText(objectMapper.writeValueAsString(clientResponse));
                                } catch (Exception e) {
                                    // 如果解析失败，直接发送原始响应
                                    session.getBasicRemote().sendText(response);
                                }
                            }
                        } catch (IOException e) {
                            log.error("发送消息失败", e);
                        }
                    },
                    error -> log.error("调用豆包大模型失败", error),
                    () -> log.info("消息生成完成")
                );
        } catch (Exception e) {
            log.error("处理用户消息失败", e);
        }
    }
    
    /**
     * 根据团队ID获取团队名称
     * @param teamId 团队ID
     * @return 团队名称
     */
    private String getTeamName(Long teamId) {
        String teamName = "客服团队";
        if (teamId != null) {
            TrainTeam team = trainTeamService.getTeamById(teamId);
            if (team != null) {
                teamName = team.getName();
            }
        }
        return teamName;
    }

    @Override
    public String buildSystemMessage(ScriptChatMessage message) {
        StringBuilder systemMessage = new StringBuilder();
        systemMessage.append("你模拟一个客户，扮演成以下角色，和客服聊天，所提问题要聚焦在用户想要购买的产品上，按照第4步流程节点的顺序进行提问。具体的信息如下：");
        
        // 添加买家背景信息
        if (message.getBuyerRequirement() != null && !message.getBuyerRequirement().isEmpty()) {
            systemMessage.append("买家背景信息是 : ").append(message.getBuyerRequirement()).append(" ， ");
        }
        
        // 添加买家想要购买的产品
        if (message.getProductList() != null && !message.getProductList().isEmpty()) {
            Map<String, Object> firstProduct = message.getProductList().get(0);
            if (firstProduct.containsKey("externalProductName")) {
                systemMessage.append("买家想要购买的产品是 : ").append(firstProduct.get("externalProductName")).append("，");
            }
        }
        
        // 添加图片信息
        if (message.getRelateImgs() != null && !message.getRelateImgs().isEmpty()) {
            for (Map<String, Object> img : message.getRelateImgs()) {
                if (img.containsKey("recognized_text") && img.containsKey("url")) {
                    systemMessage.append("关联的图片(视频)信息是 : ").append(img.get("recognized_text"))
                               .append(" ， 图片(视频)链接是 : ").append(img.get("url")).append("，");
                }
            }
        }
        
        // 添加流程节点信息
        if (message.getFlowNodes() != null && !message.getFlowNodes().isEmpty()) {
            systemMessage.append("按照如下流程节点进行询问 : ");
            for (Map<String, Object> node : message.getFlowNodes()) {
                if (node.containsKey("nodeName") && node.containsKey("nodeBuyerRequirement")) {
                    systemMessage.append("流程节点名称：").append(node.get("nodeName"))
                               .append(" ，本流程买家要求： ").append(node.get("nodeBuyerRequirement")).append("。");
                }
            }
        }
        
        // 添加意图信息
        if (message.getIntents() != null) {
            Map<String, Object> intents = message.getIntents();
            if (intents.containsKey("parentName") && intents.containsKey("name")) {
                systemMessage.append("买家进线意图是 : ").append(intents.get("parentName"))
                           .append("，").append(intents.get("name")).append("，");
            }
        }
        
        systemMessage.append("你需要首先向客服发起聊天。");
        
        return systemMessage.toString();
    }
} 