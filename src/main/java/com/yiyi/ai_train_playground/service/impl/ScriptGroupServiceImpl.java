package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.dto.AllGroupsHead;
import com.yiyi.ai_train_playground.dto.OfficialGroupHead;
import com.yiyi.ai_train_playground.dto.ScriptGroupDTO;
import com.yiyi.ai_train_playground.entity.ScriptGroup;
import com.yiyi.ai_train_playground.mapper.ScriptGroupMapper;
import com.yiyi.ai_train_playground.service.ScriptGroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ScriptGroupServiceImpl implements ScriptGroupService {

    private final ScriptGroupMapper scriptGroupMapper;

    public ScriptGroupServiceImpl(ScriptGroupMapper scriptGroupMapper) {
        this.scriptGroupMapper = scriptGroupMapper;
    }

    @Override
    public Map<String, Object> getGroupTree(String groupTitle, Long teamId) {
        // 获取所有分组
        List<ScriptGroup> allGroups = scriptGroupMapper.selectList(groupTitle, teamId);
        
        // 创建根节点 "全部剧本"
        ScriptGroup allGroupsHead = createRootGroup();
        
        // 创建ID到分组的映射，同时去重
        Map<Long, ScriptGroup> groupMap = allGroups.stream()
            .collect(Collectors.toMap(
                ScriptGroup::getId,
                group -> {
                    group.setSubGroups(new ArrayList<>());
                    return group;
                },
                (existing, replacement) -> {
                    if (existing.getFullPath() != null && replacement.getFullPath() != null &&
                        existing.getFullPath().length() <= replacement.getFullPath().length()) {
                        return existing;
                    }
                    return replacement;
                }
            ));

        // 添加根节点到映射
        groupMap.put(allGroupsHead.getId(), allGroupsHead);

        // 构建树形结构
        groupMap.values().stream()
            .filter(group -> group.getParentId() != null)
            .forEach(group -> {
                ScriptGroup parent = groupMap.get(group.getParentId());
                if (parent != null) {
                    if (!parent.getSubGroups().contains(group)) {
                        parent.getSubGroups().add(group);
                    }
                }
            });

        // 获取顶级分组
        Set<Long> allIds = groupMap.keySet();
        List<ScriptGroup> topLevelGroups = groupMap.values().stream()
            .filter(group -> group.getParentId() == null || !allIds.contains(group.getParentId()))
            .collect(Collectors.toList());

        // 按类型分组
        Map<String, List<ScriptGroup>> groupedByType = topLevelGroups.stream()
            .collect(Collectors.groupingBy(group -> {
                if (group.getId() == 1L) return "unGrouped";
                if (Boolean.TRUE.equals(group.getIsOfficial())) return "official";
                return "user";
            }));

        // 按指定顺序添加到根节点：1.未分组 2.用户自定义分组 3.官方预设分组
        List<ScriptGroup> orderedGroups = new ArrayList<>();
        // 1. 未分组
        orderedGroups.addAll(groupedByType.getOrDefault("unGrouped", Collections.emptyList()));
        // 2. 用户自定义分组
        List<ScriptGroup> userGroups = groupedByType.getOrDefault("user", Collections.emptyList());
        if (!userGroups.isEmpty()) {
            userGroups.sort((g1, g2) -> {
                // 按创建时间倒序
                LocalDateTime time1 = g1.getCreateTime();
                LocalDateTime time2 = g2.getCreateTime();
                if (time1 == null && time2 == null) return 0;
                if (time1 == null) return 1;
                if (time2 == null) return -1;
                return time2.compareTo(time1);
            });
            orderedGroups.addAll(userGroups);
        }
        // 3. 官方预设分组
        List<ScriptGroup> officialGroups = groupedByType.getOrDefault("official", Collections.emptyList());
        if (!officialGroups.isEmpty()) {
            officialGroups.sort((g1, g2) -> {
                // 按sortOrder和创建时间排序
                Integer sort1 = g1.getSortOrder();
                Integer sort2 = g2.getSortOrder();
                if (sort1 == null && sort2 == null) {
                    LocalDateTime time1 = g1.getCreateTime();
                    LocalDateTime time2 = g2.getCreateTime();
                    if (time1 == null && time2 == null) return 0;
                    if (time1 == null) return 1;
                    if (time2 == null) return -1;
                    return time2.compareTo(time1);
                }
                if (sort1 == null) return 1;
                if (sort2 == null) return -1;
                return sort1.compareTo(sort2);
            });
            orderedGroups.addAll(officialGroups);
        }

        // 递归排序子分组
        sortSubGroupsRecursively(orderedGroups);

        allGroupsHead.setSubGroups(orderedGroups);
        
        // 转换为DTO并返回
        ScriptGroupDTO rootDTO = ScriptGroupDTO.fromEntity(allGroupsHead);
        return Collections.singletonMap("groups", Collections.singletonList(rootDTO));
    }

    private void sortSubGroupsRecursively(List<ScriptGroup> groups) {
        if (groups == null || groups.isEmpty()) {
            return;
        }

        // 排序当前层级的子分组
        for (ScriptGroup group : groups) {
            if (group.getSubGroups() != null && !group.getSubGroups().isEmpty()) {
                List<ScriptGroup> subGroups = group.getSubGroups();
                subGroups.sort((g1, g2) -> {
                    // 按创建时间倒序
                    LocalDateTime time1 = g1.getCreateTime();
                    LocalDateTime time2 = g2.getCreateTime();
                    if (time1 == null && time2 == null) return 0;
                    if (time1 == null) return 1;
                    if (time2 == null) return -1;
                    return time2.compareTo(time1);
                });
                // 递归排序子分组
                sortSubGroupsRecursively(subGroups);
            }
        }
    }

    private ScriptGroup createRootGroup() {
        ScriptGroup root = new ScriptGroup();
        root.setId(-1L);
        root.setTeamId(0L);
        root.setGroupTitle("全部剧本");
        root.setIsOfficial(false);
        root.setSubGroups(new ArrayList<>());
        return root;
    }

    @Override
    @Transactional
    public boolean save(ScriptGroup scriptGroup) {
        return scriptGroupMapper.insert(scriptGroup) > 0;
    }

    @Override
    @Transactional
    public boolean update(ScriptGroup scriptGroup) {
        return scriptGroupMapper.update(scriptGroup) > 0;
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids, Long teamId) {
        return scriptGroupMapper.deleteByIds(ids, teamId) > 0;
    }

    @Override
    public List<ScriptGroup> getScriptGroups(String groupTitle, Long teamId) {
        return scriptGroupMapper.selectList(groupTitle, teamId);
    }
} 