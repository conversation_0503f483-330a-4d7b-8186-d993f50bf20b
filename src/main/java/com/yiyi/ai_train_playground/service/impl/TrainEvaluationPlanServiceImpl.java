package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.dto.EvaluationPlanShortDTO;
import com.yiyi.ai_train_playground.mapper.TrainEvaluationPlanMapper;
import com.yiyi.ai_train_playground.service.TrainEvaluationPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 评价方案服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-03
 */
@Slf4j
@Service
public class TrainEvaluationPlanServiceImpl implements TrainEvaluationPlanService {

    @Autowired
    private TrainEvaluationPlanMapper trainEvaluationPlanMapper;

    @Override
    public List<EvaluationPlanShortDTO> getShortList(Long teamId) {
        log.info("查询评价方案简短列表：teamId={}", teamId);
        
        if (teamId == null) {
            throw new RuntimeException("团队ID不能为空");
        }
        
        List<EvaluationPlanShortDTO> result = trainEvaluationPlanMapper.selectShortList(teamId);
        log.info("查询评价方案简短列表结果：size={}", result.size());
        
        return result;
    }
}
