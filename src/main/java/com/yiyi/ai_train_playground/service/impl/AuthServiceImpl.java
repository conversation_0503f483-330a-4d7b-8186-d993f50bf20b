package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.dto.LoginRequest;
import com.yiyi.ai_train_playground.dto.LoginResponse;
import com.yiyi.ai_train_playground.entity.User;
import com.yiyi.ai_train_playground.mapper.UserMapper;
import com.yiyi.ai_train_playground.service.AuthService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class AuthServiceImpl implements AuthService {

    private final UserMapper userMapper;
    private final JwtUtil jwtUtil;
    private final PasswordEncoder passwordEncoder;

    public AuthServiceImpl(UserMapper userMapper, JwtUtil jwtUtil, PasswordEncoder passwordEncoder) {
        this.userMapper = userMapper;
        this.jwtUtil = jwtUtil;
        this.passwordEncoder = passwordEncoder;
    }

    @Override
    @Transactional
    public LoginResponse login(LoginRequest request) {
        User user = userMapper.findByIdentity(request.getIdentity());
        if (user == null) {
            throw new RuntimeException("用户名或密码错误");
        }

        // 检查账号是否被锁定
        if (user.getIsLocked() && user.getLockTime() != null) {
            // 如果锁定时间超过30分钟，解除锁定
            if (user.getLockTime().plusMinutes(30).isBefore(LocalDateTime.now())) {
                userMapper.updateLoginStatus(user.getId(), 0, false, null);
                user.setFailedAttempts(0);
                user.setIsLocked(false);
                user.setLockTime(null);
            } else {
                throw new RuntimeException("账号已被锁定，请30分钟后再试");
            }
        }

        // 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPasswordHash())) {
            // 更新失败次数
            int failedAttempts = user.getFailedAttempts() + 1;
            boolean shouldLock = failedAttempts >= 5;
            LocalDateTime lockTime = shouldLock ? LocalDateTime.now() : null;
            
            userMapper.updateLoginStatus(
                user.getId(),
                failedAttempts,
                shouldLock,
                lockTime
            );

            throw new RuntimeException("用户名或密码错误");
        }

        // 登录成功，重置失败次数
        if (user.getFailedAttempts() > 0) {
            userMapper.updateLoginStatus(user.getId(), 0, false, null);
        }

        // 生成token
        String token = jwtUtil.generateToken(user.getId(), user.getUsername(), user.getTeamId(), request.getRememberMe());

        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setUserId(user.getId());
        response.setUsername(user.getUsername());
        response.setEmail(user.getEmail());
        response.setMobile(user.getMobile());
        response.setToken(token);
        response.setTeamId(user.getTeamId());
        response.setDisplayName(user.getDisplayName());

        return response;
    }
} 