package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.entity.ScriptGroup;
import java.util.List;
import java.util.Map;

public interface ScriptGroupService {
    Map<String, Object> getGroupTree(String groupTitle, Long teamId);
    
    boolean save(ScriptGroup scriptGroup);
    
    boolean update(ScriptGroup scriptGroup);
    
    boolean deleteByIds(String ids, Long teamId);
    
    List<ScriptGroup> getScriptGroups(String groupTitle, Long teamId);
} 