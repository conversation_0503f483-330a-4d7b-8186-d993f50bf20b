package com.yiyi.ai_train_playground.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Redis服务类
 * 使用application.yml中的配置，管理连接池
 */
@Slf4j
@Service
public class RedisService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final StringRedisTemplate stringRedisTemplate;
    private final RedisConnectionFactory connectionFactory;

    // 从application.yml读取Redis配置
    @Value("${spring.data.redis.host:localhost}")
    private String redisHost;

    @Value("${spring.data.redis.port:6379}")
    private int redisPort;

    @Value("${spring.data.redis.database:0}")
    private int redisDatabase;

    @Value("${spring.data.redis.lettuce.pool.max-active:50}")
    private int maxActive;

    @Value("${spring.data.redis.lettuce.pool.max-idle:20}")
    private int maxIdle;

    @Value("${spring.data.redis.lettuce.pool.min-idle:5}")
    private int minIdle;

    @Value("${spring.data.redis.lettuce.pool.max-wait:2000}")
    private long maxWait;

    /**
     * 构造方法，注入Redis模板
     */
    public RedisService(RedisTemplate<String, Object> redisTemplate, 
                       StringRedisTemplate stringRedisTemplate,
                       RedisConnectionFactory connectionFactory) {
        this.redisTemplate = redisTemplate;
        this.stringRedisTemplate = stringRedisTemplate;
        this.connectionFactory = connectionFactory;
    }

    /**
     * 初始化方法，在构造完成后执行
     */
    @PostConstruct
    public void init() {
        log.info("Redis服务初始化");
        log.info("Redis配置 - Host: {}, Port: {}, Database: {}", redisHost, redisPort, redisDatabase);
        log.info("连接池配置 - MaxActive: {}, MaxIdle: {}, MinIdle: {}, MaxWait: {}ms", 
                maxActive, maxIdle, minIdle, maxWait);
        
        // 测试连接
        try {
            testConnection();
            log.info("✅ Redis连接池初始化成功");
        } catch (Exception e) {
            log.error("❌ Redis连接池初始化失败", e);
            throw new RuntimeException("Redis连接池初始化失败: " + e.getMessage());
        }
    }

    /**
     * 测试Redis连接
     */
    public void testConnection() {
        String testKey = "redis:service:test";
        String testValue = "connection_test_" + System.currentTimeMillis();
        
        // 设置测试值
        stringRedisTemplate.opsForValue().set(testKey, testValue, 10, TimeUnit.SECONDS);
        
        // 获取测试值
        String result = stringRedisTemplate.opsForValue().get(testKey);
        
        if (!testValue.equals(result)) {
            throw new RuntimeException("Redis连接测试失败：写入和读取的值不一致");
        }
        
        // 清理测试数据
        stringRedisTemplate.delete(testKey);
    }

    /**
     * 设置字符串值
     */
    public void setString(String key, String value) {
        stringRedisTemplate.opsForValue().set(key, value);
    }

    /**
     * 设置字符串值（带过期时间）
     */
    public void setString(String key, String value, long timeout, TimeUnit unit) {
        stringRedisTemplate.opsForValue().set(key, value, timeout, unit);
    }

    /**
     * 获取字符串值
     */
    public String getString(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    /**
     * 设置Hash字段
     */
    public void setHash(String key, String hashKey, String value) {
        stringRedisTemplate.opsForHash().put(key, hashKey, value);
    }

    /**
     * 批量设置Hash字段
     */
    public void setHash(String key, Map<String, String> hash) {
        stringRedisTemplate.opsForHash().putAll(key, hash);
    }

    /**
     * 获取Hash字段
     */
    public Object getHash(String key, String hashKey) {
        return stringRedisTemplate.opsForHash().get(key, hashKey);
    }

    /**
     * 获取Hash所有字段
     */
    public Map<Object, Object> getHashAll(String key) {
        return stringRedisTemplate.opsForHash().entries(key);
    }

    /**
     * 删除Hash字段
     */
    public Long deleteHash(String key, Object... hashKeys) {
        return stringRedisTemplate.opsForHash().delete(key, hashKeys);
    }

    /**
     * 设置对象值
     */
    public void setObject(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 设置对象值（带过期时间）
     */
    public void setObject(String key, Object value, long timeout, TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value, timeout, unit);
    }

    /**
     * 获取对象值
     */
    public Object getObject(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 删除键
     */
    public Boolean delete(String key) {
        return stringRedisTemplate.delete(key);
    }

    /**
     * 批量删除键
     */
    public Long delete(Collection<String> keys) {
        return stringRedisTemplate.delete(keys);
    }

    /**
     * 设置过期时间
     */
    public Boolean expire(String key, long timeout, TimeUnit unit) {
        return stringRedisTemplate.expire(key, timeout, unit);
    }

    /**
     * 获取过期时间
     */
    public Long getExpire(String key) {
        return stringRedisTemplate.getExpire(key);
    }

    /**
     * 检查键是否存在
     */
    public Boolean hasKey(String key) {
        return stringRedisTemplate.hasKey(key);
    }

    /**
     * 获取匹配的键
     */
    public Set<String> keys(String pattern) {
        return stringRedisTemplate.keys(pattern);
    }

    /**
     * 获取连接池信息
     */
    public Map<String, Object> getConnectionPoolInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("host", redisHost);
        info.put("port", redisPort);
        info.put("database", redisDatabase);
        info.put("maxActive", maxActive);
        info.put("maxIdle", maxIdle);
        info.put("minIdle", minIdle);
        info.put("maxWait", maxWait);
        
        try {
            info.put("connectionFactoryClass", connectionFactory.getClass().getSimpleName());
        } catch (Exception e) {
            info.put("connectionFactoryClass", "unknown");
        }
        
        return info;
    }
} 