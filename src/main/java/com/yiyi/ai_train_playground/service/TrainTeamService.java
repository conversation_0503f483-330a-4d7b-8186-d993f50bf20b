package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.entity.TrainTeam;

import java.util.List;

public interface TrainTeamService {

    /**
     * 根据团队ID查询团队信息
     * @param teamId 团队ID
     * @return 团队信息
     */
    TrainTeam getTeamById(Long teamId);

    /**
     * 根据店铺ID查询团队信息
     * @param shopId 店铺ID
     * @return 团队信息，如果不存在返回null
     */
    TrainTeam findByShopId(Long shopId);

    /**
     * 获取所有团队
     * @return 团队列表
     */
    List<TrainTeam> findAll();
}