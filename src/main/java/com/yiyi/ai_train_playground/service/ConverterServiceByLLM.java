package com.yiyi.ai_train_playground.service;

/**
 * 基于大语言模型的转换服务接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
public interface ConverterServiceByLLM {
    
    /**
     * 使用大语言模型进行转换处理
     *
     * @param prmtTemplateKW 提示词模板关键词
     * @param systemPrompt 系统提示词内容
     * @param userPrompt 用户提示词内容
     * @return 大模型处理结果
     */
    String convertText(String prmtTemplateKW, String systemPrompt, String userPrompt);

    /**
     * 使用大语言模型进行图像转换处理
     *
     * @param prmtTemplateKW 提示词模板关键词
     * @param systemPrompt 系统提示词内容
     * @param userPrompt 用户提示词内容
     * @param imageUrl 图像URL
     * @return 大模型处理结果
     */
    String convertImg(String prmtTemplateKW, String systemPrompt, String userPrompt, String imageUrl);
}
