package com.yiyi.ai_train_playground.service.sms.impl;

import com.yiyi.ai_train_playground.dto.SmsCodeResponse;
import com.yiyi.ai_train_playground.dto.SmsLoginResponse;
import com.yiyi.ai_train_playground.entity.TrainTeam;
import com.yiyi.ai_train_playground.entity.User;
import com.yiyi.ai_train_playground.mapper.TrainTeamMapper;
import com.yiyi.ai_train_playground.mapper.UserMapper;
import com.yiyi.ai_train_playground.service.sms.SmsService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.Duration;
import java.util.UUID;

/**
 * 短信验证码服务实现类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SmsServiceImpl implements SmsService {
    
    private final StringRedisTemplate stringRedisTemplate;
    private final UserMapper userMapper;
    private final TrainTeamMapper trainTeamMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    
    private static final String SMS_CODE_PREFIX = "sms:code:";
    private static final String SMS_LIMIT_PREFIX = "sms:limit:";
    private static final int CODE_LENGTH = 4;
    private static final Duration CODE_EXPIRE_TIME = Duration.ofMinutes(5);
    private static final Duration LIMIT_EXPIRE_TIME = Duration.ofMinutes(1);
    
    @Override
    public SmsCodeResponse sendVerificationCode(String phone) {
        // 1. 检查发送频率限制
        String limitKey = SMS_LIMIT_PREFIX + phone;
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(limitKey))) {
            throw new RuntimeException("发送过于频繁，请1分钟后再试");
        }
        
        // 2. 生成4位数字验证码
        String verificationCode = generateVerificationCode();
        
        // 3. 生成全局唯一的验证密钥
        String verificationKey = UUID.randomUUID().toString().replace("-", "");
        
        // 4. 存储验证码到Redis（验证密钥作为key，手机号和验证码作为value）
        String codeKey = SMS_CODE_PREFIX + verificationKey;
        String codeValue = phone + ":" + verificationCode; // 存储手机号和验证码
        stringRedisTemplate.opsForValue().set(codeKey, codeValue, CODE_EXPIRE_TIME);
        
        // 5. 设置发送频率限制
        stringRedisTemplate.opsForValue().set(limitKey, "1", LIMIT_EXPIRE_TIME);
        
        // 6. 模拟发送短信（实际项目中这里应该调用短信服务商API）
        log.info("向手机号 {} 发送验证码: {}", phone, verificationCode);
        
        return new SmsCodeResponse(verificationKey);
    }
    
    @Override
    public boolean verifyCode(String verificationKey, String verificationCode) {
        // 1. 从Redis获取存储的验证码
        String codeKey = SMS_CODE_PREFIX + verificationKey;
        String storedValue = stringRedisTemplate.opsForValue().get(codeKey);
        
        // 2. 验证码不存在或已过期
        if (storedValue == null) {
            log.warn("验证码不存在或已过期，verificationKey: {}", verificationKey);
            return false;
        }
        
        // 3. 解析存储的值（格式：phone:code）
        String[] parts = storedValue.split(":");
        if (parts.length != 2) {
            log.warn("验证码格式错误，verificationKey: {}", verificationKey);
            return false;
        }
        
        String storedCode = parts[1];
        
        // 4. 验证码匹配
        boolean isValid = storedCode.equals(verificationCode);
        
        // 5. 验证成功后删除验证码（一次性使用）
        if (isValid) {
            stringRedisTemplate.delete(codeKey);
            log.info("验证码验证成功，verificationKey: {}", verificationKey);
        } else {
            log.warn("验证码验证失败，verificationKey: {}, 输入的验证码: {}", verificationKey, verificationCode);
        }
        
        return isValid;
    }
    
    @Override
    @Transactional
    public SmsLoginResponse smsLogin(String verificationKey, String verificationCode, String phone) {
        // 1. 从Redis获取存储的验证码和手机号
        String codeKey = SMS_CODE_PREFIX + verificationKey;
        String storedValue = stringRedisTemplate.opsForValue().get(codeKey);
        
        // 2. 验证码不存在或已过期
        if (storedValue == null) {
            throw new RuntimeException("验证码不存在或已过期");
        }
        
        // 3. 解析存储的值（格式：phone:code）
        String[] parts = storedValue.split(":");
        if (parts.length != 2) {
            throw new RuntimeException("验证码格式错误");
        }
        
        String storedPhone = parts[0];
        String storedCode = parts[1];
        
        // 4. 验证验证码
        if (!storedCode.equals(verificationCode)) {
            throw new RuntimeException("验证码错误");
        }
        
        // 5. 使用从Redis中获取的手机号（如果传入的phone为null）
        if (phone == null) {
            phone = storedPhone;
        } else if (!storedPhone.equals(phone)) {
            throw new RuntimeException("手机号不匹配");
        }
        
        // 6. 验证成功，删除验证码
        stringRedisTemplate.delete(codeKey);
        
        // 7. 检查用户是否已存在
        User existingUser = userMapper.findByMobile(phone);
        if (existingUser != null) {
            // 用户已存在，直接登录
            String token = jwtUtil.generateToken(existingUser.getId(), existingUser.getUsername(), existingUser.getTeamId(), false);
            
            SmsLoginResponse response = new SmsLoginResponse();
            response.setUserId(existingUser.getId());
            response.setUsername(existingUser.getUsername());
            response.setDisplayName(existingUser.getDisplayName());
            response.setMobile(existingUser.getMobile());
            response.setToken(token);
            
            log.info("用户已存在，直接登录成功，userId: {}, username: {}", existingUser.getId(), existingUser.getUsername());
            return response;
        }
        
        // 8. 用户不存在，自动创建团队和用户
        
        // 8.1 创建团队
        TrainTeam team = new TrainTeam();
        team.setName("手机用户团队_" + phone);
        team.setDescription("通过手机验证码自动创建的团队");
        team.setCreator("system");
        team.setUpdater("system");
        
        int teamInsertResult = trainTeamMapper.insert(team);
        if (teamInsertResult <= 0) {
            throw new RuntimeException("创建团队失败");
        }
        
        log.info("自动创建团队成功，teamId: {}, teamName: {}", team.getId(), team.getName());
        
        // 8.2 生成随机用户名
        String username = generateRandomUsername();
        
        // 8.3 创建用户
        User user = new User();
        user.setUsername(username);
        user.setPasswordHash(passwordEncoder.encode("123456")); // 默认密码123456
        user.setMobile(phone);
        user.setDisplayName("手机用户");
        user.setTeamId(team.getId());
        user.setFailedAttempts(0);
        user.setIsLocked(false);
        user.setCreator("system");
        user.setUpdater("system");
        
        int userInsertResult = userMapper.insert(user);
        if (userInsertResult <= 0) {
            throw new RuntimeException("创建用户失败");
        }
        
        log.info("自动创建用户成功，userId: {}, username: {}, teamId: {}", user.getId(), user.getUsername(), user.getTeamId());
        
        // 9. 生成JWT token
        String token = jwtUtil.generateToken(user.getId(), user.getUsername(), user.getTeamId(), false);
        
        // 10. 构建响应
        SmsLoginResponse response = new SmsLoginResponse();
        response.setUserId(user.getId());
        response.setUsername(user.getUsername());
        response.setDisplayName(user.getDisplayName());
        response.setMobile(user.getMobile());
        response.setToken(token);
        
        log.info("短信验证码登录成功，userId: {}, username: {}, teamId: {}", user.getId(), user.getUsername(), user.getTeamId());
        return response;
    }
    
    /**
     * 生成4位数字验证码
     */
    private String generateVerificationCode() {
        SecureRandom random = new SecureRandom();
        int code = 1000 + random.nextInt(9000); // 生成1000-9999的随机数
        return String.valueOf(code);
    }
    
    /**
     * 生成随机用户名
     */
    private String generateRandomUsername() {
        // 生成格式：user_ + 8位随机字符
        String chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        SecureRandom random = new SecureRandom();
        StringBuilder username = new StringBuilder("user_");
        
        for (int i = 0; i < 8; i++) {
            username.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        // 检查用户名是否已存在，如果存在则重新生成
        String generatedUsername = username.toString();
        User existingUser = userMapper.findByUsername(generatedUsername);
        if (existingUser != null) {
            return generateRandomUsername(); // 递归重新生成
        }
        
        return generatedUsername;
    }
} 