package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.entity.EvaluationGroup;
import com.yiyi.ai_train_playground.service.EvaluationGroupService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import io.jsonwebtoken.Claims;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/api/evaluation-groups")
public class EvaluationGroupController {

    private final EvaluationGroupService evaluationGroupService;
    private final JwtUtil jwtUtil;

    public EvaluationGroupController(EvaluationGroupService evaluationGroupService, JwtUtil jwtUtil) {
        this.evaluationGroupService = evaluationGroupService;
        this.jwtUtil = jwtUtil;
    }

    @GetMapping
    public Result<Map<String, Object>> getEvaluationGroups(
            @RequestParam(required = false) String groupTitle,
            @RequestHeader("Authorization") String authorization) {
        // 从Authorization header中提取token
        String token = authorization.replace("Bearer ", "");
        Claims claims = jwtUtil.parseToken(token);
        Long teamId = claims.get("teamId", Long.class);

        // 调用服务层方法获取分组树
        Map<String, Object> groupTree = evaluationGroupService.getGroupTree(groupTitle, teamId);
        return Result.success(groupTree);
    }

    @PostMapping
    public Result<Void> save(@RequestBody EvaluationGroup evaluationGroup,
                           @RequestHeader("Authorization") String authorization) {
        String token = authorization.replace("Bearer ", "");
        Claims claims = jwtUtil.parseToken(token);
        Long teamId = claims.get("teamId", Long.class);
        
        evaluationGroup.setTeamId(teamId);
        if (evaluationGroupService.save(evaluationGroup)) {
            return Result.success(null);
        }
        return Result.error("添加失败");
    }

    @PutMapping
    public Result<Void> update(@RequestBody EvaluationGroup evaluationGroup,
                             @RequestHeader("Authorization") String authorization) {
        String token = authorization.replace("Bearer ", "");
        Claims claims = jwtUtil.parseToken(token);
        Long teamId = claims.get("teamId", Long.class);
        
        // 验证是否有权限修改
        if (!teamId.equals(evaluationGroup.getTeamId())) {
            return Result.error("无权修改其他团队的分组");
        }
        
        if (evaluationGroupService.update(evaluationGroup)) {
            return Result.success(null);
        }
        return Result.error("修改失败");
    }

    @DeleteMapping
    public Result<Void> delete(@RequestParam String ids,
                             @RequestHeader("Authorization") String authorization) {
        String token = authorization.replace("Bearer ", "");
        Claims claims = jwtUtil.parseToken(token);
        Long teamId = claims.get("teamId", Long.class);
        
        if (evaluationGroupService.deleteByIds(ids, teamId)) {
            return Result.success(null);
        }
        return Result.error("删除失败");
    }
}
