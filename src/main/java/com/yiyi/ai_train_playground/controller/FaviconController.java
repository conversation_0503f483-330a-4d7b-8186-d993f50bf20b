package com.yiyi.ai_train_playground.controller;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class FaviconController {

    @GetMapping("/favicon.ico")
    public ResponseEntity<Void> favicon() {
        // 返回404而不是500错误
        return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
    }
} 