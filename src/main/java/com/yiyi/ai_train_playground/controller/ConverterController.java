package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.service.ConverterServiceByLLM;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 基于大语言模型的转换服务控制器
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("/api/converter")
@RequiredArgsConstructor
@Tag(name = "LLM转换服务", description = "基于大语言模型的转换服务接口")
public class ConverterController {

    private final ConverterServiceByLLM converterService;

    @PostMapping("/convert")
    @Operation(summary = "LLM转换处理", description = "使用大语言模型进行转换处理，支持自定义提示词模板")
    public String convert(
            @Parameter(description = "提示词模板关键词", required = true, example = "html_md_converter:SU")
            @RequestParam String prmtTemplateKW,

            @Parameter(description = "系统提示词内容", required = true, example = "你是一个专业的转换助手")
            @RequestParam String systemPrompt,

            @Parameter(description = "用户提示词内容", required = true, example = "请转换以下内容")
            @RequestParam String userPrompt) {
        
        log.info("收到LLM转换请求: prmtTemplateKW={}, systemPrompt长度={}, userPrompt长度={}", 
                prmtTemplateKW, 
                systemPrompt != null ? systemPrompt.length() : 0,
                userPrompt != null ? userPrompt.length() : 0);
        
        try {
            String result = converterService.convertText(prmtTemplateKW, systemPrompt, userPrompt);
            log.info("LLM转换完成: 结果长度={}", result.length());
            return result;
        } catch (Exception e) {
            log.error("LLM转换失败", e);
            throw e;
        }
    }
    
    @PostMapping("/html-to-markdown")
    @Operation(summary = "HTML转Markdown", description = "使用预设模板将HTML内容转换为Markdown格式")
    public String htmlToMarkdown(
            @Parameter(description = "HTML内容", required = true, example = "<div><h1>标题</h1><p>段落</p></div>")
            @RequestParam String htmlContent) {
        
        log.info("收到HTML转Markdown请求: HTML长度={}", htmlContent != null ? htmlContent.length() : 0);
        
        // 使用预设的HTML转Markdown模板
        String prmtTemplateKW = "html_md_converter:SU";
        String systemPrompt = "你是一个专业的HTML到Markdown转换助手，请将用户提供的HTML内容转换为标准的Markdown格式。";
        
        try {
            String result = converterService.convertText(prmtTemplateKW, systemPrompt, htmlContent);
            log.info("HTML转Markdown完成: 结果长度={}", result.length());
            return result;
        } catch (Exception e) {
            log.error("HTML转Markdown失败", e);
            throw e;
        }
    }
}
