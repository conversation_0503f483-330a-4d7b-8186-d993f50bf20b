package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.util.ResolveUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * HTML解析工具控制器
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("/api/resolve")
@Tag(name = "HTML解析工具", description = "用于解析HTML并提取图片URL的工具接口")
public class ResolveUtilController {
    
    /**
     * 从HTML内容中提取图片URL
     */
    @PostMapping("/extract-images")
    @Operation(summary = "提取图片URL", description = "从HTML内容中提取所有img标签的src属性")
    public Map<String, Object> extractImages(@RequestBody ExtractImagesRequest request) {
        log.info("开始解析HTML内容，长度: {}", request.getHtmlContent() != null ? request.getHtmlContent().length() : 0);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<String> imageUrls;
            
            if (request.getCssSelector() != null && !request.getCssSelector().trim().isEmpty()) {
                // 使用CSS选择器
                imageUrls = ResolveUtil.extractImageSourcesBySelector(request.getHtmlContent(), request.getCssSelector());
            } else if (request.isFilterEmpty() || request.isFilterDataUrl()) {
                // 使用过滤器
                imageUrls = ResolveUtil.extractImageSources(request.getHtmlContent(), request.isFilterEmpty(), request.isFilterDataUrl());
            } else {
                // 使用基本方法
                imageUrls = ResolveUtil.extractImageSources(request.getHtmlContent());
            }
            
            response.put("success", true);
            response.put("count", imageUrls.size());
            response.put("imageUrls", imageUrls);
            response.put("message", "成功提取到 " + imageUrls.size() + " 个图片URL");
            
            log.info("成功提取到 {} 个图片URL", imageUrls.size());
            
        } catch (Exception e) {
            log.error("解析HTML时发生异常", e);
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("message", "解析HTML时发生异常: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 验证图片URL格式
     */
    @PostMapping("/validate-url")
    @Operation(summary = "验证图片URL", description = "验证URL是否为有效的图片URL格式")
    public Map<String, Object> validateImageUrl(@RequestBody ValidateUrlRequest request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean isValid = ResolveUtil.isValidImageUrl(request.getUrl());
            
            response.put("success", true);
            response.put("url", request.getUrl());
            response.put("isValid", isValid);
            response.put("message", isValid ? "URL格式有效" : "URL格式无效");
            
        } catch (Exception e) {
            log.error("验证URL时发生异常", e);
            response.put("success", false);
            response.put("error", e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 获取演示HTML
     */
    @GetMapping("/demo-html")
    @Operation(summary = "获取演示HTML", description = "获取用于测试的演示HTML内容")
    public Map<String, Object> getDemoHtml() {
        Map<String, Object> response = new HashMap<>();
        
        String demoHtml = """
            <div data-tab="item">
                <div class="module-title">商品详情</div>
                <div class="goods-base">
                    <div class="item">
                        <div class="text">
                            <a href="//list.jd.com/list.html">
                                <img src="//img1.360buyimg.com/brand-logo.jpg" alt="品牌"/>
                            </a>
                        </div>
                    </div>
                </div>
                <div id="img-text-warp">
                    <div id="img-text">
                        <div class="wrap-scale">
                            <div id="quality-life" class="quality-life">
                                <div class="q-logo">
                                    <img src="//img11.360buyimg.com/devfe/jfs/t19249/266/555939662/10324/447efd03/5a965eb2Nf83fd68c.jpg" alt="品质生活"/>
                                </div>
                            </div>
                        </div>
                        <div class="detail-content">
                            <img src="//detail1.360buyimg.com/image1.png"/>
                            <img src="//detail2.360buyimg.com/image2.jpg"/>
                        </div>
                    </div>
                </div>
            </div>
            """;
        
        response.put("success", true);
        response.put("htmlContent", demoHtml);
        response.put("expectedUrls", List.of(
            "https://img1.360buyimg.com/brand-logo.jpg",
            "https://img11.360buyimg.com/devfe/jfs/t19249/266/555939662/10324/447efd03/5a965eb2Nf83fd68c.jpg",
            "https://detail1.360buyimg.com/image1.png",
            "https://detail2.360buyimg.com/image2.jpg"
        ));
        response.put("suggestedSelectors", Map.of(
            "all", "img",
            "detail-only", "div.detail-content img",
            "quality-logo", "div.q-logo img"
        ));
        
        return response;
    }
    
    /**
     * 提取图片请求对象
     */
    public static class ExtractImagesRequest {
        private String htmlContent;
        private String cssSelector;
        private boolean filterEmpty = true;
        private boolean filterDataUrl = true;
        
        public String getHtmlContent() { return htmlContent; }
        public void setHtmlContent(String htmlContent) { this.htmlContent = htmlContent; }
        public String getCssSelector() { return cssSelector; }
        public void setCssSelector(String cssSelector) { this.cssSelector = cssSelector; }
        public boolean isFilterEmpty() { return filterEmpty; }
        public void setFilterEmpty(boolean filterEmpty) { this.filterEmpty = filterEmpty; }
        public boolean isFilterDataUrl() { return filterDataUrl; }
        public void setFilterDataUrl(boolean filterDataUrl) { this.filterDataUrl = filterDataUrl; }
    }
    
    /**
     * 验证URL请求对象
     */
    public static class ValidateUrlRequest {
        private String url;
        
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
    }
} 