package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.ProductListDTO;
import com.yiyi.ai_train_playground.dto.ProductDetailDTO;
import com.yiyi.ai_train_playground.dto.ProductUpdateDTO;
import com.yiyi.ai_train_playground.dto.PageRequest;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.service.ProductService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api")
public class ProductController {
    
    @Autowired
    private ProductService productService;
    
    /**
     * 获取商品列表（分页）
     * @param externalProductName 商品名称（可选）
     * @param page 页码，默认1
     * @param size 每页大小，默认10
     * @param sort 排序字段
     * @param order 排序方向，默认desc
     * @param request HTTP请求
     * @return 分页商品列表
     */
    @GetMapping("/products")
    public Result<PageResult<ProductListDTO>> getProducts(
            @RequestParam(value = "externalProductName", required = false) String externalProductName,
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "sort", required = false) String sort,
            @RequestParam(value = "order", required = false, defaultValue = "desc") String order,
            HttpServletRequest request) {
        try {
            // 从请求头获取token
            String token = extractToken(request);
            
            log.info("获取商品列表：externalProductName={}, page={}, size={}", externalProductName, page, size);
            
            // 构建分页请求参数
            PageRequest pageRequest = new PageRequest();
            pageRequest.setPage(page);
            pageRequest.setSize(size);
            pageRequest.setSort(sort);
            pageRequest.setOrder(order);
            
            PageResult<ProductListDTO> products = productService.getProductListWithPage(externalProductName, pageRequest, token);
            return Result.success("success", products);
            
        } catch (Exception e) {
            log.error("获取商品列表失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 根据ID获取商品详情
     * @param productId 商品ID
     * @param request HTTP请求
     * @return 商品详情
     */
    @GetMapping("/get-product-byId/{productId}")
    public Result<ProductDetailDTO> getProductById(
            @PathVariable("productId") Long productId,
            HttpServletRequest request) {
        try {
            // 检查参数
            if (productId == null) {
                return Result.error(0, "商品ID不能为空");
            }
            
            // 从请求头获取token
            String token = extractToken(request);
            
            log.info("获取商品详情：productId={}", productId);
            
            ProductDetailDTO product = productService.getProductById(productId, token);
            return Result.success("success", product);
            
        } catch (Exception e) {
            log.error("获取商品详情失败：productId={}", productId, e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 更新商品信息
     * @param productUpdateDTO 商品更新信息
     * @param request HTTP请求
     * @return 操作结果
     */
    @PutMapping("/product-update")
    public Result<Void> updateProduct(
            @Valid @RequestBody ProductUpdateDTO productUpdateDTO,
            HttpServletRequest request) {
        try {
            // 从请求头获取token
            String token = extractToken(request);
            
            log.info("更新商品并启动异步向量化处理：productId={}", productUpdateDTO.getId());
            
            // 使用新的异步处理方法
            productService.updateProductWithAsyncProcessing(productUpdateDTO, token);
            return Result.success("success", null);
            
        } catch (Exception e) {
            log.error("更新商品失败：productId={}", productUpdateDTO != null ? productUpdateDTO.getId() : null, e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 删除商品
     * @param productId 商品ID
     * @param request HTTP请求
     * @return 操作结果
     */
    @DeleteMapping("/product-del/{productId}")
    public Result<Void> deleteProduct(
            @PathVariable("productId") Long productId,
            HttpServletRequest request) {
        try {
            // 检查参数
            if (productId == null) {
                return Result.error(0, "商品ID不能为空");
            }
            
            // 从请求头获取token
            String token = extractToken(request);
            
            log.info("删除商品：productId={}", productId);
            
            productService.deleteProduct(productId, token);
            return Result.success("success", null);
            
        } catch (Exception e) {
            log.error("删除商品失败：productId={}", productId, e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 从请求头中提取JWT token
     * @param request HTTP请求
     * @return JWT token
     */
    private String extractToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            return token.substring(7);
        }
        return null;
    }
} 