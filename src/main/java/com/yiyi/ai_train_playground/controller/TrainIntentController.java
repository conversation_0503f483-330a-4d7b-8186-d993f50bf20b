package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.IntentGroupDTO;
import com.yiyi.ai_train_playground.service.TrainIntentService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api")
public class TrainIntentController {
    
    private final TrainIntentService trainIntentService;
    private final JwtUtil jwtUtil;

    public TrainIntentController(TrainIntentService trainIntentService, JwtUtil jwtUtil) {
        this.trainIntentService = trainIntentService;
        this.jwtUtil = jwtUtil;
    }

    @GetMapping("/intents")
    public Result<List<IntentGroupDTO>> getIntents(HttpServletRequest request) {
        // 从请求头获取token，如有需要可以通过jwtUtil获取用户信息
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
        }

        // 获取意图列表
        List<IntentGroupDTO> intents = trainIntentService.getIntentGroups();
        return Result.success(intents);
    }
} 