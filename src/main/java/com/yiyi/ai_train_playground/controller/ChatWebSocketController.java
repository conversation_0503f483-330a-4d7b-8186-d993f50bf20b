package com.yiyi.ai_train_playground.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.consts.CONSTS;
import com.yiyi.ai_train_playground.service.BigModelManager;
import com.yiyi.ai_train_playground.service.BigmodelPromptsService;
import com.yiyi.ai_train_playground.service.SuperBigModelInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Controller
public class ChatWebSocketController {
    
    @Autowired
    private BigModelManager bigModelManager;
    
    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    
    @Autowired
    private ObjectMapper objectMapper;
    

    @Autowired
    private BigmodelPromptsService bigmodelPromptsService;
    
    /**
     * 批量初始化多个机器人会话
     */
    @MessageMapping("/initMultiple")
    @SendTo("/topic/initMultiple")
    public String initMultipleRobots(String message) {
        try {
            Map<String, Object> request = objectMapper.readValue(message, Map.class);
            String sceneName = (String) request.get("sceneName");
            String servicerId = (String) request.get("servicerId");
            String token = (String) request.get("token");
            Boolean isThinking = (Boolean) request.get("isThinking");
            Boolean isStreaming = (Boolean) request.get("isStreaming");
            
            // 构建系统提示词数组 - 支持两种方式
            String[] systemPrompts = null;
            
            // 方式1：直接提供完整的systemPrompts数组
            Object systemPromptsObj = request.get("systemPrompts");
            if (systemPromptsObj instanceof List) {
                List<?> promptList = (List<?>) systemPromptsObj;
                systemPrompts = promptList.stream()
                        .map(Object::toString)
                        .toArray(String[]::new);
            } else {
                // 方式2：通过脚本数据构建系统提示词数组
                systemPrompts = buildSystemPromptsArrayFromRequest(request);
            }
            
            // 获取机器人数量
            Integer robotCount = null;
            Object robotCountObj = request.get("robotCount");
            if (robotCountObj instanceof Number) {
                robotCount = ((Number) robotCountObj).intValue();
            }
            
            List<Map<String, Object>> result = bigModelManager.initMultipleRobots(
                sceneName, servicerId, token, isThinking, isStreaming, systemPrompts, robotCount);
            
            // 为每个机器人添加产品列表信息
            for (Map<String, Object> robot : result) {
                enrichResultWithProductInfo(robot, request);
            }
            
            log.info("WebSocket批量初始化机器人成功，数量: {}", result.size());
            return objectMapper.writeValueAsString(result);
            
        } catch (Exception e) {
            log.error("WebSocket批量初始化机器人失败", e);
            try {
                Map<String, Object> errorResult = Map.of(
                    "error", true,
                    "message", "批量初始化机器人失败: " + e.getMessage()
                );
                return objectMapper.writeValueAsString(errorResult);
            } catch (Exception ex) {
                return "{\"error\":true,\"message\":\"批量初始化机器人失败\"}";
            }
        }
    }
    
    /**
     * 初始化单个会话（兼容性方法）
     */
    @MessageMapping("/init")
    @SendTo("/topic/init")
    public String initSession(String message) {
        try {
            Map<String, Object> request = objectMapper.readValue(message, Map.class);
            String sceneName = (String) request.get("sceneName");
            String servicerId = (String) request.get("servicerId");
            String token = (String) request.get("token");
            Boolean isThinking = (Boolean) request.get("isThinking");
            Boolean isStreaming = (Boolean) request.get("isStreaming");
            
            // 构建系统提示词 - 支持两种方式
            String systemPrompt = null;
            Object scriptDataObj = null;
            
            // 方式1：直接提供完整的systemPrompt
            String directPrompt = (String) request.get("systemPrompt");
            if (directPrompt != null && !directPrompt.trim().isEmpty()) {
                systemPrompt = directPrompt;
            } else {
                // 方式2：通过脚本数据构建系统提示词
                scriptDataObj = request.get("scriptData");
                if (scriptDataObj instanceof Map) {
                    systemPrompt = buildSystemPromptFromRequest((Map<String, Object>) scriptDataObj);
                } else {
                    // 兼容旧版本：直接从request读取各个字段
                    systemPrompt = buildSystemPromptFromRequest(request);
                }
            }


            String firstExternalProductId=getExternalProductId((Map<String, Object>) scriptDataObj);

            // 使用BigModelManager初始化会话（内部已处理真正的firstMessage生成）
            Map<String, Object> result = bigModelManager.initSession(
                sceneName, servicerId, token, isThinking, isStreaming, systemPrompt,firstExternalProductId);
            
            // 添加产品列表信息到返回结果中
            if (scriptDataObj instanceof Map) {
                enrichResultWithProductInfo(result, (Map<String, Object>) scriptDataObj);
            } else {
                // 兼容旧版本
                enrichResultWithProductInfo(result, request);
            }
            
            log.info("WebSocket初始化会话成功: {}", result);
            return objectMapper.writeValueAsString(result);
            
        } catch (Exception e) {
            log.error("WebSocket初始化会话失败", e);
            try {
                Map<String, Object> errorResult = Map.of(
                    "error", true,
                    "message", "初始化会话失败: " + e.getMessage()
                );
                return objectMapper.writeValueAsString(errorResult);
            } catch (Exception ex) {
                return "{\"error\":true,\"message\":\"初始化会话失败\"}";
            }
        }
    }
    
    /**
     * 处理客户端发来的消息（重命名自sendMessage）
     */
    @MessageMapping("/send")
    public void handlerAndResponseMessage(String message) {
        try {
            Map<String, Object> request = objectMapper.readValue(message, Map.class);
            String sessionId = (String) request.get("sessionId");
            String userMessage = (String) request.get("message");
            
            if (sessionId == null || userMessage == null) {
                messagingTemplate.convertAndSend("/topic/chat/" + sessionId, 
                    "{\"error\":true,\"message\":\"参数不完整\"}");
                return;
            }
            
            // 调用大模型管理器发送消息（流式响应）
            bigModelManager.handlerAndResponse(sessionId, userMessage)
                    .subscribe(
                        response -> {
                            // 发送流式响应到客户端
                            messagingTemplate.convertAndSend("/topic/chat/" + sessionId, response);
                        },
                        error -> {
                            // 发送错误信息
                            try {
                                Map<String, Object> errorResult = Map.of(
                                    "error", true,
                                    "message", error.getMessage()
                                );
                                String errorJson = objectMapper.writeValueAsString(errorResult);
                                messagingTemplate.convertAndSend("/topic/chat/" + sessionId, errorJson);
                            } catch (Exception ex) {
                                messagingTemplate.convertAndSend("/topic/chat/" + sessionId, 
                                    "{\"error\":true,\"message\":\"发送消息失败\"}");
                            }
                            log.error("WebSocket发送消息失败: sessionId={}", sessionId, error);
                        },
                        () -> {
                            // 流完成
                            log.debug("WebSocket消息流完成: sessionId={}", sessionId);
                        }
                    );
            
        } catch (Exception e) {
            log.error("WebSocket处理发送消息失败", e);
            try {
                Map<String, Object> request = objectMapper.readValue(message, Map.class);
                String sessionId = (String) request.get("sessionId");
                messagingTemplate.convertAndSend("/topic/chat/" + sessionId, 
                    "{\"error\":true,\"message\":\"处理消息失败\"}");
            } catch (Exception ex) {
                // 无法解析sessionId，无法发送错误消息
                log.error("无法解析sessionId发送错误消息", ex);
            }
        }
    }
    
    /**
     * 根据请求数据构建系统提示词
     * @param request 请求数据（可能是scriptData或者兼容旧版本的直接字段）
     * @return 构建好的系统提示词
     */
    private String buildSystemPromptFromRequest(Map<String, Object> request) {
        try {
            // 1. 从数据库获取提示词模板
            List<String> prompts = bigmodelPromptsService.getPromptsByKeyword(CONSTS.DEFAULT_MULTI_CHATS_KEYWORD);
            if (prompts == null || prompts.isEmpty()) {
                log.warn("未找到多轮聊天提示词模板，使用默认模板");
                return buildDefaultSystemPrompt(request);
            }
            
            String template = prompts.get(0); // 取第一个提示词作为模板
            log.info("获取到提示词模板：{}", template);
            
            // 2. 进行变量替换
            String result = replaceVariables(template, request);
            log.info("变量替换后的提示词：{}", result);
            
            return result;
            
        } catch (Exception e) {
            log.error("构建系统提示词失败，使用默认模板", e);
            return buildDefaultSystemPrompt(request);
        }
    }
    
    /**
     * 替换模板中的变量
     * @param template 提示词模板
     * @param request 请求数据
     * @return 替换后的提示词
     */
    private String replaceVariables(String template, Map<String, Object> request) {
        String result = template;
        
        // 3.1 替换 {{buyerRequirement}}
        String buyerRequirement = (String) request.get("buyerRequirement");
        if (buyerRequirement != null && !buyerRequirement.trim().isEmpty()) {
            result = result.replace("{{buyerRequirement}}", buyerRequirement);
        } else {
            result = result.replace("{{buyerRequirement}}", "");
        }
        
        // 3.2 替换 {{externalProductName}} - 取第一个产品的名称
        Object productListObj = request.get("productList");
        String productName = "";
        if (productListObj instanceof List) {
            List<?> productList = (List<?>) productListObj;
            if (!productList.isEmpty() && productList.get(0) instanceof Map) {
                Map<?, ?> firstProduct = (Map<?, ?>) productList.get(0);
                Object nameObj = firstProduct.get("externalProductName");
                if (nameObj != null) {
                    productName = nameObj.toString();
                }
            }
        }
        result = result.replace("{{externalProductName}}", productName);
        
        // 3.3 处理 {{relateImgsTextListLoop}}{{/relateImgsTextListLoop}} 循环标签
        result = processRelateImgsLoop(result, request);
        
        // 3.4 处理 {{flowNodeLoop}}{{/flowNodeLoop}} 循环标签
        result = processFlowNodeLoop(result, request);
        
        // 3.5 替换意图相关变量
        Object intentsObj = request.get("intents");
        String intentPhrase = "";
        String intentName = "";
        if (intentsObj instanceof Map) {
            Map<?, ?> intents = (Map<?, ?>) intentsObj;
            Object parentNameObj = intents.get("parentName");
            Object nameObj = intents.get("name");
            if (parentNameObj != null) {
                intentPhrase = parentNameObj.toString();
            }
            if (nameObj != null) {
                intentName = nameObj.toString();
            }
        }
        result = result.replace("{{intentPhrase}}", intentPhrase);
        result = result.replace("{{intentName}}", intentName);
        
        return result;
    }


    private String getExternalProductId(Map<String, Object> request) {

        // 3.2 替换 {{externalProductName}} - 取第一个产品的名称
        Object productListObj = request.get("productList");
        String productId = "";
        if (productListObj instanceof List) {
            List<?> productList = (List<?>) productListObj;
            if (!productList.isEmpty() && productList.get(0) instanceof Map) {
                Map<?, ?> firstProduct = (Map<?, ?>) productList.get(0);
                Object nameObj = firstProduct.get("externalProductId");
                if (nameObj != null) {
                    productId = nameObj.toString();
                }
            }
        }

        return productId;
    }


    /**
     * 处理关联图片循环标签
     */
    private String processRelateImgsLoop(String template, Map<String, Object> request) {
        String startTag = "{{relateImgsTextListLoop}}";
        String endTag = "{{/relateImgsTextListLoop}}";
        
        int startIndex = template.indexOf(startTag);
        int endIndex = template.indexOf(endTag);
        
        if (startIndex == -1 || endIndex == -1 || startIndex >= endIndex) {
            return template; // 没有找到循环标签，直接返回
        }
        
        String beforeLoop = template.substring(0, startIndex);
        String loopTemplate = template.substring(startIndex + startTag.length(), endIndex);
        String afterLoop = template.substring(endIndex + endTag.length());
        
        StringBuilder loopResult = new StringBuilder();
        
        // 遍历relateImgs数组
        Object relateImgsObj = request.get("relateImgs");
        if (relateImgsObj instanceof List) {
            List<?> relateImgs = (List<?>) relateImgsObj;
            for (Object imgObj : relateImgs) {
                if (imgObj instanceof Map) {
                    Map<?, ?> img = (Map<?, ?>) imgObj;
                    String recognizedText = "";
                    String url = "";
                    
                    Object recognizedTextObj = img.get("recognized_text");
                    Object urlObj = img.get("url");
                    
                    if (recognizedTextObj != null) {
                        recognizedText = recognizedTextObj.toString();
                    }
                    if (urlObj != null) {
                        url = urlObj.toString();
                    }
                    
                    // 替换循环模板中的变量
                    String currentLoop = loopTemplate
                            .replace("{{relateImgsText}}", recognizedText)
                            .replace("{{relateImgsUrl}}", url);
                    
                    loopResult.append(currentLoop);
                }
            }
        }
        
        return beforeLoop + loopResult.toString() + afterLoop;
    }
    
    /**
     * 处理流程节点循环标签
     */
    private String processFlowNodeLoop(String template, Map<String, Object> request) {
        String startTag = "{{flowNodeLoop}}";
        String endTag = "{{/flowNodeLoop}}";
        
        int startIndex = template.indexOf(startTag);
        int endIndex = template.indexOf(endTag);
        
        if (startIndex == -1 || endIndex == -1 || startIndex >= endIndex) {
            return template; // 没有找到循环标签，直接返回
        }
        
        String beforeLoop = template.substring(0, startIndex);
        String loopTemplate = template.substring(startIndex + startTag.length(), endIndex);
        String afterLoop = template.substring(endIndex + endTag.length());
        
        StringBuilder loopResult = new StringBuilder();
        
        // 遍历flowNodes数组
        Object flowNodesObj = request.get("flowNodes");
        if (flowNodesObj instanceof List) {
            List<?> flowNodes = (List<?>) flowNodesObj;
            for (Object nodeObj : flowNodes) {
                if (nodeObj instanceof Map) {
                    Map<?, ?> node = (Map<?, ?>) nodeObj;
                    String nodeName = "";
                    String nodeBuyerRequirement = "";
                    
                    Object nodeNameObj = node.get("nodeName");
                    Object nodeBuyerRequirementObj = node.get("nodeBuyerRequirement");
                    
                    if (nodeNameObj != null) {
                        nodeName = nodeNameObj.toString();
                    }
                    if (nodeBuyerRequirementObj != null) {
                        nodeBuyerRequirement = nodeBuyerRequirementObj.toString();
                    }
                    
                    // 替换循环模板中的变量
                    String currentLoop = loopTemplate
                            .replace("{{flowNodesName}}", nodeName)
                            .replace("{{flowNodesBuyerRequirement}}", nodeBuyerRequirement);
                    
                    loopResult.append(currentLoop);
                }
            }
        }
        
        return beforeLoop + loopResult.toString() + afterLoop;
    }
    
    /**
     * 构建默认系统提示词（当数据库中没有模板时使用）
     */
    private String buildDefaultSystemPrompt(Map<String, Object> request) {
        StringBuilder systemMessage = new StringBuilder();
        
        // 固定前缀
        String fixedPrefix = "你模拟一个客户，扮演成以下角色，和客服聊天，所提问题要聚焦在用户想要购买的产品上，按照第4步流程节点的顺序进行提问。具体的信息如下：";
        systemMessage.append(fixedPrefix);
        
        // 1. 买家背景信息
        String buyerRequirement = (String) request.get("buyerRequirement");
        if (buyerRequirement != null && !buyerRequirement.trim().isEmpty()) {
            systemMessage.append("买家背景信息是 : ").append(buyerRequirement).append(" ， ");
        }
        
        // 2. 买家想要购买的产品（只取第一个）
        Object productListObj = request.get("productList");
        if (productListObj instanceof List) {
            List<?> productList = (List<?>) productListObj;
            if (!productList.isEmpty() && productList.get(0) instanceof Map) {
                Map<?, ?> firstProduct = (Map<?, ?>) productList.get(0);
                Object productName = firstProduct.get("externalProductName");
                if (productName != null) {
                    systemMessage.append("买家想要购买的产品是 : ").append(productName).append("，");
                }
            }
        }
        
        // 3. 关联的图片(视频)信息 - 需要遍历数组
        Object relateImgsObj = request.get("relateImgs");
        if (relateImgsObj instanceof List) {
            List<?> relateImgs = (List<?>) relateImgsObj;
            for (Object imgObj : relateImgs) {
                if (imgObj instanceof Map) {
                    Map<?, ?> img = (Map<?, ?>) imgObj;
                    Object recognizedText = img.get("recognized_text");
                    Object url = img.get("url");
                    
                    if (recognizedText != null && url != null) {
                        systemMessage.append("关联的图片(视频)信息是 : ").append(recognizedText)
                                   .append(" ， 图片(视频)链接是 : ").append(url).append("，");
                    }
                }
            }
        }
        
        // 4. 流程节点信息 - 需要遍历数组
        Object flowNodesObj = request.get("flowNodes");
        if (flowNodesObj instanceof List) {
            List<?> flowNodes = (List<?>) flowNodesObj;
            if (!flowNodes.isEmpty()) {
                systemMessage.append("按照如下流程节点进行询问 : ");
                for (Object nodeObj : flowNodes) {
                    if (nodeObj instanceof Map) {
                        Map<?, ?> node = (Map<?, ?>) nodeObj;
                        Object nodeName = node.get("nodeName");
                        Object nodeBuyerRequirement = node.get("nodeBuyerRequirement");
                        
                        if (nodeName != null && nodeBuyerRequirement != null) {
                            systemMessage.append("流程节点名称：").append(nodeName)
                                       .append(" ，本流程买家要求： ").append(nodeBuyerRequirement).append("。");
                        }
                    }
                }
            }
        }
        
        // 5. 买家进线意图
        Object intentsObj = request.get("intents");
        if (intentsObj instanceof Map) {
            Map<?, ?> intents = (Map<?, ?>) intentsObj;
            Object parentName = intents.get("parentName");
            Object name = intents.get("name");
            
            if (parentName != null && name != null) {
                systemMessage.append("买家进线意图是 : ").append(parentName)
                           .append("，").append(name).append("，");
            }
        }
        
        // 结尾固定文本
        systemMessage.append("你需要首先向客服发起聊天。");
        
        return systemMessage.toString();
    }
    
    /**
     * 根据请求数据构建系统提示词数组（用于多个机器人）
     * @param request 请求数据
     * @return 构建好的系统提示词数组
     */
    private String[] buildSystemPromptsArrayFromRequest(Map<String, Object> request) {
        // 获取机器人数量，默认为4
        Integer robotCount = 4;
        Object robotCountObj = request.get("robotCount");
        if (robotCountObj instanceof Number) {
            robotCount = ((Number) robotCountObj).intValue();
        }
        
        // 处理scriptData
        String baseSystemPrompt;
        Object scriptDataObj = request.get("scriptData");
        if (scriptDataObj instanceof Map) {
            baseSystemPrompt = buildSystemPromptFromRequest((Map<String, Object>) scriptDataObj);
        } else {
            // 兼容旧版本：直接从request读取各个字段
            baseSystemPrompt = buildSystemPromptFromRequest(request);
        }
        
        // 创建数组，每个机器人使用相同的系统提示词
        String[] systemPrompts = new String[robotCount];
        for (int i = 0; i < robotCount; i++) {
            systemPrompts[i] = baseSystemPrompt;
        }
        
        return systemPrompts;
    }

    /**
     * 丰富返回结果，添加产品信息
     * @param result 返回结果
     * @param request 请求数据
     */
    private void enrichResultWithProductInfo(Map<String, Object> result, Map<String, Object> request) {
        // 添加产品列表信息
        Object productListObj = request.get("productList");
        if (productListObj instanceof List) {
            List<?> productList = (List<?>) productListObj;
            List<Map<String, Object>> processedProducts = new ArrayList<>();
            
            for (Object productObj : productList) {
                if (productObj instanceof Map) {
                    Map<?, ?> product = (Map<?, ?>) productObj;
                    Map<String, Object> processedProduct = new HashMap<>();
                    
                    // 提取产品信息
                    Object productId = product.get("externalProductId");
                    Object productName = product.get("externalProductName");
                    Object productLink = product.get("externalProductLink");
                    Object productImage = product.get("externalProductImage");
                    
                    if (productId != null) processedProduct.put("productId", productId);
                    if (productName != null) processedProduct.put("productName", productName);
                    if (productLink != null) processedProduct.put("productLink", productLink);
                    if (productImage != null) processedProduct.put("productImage", productImage);
                    
                    processedProducts.add(processedProduct);
                }
            }
            
            if (!processedProducts.isEmpty()) {
                result.put("productList", processedProducts);
            }
        }
    }

    /**
     * 关闭会话并清除资源
     */
    @MessageMapping("/close")
    public void closeSession(String message) {
        try {
            Map<String, Object> request = objectMapper.readValue(message, Map.class);
            String sessionId = (String) request.get("sessionId");

            if (sessionId == null || sessionId.trim().isEmpty()) {
                log.warn("关闭会话失败：sessionId为空");
                return;
            }

            log.info("收到关闭会话请求: sessionId={}", sessionId);

            // 调用BigModelManager清除会话资源
            bigModelManager.clearSession(sessionId);

            // 向客户端发送关闭确认
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "会话已关闭");
            response.put("sessionId", sessionId);

            String responseJson = objectMapper.writeValueAsString(response);
            messagingTemplate.convertAndSend("/topic/close/" + sessionId, responseJson);

            log.info("会话关闭完成: sessionId={}", sessionId);

        } catch (Exception e) {
            log.error("关闭会话失败", e);
            try {
                Map<String, Object> request = objectMapper.readValue(message, Map.class);
                String sessionId = (String) request.get("sessionId");

                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "关闭会话失败: " + e.getMessage());
                errorResponse.put("sessionId", sessionId);

                String errorJson = objectMapper.writeValueAsString(errorResponse);
                messagingTemplate.convertAndSend("/topic/close/" + sessionId, errorJson);
            } catch (Exception ex) {
                log.error("发送关闭会话错误响应失败", ex);
            }
        }
    }
}