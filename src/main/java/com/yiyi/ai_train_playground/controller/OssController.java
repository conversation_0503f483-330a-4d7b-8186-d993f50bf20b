package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.FileUploadDTO;
import com.yiyi.ai_train_playground.service.OssService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api")
public class OssController {
    private final OssService ossService;
    private final JwtUtil jwtUtil;

    public OssController(OssService ossService, JwtUtil jwtUtil) {
        this.ossService = ossService;
        this.jwtUtil = jwtUtil;
    }

    @PostMapping("/upload")
    public Result<List<FileUploadDTO>> upload(@RequestParam("file") MultipartFile[] files,
                                            @RequestParam("fileTypes") String[] fileTypes,
                                            HttpServletRequest request) {
        if (files == null || files.length == 0) {
            return Result.error("请选择要上传的文件");
        }
        if (fileTypes == null || fileTypes.length == 0) {
            return Result.error("请指定文件类型");
        }
        if (files.length != fileTypes.length) {
            return Result.error("文件数量与类型数量不匹配");
        }

        // 转换文件类型为Integer数组
        Integer[] types = new Integer[fileTypes.length];
        try {
            for (int i = 0; i < fileTypes.length; i++) {
                types[i] = Integer.parseInt(fileTypes[i]);
            }
        } catch (NumberFormatException e) {
            return Result.error("文件类型格式不正确，必须为数字");
        }

        // 从请求头获取token
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
        } else {
            return Result.error("未提供有效的认证信息");
        }

        // 从JWT中获取teamId
        Long teamId = jwtUtil.getTeamIdFromToken(token);
        if (teamId == null) {
            return Result.error("未获取到团队信息");
        }

        List<FileUploadDTO> results = new ArrayList<>();
        for (int i = 0; i < files.length; i++) {
            String url = ossService.upload(files[i], types[i], teamId);
            results.add(FileUploadDTO.of(types[i], url));
        }

        return Result.success(results);
    }

    @PostMapping("/geneProc")
    public Result<List<String>> geneProc(@RequestParam("fileName") String[] fileNames) {
        if (fileNames == null || fileNames.length == 0) {
            return Result.error("文件名不能为空");
        }

        List<String> urls = ossService.geneProc(fileNames);
        return Result.success(urls);
    }
} 