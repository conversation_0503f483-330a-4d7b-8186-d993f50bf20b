package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.EvaluationPlanShortDTO;
import com.yiyi.ai_train_playground.service.TrainEvaluationPlanService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 评价方案控制器
 * TEP = Train Evaluation Plan
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-03
 */
@Slf4j
@RestController
@RequestMapping("/api/tep")
@CrossOrigin(origins = "*")
public class TepController {

    @Autowired
    private TrainEvaluationPlanService trainEvaluationPlanService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 获取评价方案简短列表
     * 展示评价方案名称和评价分组名称
     *
     * @param request HTTP请求对象，用于获取JWT token
     * @return 评价方案简短列表
     */
    @GetMapping("/shortList")
    public Result<List<EvaluationPlanShortDTO>> getShortList(HttpServletRequest request) {
        try {
            // 从请求头获取token
            String token = extractToken(request);

            // 从JWT中获取团队ID
            Long teamId = jwtUtil.getTeamIdFromToken(token);
            if (teamId == null) {
                return Result.error("无法获取团队信息");
            }

            log.info("查询评价方案简短列表：teamId={}", teamId);

            List<EvaluationPlanShortDTO> result = trainEvaluationPlanService.getShortList(teamId);
            return Result.success("success", result);

        } catch (Exception e) {
            log.error("查询评价方案简短列表失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 从请求头中提取JWT token
     *
     * @param request HTTP请求
     * @return JWT token
     */
    private String extractToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            return token.substring(7);
        }
        throw new RuntimeException("未找到有效的认证token");
    }
}
