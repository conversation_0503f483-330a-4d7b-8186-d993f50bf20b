package com.yiyi.ai_train_playground.controller.user;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.entity.User;
import com.yiyi.ai_train_playground.service.user.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-21
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
@Tag(name = "用户管理", description = "用户相关接口")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 根据用户ID查询用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/{userId}")
    @Operation(summary = "根据用户ID查询用户信息", description = "根据用户ID查询用户详细信息，不需要认证")
    public Result<User> findByUserId(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long userId) {
        
        log.info("收到查询用户请求: userId={}", userId);
        
        try {
            // 参数校验
            if (userId == null || userId <= 0) {
                log.warn("用户ID参数无效: userId={}", userId);
                return Result.error(400, "用户ID不能为空且必须大于0");
            }
            
            // 查询用户信息
            User user = userService.findByUserId(userId);
            
            if (user == null) {
                log.warn("用户不存在: userId={}", userId);
                return Result.error(404, "用户不存在");
            }
            
            log.info("查询用户成功: userId={}, username={}", userId, user.getUsername());
            return Result.success("查询成功", user);
            
        } catch (Exception e) {
            log.error("查询用户失败: userId={}", userId, e);
            return Result.error(500, "查询用户失败: " + e.getMessage());
        }
    }
}
