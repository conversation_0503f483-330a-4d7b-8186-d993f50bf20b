package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.entity.ScriptGroup;
import com.yiyi.ai_train_playground.service.ScriptGroupService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import io.jsonwebtoken.Claims;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/api/script-groups")
public class ScriptGroupController {

    private final ScriptGroupService scriptGroupService;
    private final JwtUtil jwtUtil;

    public ScriptGroupController(ScriptGroupService scriptGroupService, JwtUtil jwtUtil) {
        this.scriptGroupService = scriptGroupService;
        this.jwtUtil = jwtUtil;
    }

    @GetMapping
    public Result<Map<String, Object>> getScriptGroups(
            @RequestParam(required = false) String groupTitle,
            @RequestHeader("Authorization") String authorization) {
        // 从Authorization header中提取token
        String token = authorization.replace("Bearer ", "");
        Claims claims = jwtUtil.parseToken(token);
        Long teamId = claims.get("teamId", Long.class);

        // 调用服务层方法获取分组树
        Map<String, Object> groupTree = scriptGroupService.getGroupTree(groupTitle, teamId);
        return Result.success(groupTree);
    }

    @PostMapping
    public Result<Void> save(@RequestBody ScriptGroup scriptGroup,
                           @RequestHeader("Authorization") String authorization) {
        String token = authorization.replace("Bearer ", "");
        Claims claims = jwtUtil.parseToken(token);
        Long teamId = claims.get("teamId", Long.class);
        
        scriptGroup.setTeamId(teamId);
        if (scriptGroupService.save(scriptGroup)) {
            return Result.success(null);
        }
        return Result.error("添加失败");
    }

    @PutMapping
    public Result<Void> update(@RequestBody ScriptGroup scriptGroup,
                             @RequestHeader("Authorization") String authorization) {
        String token = authorization.replace("Bearer ", "");
        Claims claims = jwtUtil.parseToken(token);
        Long teamId = claims.get("teamId", Long.class);
        
        // 验证是否有权限修改
        if (!teamId.equals(scriptGroup.getTeamId())) {
            return Result.error("无权修改其他团队的分组");
        }
        
        if (scriptGroupService.update(scriptGroup)) {
            return Result.success(null);
        }
        return Result.error("修改失败");
    }

    @DeleteMapping
    public Result<Void> delete(@RequestParam String ids,
                             @RequestHeader("Authorization") String authorization) {
        String token = authorization.replace("Bearer ", "");
        Claims claims = jwtUtil.parseToken(token);
        Long teamId = claims.get("teamId", Long.class);
        
        if (scriptGroupService.deleteByIds(ids, teamId)) {
            return Result.success(null);
        }
        return Result.error("删除失败");
    }
} 