package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.service.MultiMediaService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;

@Slf4j
@RestController
@RequestMapping("/api")
public class MultiMediaUploadController {

    private final MultiMediaService multiMediaService;
    private final JwtUtil jwtUtil;

    @Autowired
    public MultiMediaUploadController(MultiMediaService multiMediaService, JwtUtil jwtUtil) {
        this.multiMediaService = multiMediaService;
        this.jwtUtil = jwtUtil;
    }

    /**
     * 多媒体上传接口
     * @param file 文件
     * @param uploadType 上传类型 1:本地上传 2:URL上传
     * @param fileUrl 当uploadType为2时，传入的URL
     * @param promKW 提示词关键词，必填
     * @param token JWT令牌
     * @return Flux流式响应
     */
    @PostMapping(value = "/multi-media-upload", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> uploadMultiMedia(
            @RequestParam(value = "file", required = false) MultipartFile file,
            @RequestParam("uploadType") String uploadType,
            @RequestParam(value = "fileUrl", required = false) String fileUrl,
            @RequestParam("promKW") String promKW,
            @RequestHeader("Authorization") String token) {

        // 提取JWT token
        String jwtToken = null;
        try {
            if (token != null && token.startsWith("Bearer ")) {
                jwtToken = token.substring(7);  // 去掉Bearer前缀
            }
        } catch (Exception e) {
            log.warn("提取JWT token失败: {}", e.getMessage());
        }
        
        // 直接返回服务层的Flux流
        return multiMediaService.processMultiMediaUpload(file, uploadType, fileUrl, promKW, jwtToken, jwtUtil);
    }
} 