package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.entity.UserPurchasedProduct;
import com.yiyi.ai_train_playground.service.UserPurchasedProductService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import io.jsonwebtoken.Claims;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/purchasedProducts")
public class UserPurchasedProductController {

    private final UserPurchasedProductService userPurchasedProductService;
    private final JwtUtil jwtUtil;

    public UserPurchasedProductController(UserPurchasedProductService userPurchasedProductService,
                                        JwtUtil jwtUtil) {
        this.userPurchasedProductService = userPurchasedProductService;
        this.jwtUtil = jwtUtil;
    }

    @PostMapping
    public Result<List<UserPurchasedProduct>> findPurchasedProducts(@RequestHeader("Authorization") String authorization) {
        // 从Authorization header中提取token
        String token = authorization.replace("Bearer ", "");
        
        // 从token中获取teamId
        Claims claims = jwtUtil.parseToken(token);
        Long teamId = claims.get("teamId", Long.class);
        
        List<UserPurchasedProduct> products = userPurchasedProductService.findByTeamId(teamId);
        return Result.success(products);
    }
} 