package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.entity.BigmodelPrompts;
import com.yiyi.ai_train_playground.mapper.BigmodelPromptsMapper;
import com.yiyi.ai_train_playground.model.TextChunk;
import com.yiyi.ai_train_playground.model.VectorData;
import com.yiyi.ai_train_playground.service.BigmodelPromptsService;
import com.yiyi.ai_train_playground.service.HanLpService;
import com.yiyi.ai_train_playground.service.QdrantService;
import com.yiyi.ai_train_playground.service.SuperBigModelInterface;
import com.yiyi.ai_train_playground.service.impl.HtmlSanitizerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {
    
    @Autowired
    private HanLpService hanLpService;
    
    @Autowired
    private QdrantService qdrantService;
    
    @Autowired
    private SuperBigModelInterface bigModelService;
    
    @Autowired
    private HtmlSanitizerService htmlSanitizerService;
    
    @Autowired
    private BigmodelPromptsService bigmodelPromptsService;
    
    @Autowired
    private BigmodelPromptsMapper bigmodelPromptsMapper;
    
    /**
     * 测试HanLP语义分块功能
     * @param requestBody 包含text, chunkSize, overlapSize的请求体
     * @return 分块结果
     */
    @PostMapping("/test-chunking")
    public Result<List<TextChunk>> testChunking(@RequestBody Map<String, Object> requestBody) {
        try {
            String text = (String) requestBody.get("text");
            Integer chunkSize = (Integer) requestBody.get("chunkSize");
            Integer overlapSize = (Integer) requestBody.get("overlapSize");
            
            if (text == null || text.trim().isEmpty()) {
                return Result.error("文本不能为空");
            }
            
            if (chunkSize == null || chunkSize <= 0) {
                chunkSize = 300;
            }
            
            if (overlapSize == null || overlapSize < 0) {
                overlapSize = 60;
            }
            
            log.info("测试HanLP语义分块：textLength={}, chunkSize={}, overlapSize={}", 
                    text.length(), chunkSize, overlapSize);
            
            List<TextChunk> chunks = hanLpService.semanticChunking(text, chunkSize, overlapSize);
            
            log.info("HanLP语义分块测试成功：生成{}个分块", chunks.size());
            
            return Result.success("测试成功", chunks);
            
        } catch (Exception e) {
            log.error("HanLP语义分块测试失败", e);
            return Result.error("测试失败：" + e.getMessage());
        }
    }
    
    /**
     * 测试向量插入功能
     */
    @PostMapping("/test-vector-insert")
    public Result<Map<String, Object>> testVectorInsert(@RequestBody Map<String, Object> requestBody) {
        try {
            String text = (String) requestBody.get("text");
            String externalProductId = (String) requestBody.get("externalProductId");
            String teamId = (String) requestBody.get("teamId");
            
            if (text == null || text.trim().isEmpty()) {
                return Result.error("文本不能为空");
            }
            
            if (externalProductId == null) {
                externalProductId = "TEST_PROD_001";
            }
            
            if (teamId == null) {
                teamId = "1";
            }
            
            // Make variables final for lambda usage
            final String finalExternalProductId = externalProductId;
            final String finalTeamId = teamId;
            
            Map<String, Object> result = new HashMap<>();
            result.put("步骤", "开始测试");
            
            log.info("测试向量插入：text length={}, productId={}, teamId={}", 
                    text.length(), externalProductId, teamId);
            
            // 1. 语义分块
            List<TextChunk> chunks = hanLpService.semanticChunking(text, 300, 60);
            result.put("分块数量", chunks.size());
            log.info("语义分块完成，数量: {}", chunks.size());
            
            // 2. 向量化
            List<String> texts = chunks.stream()
                    .map(TextChunk::getContent)
                    .collect(Collectors.toList());
            
            List<List<Double>> embeddings = bigModelService.embed(texts);
            result.put("向量数量", embeddings.size());
            result.put("向量维度", embeddings.isEmpty() ? 0 : embeddings.get(0).size());
            log.info("向量化完成，数量: {}, 维度: {}", embeddings.size(), 
                    embeddings.isEmpty() ? 0 : embeddings.get(0).size());
            
            // 3. 准备向量数据
            List<VectorData> vectorDataList = chunks.stream()
                    .map(chunk -> {
                        int index = chunks.indexOf(chunk);
                        List<Double> embedding = embeddings.get(index);
                        
                        List<Float> floatEmbedding = embedding.stream()
                                .map(Double::floatValue)
                                .collect(Collectors.toList());
                        
                        Map<String, Object> payload = new HashMap<>();
                        payload.put("externalProductId", finalExternalProductId);
                        payload.put("teamId", finalTeamId);
                        payload.put("content", chunk.getContent());
                        payload.put("startPosition", chunk.getStartPosition());
                        payload.put("endPosition", chunk.getEndPosition());
                        payload.put("chunkIndex", index);
                        payload.put("timestamp", System.currentTimeMillis());
                        
                        String vectorId = java.util.UUID.randomUUID().toString();
                        
                        return VectorData.builder()
//                                .id(vectorId)
                                .vector(floatEmbedding)
                                .payload(payload)
                                .build();
                    })
                    .collect(Collectors.toList());
            
            result.put("准备的向量数据数量", vectorDataList.size());
            
            // 记录第一个向量的详细信息用于调试
            if (!vectorDataList.isEmpty()) {
                VectorData firstVector = vectorDataList.get(0);
                Map<String, Object> firstVectorInfo = new HashMap<>();
                firstVectorInfo.put("id", firstVector.getId());
                firstVectorInfo.put("vectorSize", firstVector.getVector().size());
                firstVectorInfo.put("payloadKeys", firstVector.getPayload().keySet());
                result.put("第一个向量信息", firstVectorInfo);
                
                log.info("第一个向量 - ID: {}, 向量维度: {}, 载荷键: {}", 
                        firstVector.getId(), firstVector.getVector().size(), firstVector.getPayload().keySet());
            }
            
            // 4. 插入向量库
            String collectionName = "train_prod_collection";
            boolean inserted = qdrantService.insertVectors(collectionName, vectorDataList);
            result.put("插入结果", inserted ? "成功" : "失败");
            
            if (inserted) {
                log.info("向量插入测试成功");
                return Result.success("测试成功", result);
            } else {
                log.error("向量插入测试失败");
                result.put("插入状态", "失败");
                return Result.success("测试完成，但插入失败", result);
            }
            
        } catch (Exception e) {
            log.error("向量插入测试异常", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("错误", e.getMessage());
            errorResult.put("堆栈跟踪", e.getClass().getSimpleName());
            return Result.success("测试完成，但出现异常", errorResult);
        }
    }
    
    /**
     * 测试HTML清理功能
     */
    @PostMapping("/test-html-sanitize")
    public Result<Map<String, Object>> testHtmlSanitize(@RequestBody Map<String, Object> requestBody) {
        try {
            String html = (String) requestBody.get("html");
            
            if (html == null || html.trim().isEmpty()) {
                return Result.error("HTML内容不能为空");
            }
            
            log.info("测试HTML清理：html length={}", html.length());
            
            Map<String, Object> result = new HashMap<>();
            result.put("原始HTML长度", html.length());
            result.put("原始HTML", html);
            
            // 执行HTML清理
            String cleanedText = htmlSanitizerService.sanitizeAndExtractText(html);
            
            result.put("清理后文本长度", cleanedText.length());
            result.put("清理后文本", cleanedText);
            
            log.info("HTML清理测试成功：原始长度={}, 清理后长度={}", html.length(), cleanedText.length());
            
            return Result.success("HTML清理测试成功", result);
            
        } catch (Exception e) {
            log.error("HTML清理测试失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("错误", e.getMessage());
            errorResult.put("堆栈跟踪", e.getClass().getSimpleName());
            return Result.success("测试完成，但出现异常", errorResult);
        }
    }

    @GetMapping("/init-multi-chats-template")
    public String initMultiChatsTemplate() {
        try {
            // 检查是否已存在
            try {
                List<String> existing = bigmodelPromptsService.getPromptsByKeyword("multi_chats:S");
                if (!existing.isEmpty()) {
                    return "多轮聊天提示词模板已存在：" + existing.get(0);
                }
            } catch (Exception e) {
                // 如果查询失败，可能是因为记录不存在，继续插入
                log.info("查询现有模板失败，将创建新模板", e);
            }
            
            // 创建新的提示词模板
            String template = "你模拟一个客户，扮演成以下角色，和客服聊天，所提问题要聚焦在用户想要购买的产品上，按照流程节点的顺序进行提问。具体的信息如下：\n\n" +
                    "买家背景信息：{{buyerRequirement}}\n\n" +
                    "买家想要购买的产品：{{externalProductName}}\n\n" +
                    "{{relateImgsTextListLoop}}关联的图片(视频)信息：{{relateImgsText}}，图片(视频)链接：{{relateImgsUrl}}\n{{/relateImgsTextListLoop}}\n\n" +
                    "按照如下流程节点进行询问：\n{{flowNodeLoop}}流程节点名称：{{flowNodesName}}，本流程买家要求：{{flowNodesBuyerRequirement}}\n{{/flowNodeLoop}}\n\n" +
                    "买家进线意图：{{intentPhrase}}，{{intentName}}\n\n" +
                    "你需要首先向客服发起聊天。";
            
            BigmodelPrompts prompt = new BigmodelPrompts();
            prompt.setKeyword("multi_chats");
            prompt.setSysPrompt(template);
            prompt.setUsrPrompt(null);
            prompt.setCreateTime(LocalDateTime.now());
            prompt.setUpdateTime(LocalDateTime.now());
            prompt.setCreator("system");
            prompt.setUpdater("system");
            prompt.setVersion(1L);
            
            // 插入数据库
            int result = bigmodelPromptsMapper.insert(prompt);
            if (result > 0) {
                return "提示词模板已成功创建并插入数据库：" + template;
            } else {
                return "插入数据库失败";
            }
            
        } catch (Exception e) {
            log.error("初始化多轮聊天提示词模板失败", e);
            return "初始化失败：" + e.getMessage();
        }
    }
    
    @GetMapping("/test-multi-chats-template")
    public String testMultiChatsTemplate() {
        try {
            List<String> prompts = bigmodelPromptsService.getPromptsByKeyword("multi_chats:S");
            if (prompts.isEmpty()) {
                return "未找到多轮聊天提示词模板，请先调用 /test/init-multi-chats-template 初始化";
            }
            return "找到提示词模板：" + prompts.get(0);
        } catch (Exception e) {
            log.error("测试多轮聊天提示词模板失败", e);
            return "测试失败：" + e.getMessage();
        }
    }

    @GetMapping("/test-template-replacement")
    public String testTemplateReplacement() {
        try {
            // 模拟请求数据
            Map<String, Object> request = new HashMap<>();
            request.put("buyerRequirement", "我是一个年轻的上班族，需要一台高性能的笔记本电脑用于工作和娱乐");
            
            // 产品列表
            List<Map<String, Object>> productList = new ArrayList<>();
            Map<String, Object> product = new HashMap<>();
            product.put("externalProductName", "联想ThinkPad X1 Carbon");
            productList.add(product);
            request.put("productList", productList);
            
            // 关联图片
            List<Map<String, Object>> relateImgs = new ArrayList<>();
            Map<String, Object> img1 = new HashMap<>();
            img1.put("recognized_text", "这是一台高性能笔记本电脑的产品展示图");
            img1.put("url", "https://example.com/laptop-image1.jpg");
            relateImgs.add(img1);
            
            Map<String, Object> img2 = new HashMap<>();
            img2.put("recognized_text", "展示了笔记本电脑的键盘和屏幕细节");
            img2.put("url", "https://example.com/laptop-image2.jpg");
            relateImgs.add(img2);
            request.put("relateImgs", relateImgs);
            
            // 流程节点
            List<Map<String, Object>> flowNodes = new ArrayList<>();
            Map<String, Object> node1 = new HashMap<>();
            node1.put("nodeName", "需求确认");
            node1.put("nodeBuyerRequirement", "确认笔记本电脑的具体配置需求");
            flowNodes.add(node1);
            
            Map<String, Object> node2 = new HashMap<>();
            node2.put("nodeName", "价格咨询");
            node2.put("nodeBuyerRequirement", "询问产品价格和优惠信息");
            flowNodes.add(node2);
            request.put("flowNodes", flowNodes);
            
            // 意图信息
            Map<String, Object> intents = new HashMap<>();
            intents.put("parentName", "产品咨询");
            intents.put("name", "笔记本电脑购买咨询");
            request.put("intents", intents);
            
            // 模拟提示词模板
            String template = "你模拟一个客户，扮演成以下角色，和客服聊天，所提问题要聚焦在用户想要购买的产品上，按照流程节点的顺序进行提问。具体的信息如下：\n\n" +
                    "买家背景信息：{{buyerRequirement}}\n\n" +
                    "买家想要购买的产品：{{externalProductName}}\n\n" +
                    "{{relateImgsTextListLoop}}关联的图片(视频)信息：{{relateImgsText}}，图片(视频)链接：{{relateImgsUrl}}\n{{/relateImgsTextListLoop}}\n\n" +
                    "按照如下流程节点进行询问：\n{{flowNodeLoop}}流程节点名称：{{flowNodesName}}，本流程买家要求：{{flowNodesBuyerRequirement}}\n{{/flowNodeLoop}}\n\n" +
                    "买家进线意图：{{intentPhrase}}，{{intentName}}\n\n" +
                    "你需要首先向客服发起聊天。";
            
            // 测试变量替换功能
            String result = replaceVariablesTest(template, request);
            
            return "模板替换测试成功：\n\n原始模板：\n" + template + "\n\n替换后结果：\n" + result;
            
        } catch (Exception e) {
            log.error("模板替换测试失败", e);
            return "模板替换测试失败：" + e.getMessage();
        }
    }
    
    /**
     * 测试用的变量替换方法（复制自ChatWebSocketController）
     */
    private String replaceVariablesTest(String template, Map<String, Object> request) {
        String result = template;
        
        // 3.1 替换 {{buyerRequirement}}
        String buyerRequirement = (String) request.get("buyerRequirement");
        if (buyerRequirement != null && !buyerRequirement.trim().isEmpty()) {
            result = result.replace("{{buyerRequirement}}", buyerRequirement);
        } else {
            result = result.replace("{{buyerRequirement}}", "");
        }
        
        // 3.2 替换 {{externalProductName}} - 取第一个产品的名称
        Object productListObj = request.get("productList");
        String productName = "";
        if (productListObj instanceof List) {
            List<?> productList = (List<?>) productListObj;
            if (!productList.isEmpty() && productList.get(0) instanceof Map) {
                Map<?, ?> firstProduct = (Map<?, ?>) productList.get(0);
                Object nameObj = firstProduct.get("externalProductName");
                if (nameObj != null) {
                    productName = nameObj.toString();
                }
            }
        }
        result = result.replace("{{externalProductName}}", productName);
        
        // 3.3 处理 {{relateImgsTextListLoop}}{{/relateImgsTextListLoop}} 循环标签
        result = processRelateImgsLoopTest(result, request);
        
        // 3.4 处理 {{flowNodeLoop}}{{/flowNodeLoop}} 循环标签
        result = processFlowNodeLoopTest(result, request);
        
        // 3.5 替换意图相关变量
        Object intentsObj = request.get("intents");
        String intentPhrase = "";
        String intentName = "";
        if (intentsObj instanceof Map) {
            Map<?, ?> intents = (Map<?, ?>) intentsObj;
            Object parentNameObj = intents.get("parentName");
            Object nameObj = intents.get("name");
            if (parentNameObj != null) {
                intentPhrase = parentNameObj.toString();
            }
            if (nameObj != null) {
                intentName = nameObj.toString();
            }
        }
        result = result.replace("{{intentPhrase}}", intentPhrase);
        result = result.replace("{{intentName}}", intentName);
        
        return result;
    }
    
    /**
     * 测试用的关联图片循环处理方法
     */
    private String processRelateImgsLoopTest(String template, Map<String, Object> request) {
        String startTag = "{{relateImgsTextListLoop}}";
        String endTag = "{{/relateImgsTextListLoop}}";
        
        int startIndex = template.indexOf(startTag);
        int endIndex = template.indexOf(endTag);
        
        if (startIndex == -1 || endIndex == -1 || startIndex >= endIndex) {
            return template; // 没有找到循环标签，直接返回
        }
        
        String beforeLoop = template.substring(0, startIndex);
        String loopTemplate = template.substring(startIndex + startTag.length(), endIndex);
        String afterLoop = template.substring(endIndex + endTag.length());
        
        StringBuilder loopResult = new StringBuilder();
        
        // 遍历relateImgs数组
        Object relateImgsObj = request.get("relateImgs");
        if (relateImgsObj instanceof List) {
            List<?> relateImgs = (List<?>) relateImgsObj;
            for (Object imgObj : relateImgs) {
                if (imgObj instanceof Map) {
                    Map<?, ?> img = (Map<?, ?>) imgObj;
                    String recognizedText = "";
                    String url = "";
                    
                    Object recognizedTextObj = img.get("recognized_text");
                    Object urlObj = img.get("url");
                    
                    if (recognizedTextObj != null) {
                        recognizedText = recognizedTextObj.toString();
                    }
                    if (urlObj != null) {
                        url = urlObj.toString();
                    }
                    
                    // 替换循环模板中的变量
                    String currentLoop = loopTemplate
                            .replace("{{relateImgsText}}", recognizedText)
                            .replace("{{relateImgsUrl}}", url);
                    
                    loopResult.append(currentLoop);
                }
            }
        }
        
        return beforeLoop + loopResult.toString() + afterLoop;
    }
    
    /**
     * 测试用的流程节点循环处理方法
     */
    private String processFlowNodeLoopTest(String template, Map<String, Object> request) {
        String startTag = "{{flowNodeLoop}}";
        String endTag = "{{/flowNodeLoop}}";
        
        int startIndex = template.indexOf(startTag);
        int endIndex = template.indexOf(endTag);
        
        if (startIndex == -1 || endIndex == -1 || startIndex >= endIndex) {
            return template; // 没有找到循环标签，直接返回
        }
        
        String beforeLoop = template.substring(0, startIndex);
        String loopTemplate = template.substring(startIndex + startTag.length(), endIndex);
        String afterLoop = template.substring(endIndex + endTag.length());
        
        StringBuilder loopResult = new StringBuilder();
        
        // 遍历flowNodes数组
        Object flowNodesObj = request.get("flowNodes");
        if (flowNodesObj instanceof List) {
            List<?> flowNodes = (List<?>) flowNodesObj;
            for (Object nodeObj : flowNodes) {
                if (nodeObj instanceof Map) {
                    Map<?, ?> node = (Map<?, ?>) nodeObj;
                    String nodeName = "";
                    String nodeBuyerRequirement = "";
                    
                    Object nodeNameObj = node.get("nodeName");
                    Object nodeBuyerRequirementObj = node.get("nodeBuyerRequirement");
                    
                    if (nodeNameObj != null) {
                        nodeName = nodeNameObj.toString();
                    }
                    if (nodeBuyerRequirementObj != null) {
                        nodeBuyerRequirement = nodeBuyerRequirementObj.toString();
                    }
                    
                    // 替换循环模板中的变量
                    String currentLoop = loopTemplate
                            .replace("{{flowNodesName}}", nodeName)
                            .replace("{{flowNodesBuyerRequirement}}", nodeBuyerRequirement);
                    
                    loopResult.append(currentLoop);
                }
            }
        }
        
        return beforeLoop + loopResult.toString() + afterLoop;
    }
} 