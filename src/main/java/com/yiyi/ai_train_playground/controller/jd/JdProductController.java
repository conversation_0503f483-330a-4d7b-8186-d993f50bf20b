package com.yiyi.ai_train_playground.controller.jd;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.JdPrdDtlResponse;
import com.yiyi.ai_train_playground.dto.JdPrdListRequest;
import com.yiyi.ai_train_playground.dto.JdPrdListResponse;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import com.yiyi.ai_train_playground.service.jd.TrainJdProductsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 京东商品控制器
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@Tag(name = "京东商品管理", description = "京东商品相关接口")
public class JdProductController {

    private final TrainJdProductsService trainJdProductsService;

    /**
     * 分页查询京东商品列表
     *
     * @param jdPrdTitle 商品标题（模糊查询）
     * @param page 页号，从1开始
     * @param pageSize 每页大小
     * @return 商品列表响应结果
     */
    @GetMapping("/jd-prd-list")
    @Operation(summary = "分页查询京东商品列表", description = "支持按商品标题模糊查询的分页接口")
    public Result<JdPrdListResponse> getJdProductList(
            @Parameter(description = "商品标题（模糊查询）", example = "小米手环")
            @RequestParam(value = "jdPrdTitle", required = false) String jdPrdTitle,
            
            @Parameter(description = "页号，从1开始", example = "1")
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            
            @Parameter(description = "每页大小", example = "10")
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        
        try {
            log.info("查询京东商品列表: jdPrdTitle={}, page={}, pageSize={}", jdPrdTitle, page, pageSize);
            
            // 构建请求参数
            JdPrdListRequest request = new JdPrdListRequest();
            request.setJdPrdTitle(jdPrdTitle);
            request.setPage(page);
            request.setPageSize(pageSize);

            // 从SecurityContext中获取当前用户的teamId
            Long teamId = SecurityUtil.getCurrentTeamId();
            if (teamId == null) {
                log.warn("无法从SecurityContext获取teamId，用户可能未正确登录");
                return Result.error("用户认证信息无效，请重新登录");
            }
            
            // 调用服务层查询
            JdPrdListResponse response = trainJdProductsService.findJdProductList(teamId, request);
            
            log.info("查询京东商品列表成功: 总数={}, 返回数量={}",
                    response.getTotal(), response.getRows().size());

            return Result.success(response);

        } catch (Exception e) {
            log.error("查询京东商品列表失败: jdPrdTitle={}, page={}, pageSize={}",
                    jdPrdTitle, page, pageSize, e);
            return Result.error("查询京东商品列表失败: " + e.getMessage());
        }
    }

    /**
     * 查询京东商品详情
     *
     * @param wareId 京东商品ID
     * @return 商品详情响应结果
     */
    @GetMapping("/jd-prd-dtl/{wareId}")
    @Operation(summary = "查询京东商品详情", description = "根据wareId查询京东商品详情信息")
    public Result<JdPrdDtlResponse> getJdProductDetail(
            @Parameter(description = "京东商品ID", example = "12345", required = true)
            @PathVariable("wareId") Long wareId) {

        try {
            log.info("查询京东商品详情: wareId={}", wareId);

            // 从SecurityContext中获取当前用户的teamId
            Long teamId = SecurityUtil.getCurrentTeamId();
            if (teamId == null) {
                log.warn("无法从SecurityContext获取teamId，用户可能未正确登录");
                return Result.error("用户认证信息无效，请重新登录");
            }

            // 调用服务层查询
            JdPrdDtlResponse response = trainJdProductsService.findJdProductDetail(teamId, wareId);

            if (response == null) {
                log.warn("未找到京东商品详情: wareId={}", wareId);
                return Result.error("未找到指定的商品详情");
            }

            log.info("查询京东商品详情成功: wareId={}", wareId);

            return Result.success(response);

        } catch (Exception e) {
            log.error("查询京东商品详情失败: wareId={}", wareId, e);
            return Result.error("查询京东商品详情失败: " + e.getMessage());
        }
    }
}
