package com.yiyi.ai_train_playground.controller.jd;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.entity.TrainJdProducts;
import com.yiyi.ai_train_playground.service.jd.YiYiJdService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 易易京东服务测试控制器
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("/api/test/yiyi-jd")
@RequiredArgsConstructor
public class YiYiJdTestController {
    
    private final YiYiJdService yiYiJdService;
    
    /**
     * 测试带动态accessToken的商品列表接口
     * 
     * @param request 请求参数，包含accessToken、pageNo、pageSize
     * @return 接口响应
     */
    @PostMapping("/products-with-token")
    public Result<List<TrainJdProducts>> testGetWare4ValidProductListWithToken(@RequestBody Map<String, Object> request) {
        try {
            String accessToken = (String) request.get("accessToken");
            Integer pageNo = request.containsKey("pageNo") ? ((Number) request.get("pageNo")).intValue() : 1;
            Integer pageSize = request.containsKey("pageSize") ? ((Number) request.get("pageSize")).intValue() : 10;
            
            log.info("测试京东商品接口（动态accessToken），参数：pageNo={}, pageSize={}, accessToken={}****", 
                    pageNo, pageSize, 
                    accessToken != null && accessToken.length() > 4 ? accessToken.substring(0, 4) : "null");
            
            List<TrainJdProducts> result = yiYiJdService.getWare4ValidProductList(accessToken, pageNo, pageSize);
            
            if (result != null) {
                return Result.success(result);
            } else {
                return Result.error("获取商品列表失败");
            }
            
        } catch (Exception e) {
            log.error("测试京东商品接口失败（动态accessToken）", e);
            return Result.error("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试带参数的商品列表接口（使用默认accessToken）
     * 
     * @param request 请求参数
     * @return 接口响应
     */
    @PostMapping("/products")
    public Result<List<TrainJdProducts>> testGetWare4ValidProductList(@RequestBody Map<String, Object> request) {
        try {
            Integer pageNo = request.containsKey("pageNo") ? ((Number) request.get("pageNo")).intValue() : 1;
            Integer pageSize = request.containsKey("pageSize") ? ((Number) request.get("pageSize")).intValue() : 10;
            
            log.info("测试京东商品接口（默认accessToken），参数：pageNo={}, pageSize={}", pageNo, pageSize);
            
            List<TrainJdProducts> result = yiYiJdService.getWare4ValidProductList(pageNo, pageSize);
            
            if (result != null) {
                return Result.success(result);
            } else {
                return Result.error("获取商品列表失败");
            }
            
        } catch (Exception e) {
            log.error("测试京东商品接口失败（默认accessToken）", e);
            return Result.error("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试无参数的商品列表接口（使用默认参数和accessToken）
     * 
     * @return 接口响应
     */
    @GetMapping("/products-default")
    public Result<List<TrainJdProducts>> testGetWare4ValidProductListDefault() {
        try {
            log.info("测试京东商品接口（默认所有参数）");
            
            List<TrainJdProducts> result = yiYiJdService.getWare4ValidProductList();
            
            if (result != null) {
                return Result.success(result);
            } else {
                return Result.error("获取商品列表失败");
            }
            
        } catch (Exception e) {
            log.error("测试京东商品接口失败（默认参数）", e);
            return Result.error("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证accessToken格式是否有效
     * 
     * @param request 包含accessToken的请求
     * @return 验证结果
     */
    @PostMapping("/validate-token")
    public Result<Map<String, Object>> validateAccessToken(@RequestBody Map<String, Object> request) {
        try {
            String accessToken = (String) request.get("accessToken");
            
            // 简单的格式验证
            boolean isValid = accessToken != null && !accessToken.trim().isEmpty() && accessToken.length() >= 20;
            
            Map<String, Object> result = Map.of(
                "accessToken", accessToken != null && accessToken.length() > 4 ? 
                        accessToken.substring(0, 4) + "****" : "null",
                "isValid", isValid,
                "length", accessToken != null ? accessToken.length() : 0,
                "timestamp", System.currentTimeMillis()
            );
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("验证accessToken失败", e);
            return Result.error("验证失败: " + e.getMessage());
        }
    }
} 