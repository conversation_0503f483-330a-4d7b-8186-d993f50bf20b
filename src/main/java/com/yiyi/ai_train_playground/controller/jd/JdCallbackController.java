package com.yiyi.ai_train_playground.controller.jd;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.config.JdConfig;
import com.yiyi.ai_train_playground.dto.JdCallbackRequest;
import com.yiyi.ai_train_playground.dto.JdCallbackResult;
import com.yiyi.ai_train_playground.service.jd.JdCallbackService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 京东回调接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@Controller
@RequiredArgsConstructor
@Tag(name = "京东回调接口", description = "处理京东OAuth2授权回调")
public class JdCallbackController {
    
    private final JdCallbackService jdCallbackService;
    private final JdConfig jdConfig;
    private final JwtUtil jwtUtil;
    
    /**
     * 京东回调接口
     * 
     * @param state 随机数状态码
     * @param code 京东返回的授权码
     * @param request HTTP请求对象
     * @return 重定向到前端页面
     */
    @GetMapping("/api/yiyicallback")
    @Operation(
        summary = "京东OAuth2回调接口",
        description = "接收京东OAuth2授权回调，验证state参数，调用京东API获取access_token并保存到数据库，设置授权和同步状态",
        parameters = {
            @Parameter(name = "state", description = "随机数状态码，用于防止CSRF攻击", required = true, example = "YyJdPlayground2025"),
            @Parameter(name = "code", description = "京东返回的授权码", required = true, example = "test-code-123")
        }
    )
    public String handleJdCallback(@RequestParam("state") String state, 
                                   @RequestParam("code") String code,
                                   HttpServletRequest request) {
        log.info("收到京东回调请求，state: {}, code: {}", state, code);
        
        try {
            // 1. 从JWT获取用户信息
            Long teamId = 0L;
            Long userId = 0L;
            String username = "system";
            
            try {
                String token = extractTokenFromRequest(request);
                if (token != null) {
                    teamId = jwtUtil.getTeamIdFromToken(token);
                    userId = jwtUtil.getUserIdFromToken(token);
                    username = jwtUtil.getUsernameFromToken(token);
                    log.info("从JWT获取用户信息：teamId={}, userId={}, username={}", teamId, userId, username);
                }
            } catch (Exception e) {
                log.warn("从JWT获取用户信息失败，使用默认值: {}", e.getMessage());
            }
            
            // 如果无法获取teamId或userId，使用默认值
            if (teamId == null) teamId = 0L;
            if (userId == null) userId = 0L;
            if (username == null) username = "system";
            
            // 2. 构建请求DTO
            JdCallbackRequest callbackRequest = new JdCallbackRequest();
            callbackRequest.setState(state);
            callbackRequest.setCode(code);
            callbackRequest.setTeamId(teamId);
            callbackRequest.setUserId(userId);
            callbackRequest.setUsername(username);
            
            // 3. 调用服务层处理业务逻辑
            JdCallbackResult result = jdCallbackService.handleCallback(callbackRequest);
            
            // 4. 根据处理结果重定向
            if (result.isSuccess()) {
                return "redirect:" + jdConfig.getRedirectBaseUrl() + 
                       "?isAuthorize=" + (result.isAuthorize() ? 1 : 0) + 
                       "&is_sync_complete=" + (result.isSyncComplete() ? 1 : 0);
            } else {
                log.error("京东回调处理失败: {}", result.getErrorMessage());
                return "redirect:" + jdConfig.getRedirectBaseUrl() + "?isAuthorize=0&is_sync_complete=0";
            }
            
        } catch (Exception e) {
            log.error("处理京东回调时发生异常", e);
            return "redirect:" + jdConfig.getRedirectBaseUrl() + "?isAuthorize=0&is_sync_complete=0";
        }
    }
    

    
    /**
     * 从HTTP请求中提取JWT token
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        
        // 也可以从参数中获取token
        String tokenParam = request.getParameter("token");
        if (tokenParam != null && !tokenParam.isEmpty()) {
            return tokenParam;
        }
        
        return null;
    }
} 