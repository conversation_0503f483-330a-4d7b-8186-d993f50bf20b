package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.mapper.TrainProductMapper;
import com.yiyi.ai_train_playground.model.ExcelImportResponse;
import com.yiyi.ai_train_playground.model.ExcelImportResponse.ProductItem;
import com.yiyi.ai_train_playground.model.ExcelImportResponse.FailedItem;
import com.yiyi.ai_train_playground.service.impl.OssServiceImpl;
import com.yiyi.ai_train_playground.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("/api")
public class ExcelImportController {

    private final JwtUtil jwtUtil;
    private final TrainProductMapper trainProductMapper;
    private final OssServiceImpl ossService;

    @Autowired
    public ExcelImportController(JwtUtil jwtUtil, TrainProductMapper trainProductMapper, OssServiceImpl ossService) {
        this.jwtUtil = jwtUtil;
        this.trainProductMapper = trainProductMapper;
        this.ossService = ossService;
    }

    /**
     * 导入Excel文件解析商品数据
     *
     * @param file         Excel文件
     * @param positionType 位置类型 0:新增剧本时导入，1：商品管理中导入
     * @param token        JWT令牌
     * @return 解析结果
     */
    @PostMapping("/excel-import")
    @Transactional
    public Result<ExcelImportResponse> importExcel(
            @RequestParam("file") MultipartFile file,
            @RequestParam("positionType") Integer positionType,
            @RequestHeader("Authorization") String token) {

        // 从JWT中获取teamId和userId
        String jwtToken = token.substring(7);  // 去掉Bearer前缀
        Long teamId = jwtUtil.getTeamIdFromToken(jwtToken);
        Long userId = jwtUtil.getUserIdFromToken(jwtToken);

        // 检查文件是否为空
        if (file.isEmpty()) {
            return Result.error("请选择要上传的Excel文件");
        }

        // 检查文件格式
        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            return Result.error("上传文件格式不正确，请上传Excel文件(.xlsx或.xls)");
        }

        try {
            // 读取Excel文件
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            // 检查是否有数据
            if (sheet.getPhysicalNumberOfRows() <= 1) {
                return Result.error("Excel文件中没有数据");
            }

            // 验证表头
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                return Result.error("Excel文件格式不正确，缺少表头");
            }

            // 表头验证
            String[] expectedHeaders = {"商品编号", "商品标题", "商品链接", "商品图片（可选）"};
            for (int i = 0; i < expectedHeaders.length; i++) {
                Cell cell = headerRow.getCell(i);
                if (cell == null || !expectedHeaders[i].equals(cell.getStringCellValue())) {
                    return Result.error("表头格式不正确，应为：商品编号、商品标题、商品链接、商品图片（可选）");
                }
            }

            // 检查数据行数 - 最多允许11行
            int dataRows = sheet.getPhysicalNumberOfRows() - 1; // 减去表头
            if (dataRows > 11) {
                return Result.error("条数超过11条");
            }

            // 解析数据
            List<ProductItem> successItems = new ArrayList<>();
            List<FailedItem> failedItems = new ArrayList<>();
            List<String[]> allRowData = new ArrayList<>(); // 存储所有行数据，用于创建错误明细

            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                try {
                    // 检查是否为空行
                    boolean isEmpty = true;
                    for (int j = 0; j < 3; j++) { // 检查前三列是否全为空
                        Cell cell = row.getCell(j);
                        if (cell != null && cell.getCellType() != CellType.BLANK) {
                            isEmpty = false;
                            break;
                        }
                    }
                    if (isEmpty) continue;

                    String productId = getCellValueAsString(row.getCell(0));
                    String productName = getCellValueAsString(row.getCell(1));
                    String productLink = getCellValueAsString(row.getCell(2));
                    String productImage = getCellValueAsString(row.getCell(3)); // 可选

                    // 存储行数据
                    String[] rowData = {productId, productName, productLink, productImage};
                    allRowData.add(rowData);

                    String errorReason = null;

                    // 验证必填字段
                    if (productId.isEmpty() || productName.isEmpty() || productLink.isEmpty()) {
                        errorReason = "商品编号、商品标题和商品链接不能为空";
                    }
                    // 验证链接格式
                    else if (!isValidUrl(productLink)) {
                        errorReason = "商品链接格式不正确";
                    }
                    // 检查商品编号唯一性
                    else if (trainProductMapper.existsByExternalProductId(productId, teamId) > 0) {
                        errorReason = productId + "已存在，请导入新的产品编号，或者删除原来编号为" + productId + "的产品";
                    }

                    if (errorReason != null) {
                        FailedItem failedItem = new FailedItem();
                        failedItem.setRow(i + 1);
                        failedItem.setExternalProductId(productId.isEmpty() ? "缺失" : productId);
                        failedItem.setReason(errorReason);
                        failedItems.add(failedItem);
                        continue;
                    }

                    // 如果positionType为1，则插入数据库
                    if (positionType == 1) {
                        trainProductMapper.insertProduct(
                                teamId,
                                productId,
                                productName,
                                productLink,
                                productImage.isEmpty() ? null : productImage,
                                "在售", // 默认状态
                                "-", // 默认分类
                                "-", // 默认标签
                                "-", // 默认平台
                                "-", // 默认店铺ID
                                "-", // 默认店铺名称
                                userId != null ? userId.toString() : "system",
                                userId != null ? userId.toString() : "system"
                        );
                    }

                    // 添加成功项
                    ProductItem successItem = new ProductItem();
                    successItem.setExternalProductId(productId);
                    successItem.setExternalProductName(productName);
                    successItem.setExternalProductLink(productLink);
                    if (!productImage.isEmpty()) {
                        successItem.setExternalProductImage(productImage);
                    }
                    successItems.add(successItem);

                } catch (Exception e) {
                    log.error("解析第{}行数据出错: {}", i + 1, e.getMessage());
                    FailedItem failedItem = new FailedItem();
                    failedItem.setRow(i + 1);
                    failedItem.setExternalProductId(getCellValueAsString(row.getCell(0)));
                    failedItem.setReason("数据格式错误: " + e.getMessage());
                    failedItems.add(failedItem);
                }
            }

            // 生成错误明细Excel并上传
            String failedExcelUrl = null;
            if (!failedItems.isEmpty()) {
                failedExcelUrl = createAndUploadFailedExcel(allRowData, failedItems, teamId);
            }

            // 构建响应
            ExcelImportResponse response = new ExcelImportResponse();
            response.setTotalCount(dataRows);
            response.setSuccessCount(successItems.size());
            response.setFailCount(failedItems.size());
            response.setSuccessItems(successItems);
            response.setFailedItems(failedItems);
            response.setFailedExcelUrl(failedExcelUrl);

            workbook.close();
            return Result.success("导入成功", response);

        } catch (IOException e) {
            log.error("解析Excel文件失败", e);
            return Result.error("解析Excel文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取单元格的字符串值
     *
     * @param cell 单元格
     * @return 字符串值
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 将数字转为字符串，避免科学计数法
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue();
                } catch (Exception e) {
                    try {
                        return String.valueOf(cell.getNumericCellValue());
                    } catch (Exception ex) {
                        return cell.getCellFormula();
                    }
                }
            default:
                return "";
        }
    }

    /**
     * 简单验证URL格式
     *
     * @param url URL字符串
     * @return 是否有效
     */
    private boolean isValidUrl(String url) {
        return url.startsWith("http://") || url.startsWith("https://");
    }

    /**
     * 创建错误明细Excel并上传到OSS
     *
     * @param allRowData  所有行数据
     * @param failedItems 失败项列表
     * @param teamId      团队ID
     * @return 上传后的文件URL
     */
    private String createAndUploadFailedExcel(List<String[]> allRowData, List<FailedItem> failedItems, Long teamId) {
        try {
            // 创建新的工作簿
            Workbook errorWorkbook = new XSSFWorkbook();
            
            // 创建错误明细sheet
            Sheet errorSheet = errorWorkbook.createSheet("错误明细");
            
            // 创建表头
            Row headerRow = errorSheet.createRow(0);
            String[] headers = {"商品编号", "商品标题", "商品链接", "商品图片（可选）", "错误理由"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            
            // 创建失败项行数据
            Map<Integer, String> failedRowReasons = new HashMap<>();
            for (FailedItem failedItem : failedItems) {
                failedRowReasons.put(failedItem.getRow() - 2, failedItem.getReason()); // row是Excel行号(1-based)，减2得到allRowData索引(0-based)
            }
            
            int rowIndex = 1;
            for (int i = 0; i < allRowData.size(); i++) {
                if (failedRowReasons.containsKey(i)) {
                    Row row = errorSheet.createRow(rowIndex++);
                    String[] rowData = allRowData.get(i);
                    
                    // 填充原数据
                    for (int j = 0; j < rowData.length; j++) {
                        Cell cell = row.createCell(j);
                        cell.setCellValue(rowData[j] != null ? rowData[j] : "");
                    }
                    
                    // 填充错误理由
                    Cell errorCell = row.createCell(4);
                    errorCell.setCellValue(failedRowReasons.get(i));
                }
            }
            
            // 将工作簿转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            errorWorkbook.write(baos);
            errorWorkbook.close();
            
            // 创建MultipartFile
            byte[] excelBytes = baos.toByteArray();
            String filename = "错误明细_" + System.currentTimeMillis() + ".xlsx";
            MultipartFile errorFile = new CustomMultipartFile(filename, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", excelBytes);
            
            // 上传到OSS
            return ossService.upload(errorFile, 0, teamId);
            
        } catch (Exception e) {
            log.error("创建错误明细Excel失败", e);
            return null;
        }
    }

    /**
     * 自定义MultipartFile实现
     */
    private static class CustomMultipartFile implements MultipartFile {
        private final String name;
        private final String contentType;
        private final byte[] content;

        public CustomMultipartFile(String name, String contentType, byte[] content) {
            this.name = name;
            this.contentType = contentType;
            this.content = content;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return name;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return content == null || content.length == 0;
        }

        @Override
        public long getSize() {
            return content.length;
        }

        @Override
        public byte[] getBytes() {
            return content;
        }

        @Override
        public java.io.InputStream getInputStream() {
            return new java.io.ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(java.io.File dest) throws IOException, IllegalStateException {
            try (java.io.FileOutputStream fos = new java.io.FileOutputStream(dest)) {
                fos.write(content);
            }
        }
    }
} 