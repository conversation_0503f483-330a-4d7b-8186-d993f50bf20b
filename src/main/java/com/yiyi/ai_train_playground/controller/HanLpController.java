package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.model.TextChunk;
import com.yiyi.ai_train_playground.service.HanLpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * HanLP自然语言处理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/hanlp")
public class HanLpController {

    private final HanLpService hanLpService;

    @Autowired
    public HanLpController(HanLpService hanLpService) {
        this.hanLpService = hanLpService;
    }

    /**
     * 智能语义分块接口
     * 
     * @param text 待分块的文本
     * @param chunkSize 分块大小（字符数），默认500
     * @param overlapSize 重叠字符数，默认50
     * @return 分块结果
     */
    @PostMapping("/semantic-chunking")
    public Result<List<TextChunk>> semanticChunking(
            @RequestParam("text") String text,
            @RequestParam(value = "chunkSize", defaultValue = "500") int chunkSize,
            @RequestParam(value = "overlapSize", defaultValue = "50") int overlapSize) {
        
        try {
            log.info("收到语义分块请求: 文本长度={}, 分块大小={}, 重叠字符数={}", 
                    text.length(), chunkSize, overlapSize);
            
            List<TextChunk> chunks = hanLpService.semanticChunking(text, chunkSize, overlapSize);
            
            log.info("语义分块完成: 生成{}个分块", chunks.size());
            return Result.success("分块成功", chunks);
            
        } catch (IllegalArgumentException e) {
            log.warn("参数错误: {}", e.getMessage());
            return Result.error(400, e.getMessage());
        } catch (Exception e) {
            log.error("语义分块失败", e);
            return Result.error(500, "分块处理失败: " + e.getMessage());
        }
    }

    /**
     * 文本分词接口
     * 
     * @param text 待分词的文本
     * @return 分词结果
     */
    @PostMapping("/segment")
    public Result<List<TextChunk.SemanticUnit>> segment(@RequestParam("text") String text) {
        try {
            log.info("收到分词请求: 文本长度={}", text.length());
            
            List<TextChunk.SemanticUnit> units = hanLpService.segment(text);
            
            log.info("分词完成: 生成{}个词语", units.size());
            return Result.success("分词成功", units);
            
        } catch (Exception e) {
            log.error("分词失败", e);
            return Result.error(500, "分词处理失败: " + e.getMessage());
        }
    }

    /**
     * 关键词提取接口
     * 
     * @param text 待提取的文本
     * @param topK 返回前K个关键词，默认10
     * @return 关键词列表
     */
    @PostMapping("/keywords")
    public Result<List<String>> extractKeywords(
            @RequestParam("text") String text,
            @RequestParam(value = "topK", defaultValue = "10") int topK) {
        
        try {
            log.info("收到关键词提取请求: 文本长度={}, topK={}", text.length(), topK);
            
            List<String> keywords = hanLpService.extractKeywords(text, topK);
            
            log.info("关键词提取完成: 提取{}个关键词", keywords.size());
            return Result.success("提取成功", keywords);
            
        } catch (Exception e) {
            log.error("关键词提取失败", e);
            return Result.error(500, "关键词提取失败: " + e.getMessage());
        }
    }

    /**
     * 摘要提取接口
     * 
     * @param text 待提取的文本
     * @param maxSentences 最大句子数，默认3
     * @return 摘要文本
     */
    @PostMapping("/summary")
    public Result<String> extractSummary(
            @RequestParam("text") String text,
            @RequestParam(value = "maxSentences", defaultValue = "3") int maxSentences) {
        
        try {
            log.info("收到摘要提取请求: 文本长度={}, 最大句子数={}", text.length(), maxSentences);
            
            String summary = hanLpService.extractSummary(text, maxSentences);
            
            log.info("摘要提取完成: 摘要长度={}", summary.length());
            return Result.success("提取成功", summary);
            
        } catch (Exception e) {
            log.error("摘要提取失败", e);
            return Result.error(500, "摘要提取失败: " + e.getMessage());
        }
    }

    /**
     * 批量测试接口 - 用于演示语义分块效果
     * 
     * @return 测试结果
     */
    @GetMapping("/test")
    public Result<Object> test() {
        String testText = "这款手机重量2500g，屏幕尺寸6.7英寸，电池容量4500毫安时，价格3999元。" +
                         "处理器采用骁龙888型号，内存容量8GB，存储空间256GB。" +
                         "相机配置为1200万像素主摄，支持4K视频录制。" +
                         "2023年12月15日正式发布，预计2024年1月1日开始销售。";
        
        try {
            // 测试语义分块
            List<TextChunk> chunks = hanLpService.semanticChunking(testText, 100, 20);
            
            // 测试分词
            List<TextChunk.SemanticUnit> segments = hanLpService.segment(testText);
            
            // 测试关键词提取
            List<String> keywords = hanLpService.extractKeywords(testText, 5);
            
            // 测试摘要提取
            String summary = hanLpService.extractSummary(testText, 2);
            
            // 构建测试结果
            return Result.success("测试完成", Map.of(
                "originalText", testText,
                "chunks", chunks,
                "segments", segments,
                "keywords", keywords,
                "summary", summary
            ));
            
        } catch (Exception e) {
            log.error("测试失败", e);
            return Result.error(500, "测试失败: " + e.getMessage());
        }
    }
} 