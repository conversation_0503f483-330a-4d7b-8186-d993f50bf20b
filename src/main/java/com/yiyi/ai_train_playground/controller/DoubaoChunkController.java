package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.service.DoubaoChunkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/doubao-chunk")
@RequiredArgsConstructor
public class DoubaoChunkController {
    
    private final DoubaoChunkService doubaoChunkService;
    
    @PostMapping("/chunk")
    public ResponseEntity<List<String>> chunkText(@RequestBody Map<String, Object> request) {
        try {
            String toBeChunkedString = (String) request.get("toBeChunkedString");
            Integer chunkSize = (Integer) request.get("chunkSize");
            Integer overlap = (Integer) request.get("overlap");
            
            // 参数验证
            if (toBeChunkedString == null || toBeChunkedString.trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }
            
            if (chunkSize == null || chunkSize <= 0) {
                return ResponseEntity.badRequest().build();
            }
            
            if (overlap == null || overlap < 0) {
                return ResponseEntity.badRequest().build();
            }
            
            log.info("收到文本分块请求，文本长度: {}, 分块大小: {}, 重叠字数: {}", 
                    toBeChunkedString.length(), chunkSize, overlap);
            
            List<String> result = doubaoChunkService.chunkText(toBeChunkedString, chunkSize, overlap);
            
            log.info("文本分块完成，返回 {} 个分块", result.size());
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("文本分块失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
} 