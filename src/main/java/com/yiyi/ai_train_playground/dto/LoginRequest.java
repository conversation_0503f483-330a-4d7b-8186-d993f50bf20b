package com.yiyi.ai_train_playground.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "LoginRequest", description = "用户登录请求")
public class LoginRequest {

    @Schema(description = "用户身份标识，可以是用户名、邮箱或手机号",
            example = "admin",
            required = true)
    private String identity;

    @Schema(description = "用户密码",
            example = "123456",
            required = true)
    private String password;

    @Schema(description = "是否记住登录状态，默认为false",
            example = "false",
            defaultValue = "false")
    private Boolean rememberMe = false;
}