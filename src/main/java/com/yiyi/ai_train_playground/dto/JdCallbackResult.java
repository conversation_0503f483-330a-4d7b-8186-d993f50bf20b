package com.yiyi.ai_train_playground.dto;

import lombok.Data;

/**
 * 京东回调结果DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
public class JdCallbackResult {
    
    /**
     * 处理是否成功
     */
    private boolean success;
    
    /**
     * 是否已授权
     */
    private boolean authorize;
    
    /**
     * 是否同步完成
     */
    private boolean syncComplete;
    
    /**
     * 错误信息（如果失败）
     */
    private String errorMessage;
    
    /**
     * Access Token（用于后续操作）
     */
    private String accessToken;

    /**
     * 京东用户是否已存在
     */
    private boolean isJdUserExist;

    /**
     * 用户ID（来自existingToken.userId）
     */
    private Long userId;

    /**
     * 京东访问令牌唯一标识
     */
    private String xid;

    /**
     * 创建成功结果
     */
    public static JdCallbackResult success(boolean authorize, boolean syncComplete, String accessToken) {
        JdCallbackResult result = new JdCallbackResult();
        result.setSuccess(true);
        result.setAuthorize(authorize);
        result.setSyncComplete(syncComplete);
        result.setAccessToken(accessToken);
        return result;
    }

    /**
     * 创建成功结果（包含用户存在状态）
     */
    public static JdCallbackResult success(boolean authorize, boolean syncComplete, String accessToken, boolean isJdUserExist) {
        JdCallbackResult result = new JdCallbackResult();
        result.setSuccess(true);
        result.setAuthorize(authorize);
        result.setSyncComplete(syncComplete);
        result.setAccessToken(accessToken);
        result.setJdUserExist(isJdUserExist);
        return result;
    }

    /**
     * 创建成功结果（包含用户存在状态和用户ID）
     */
    public static JdCallbackResult success(boolean authorize, boolean syncComplete, String accessToken, boolean isJdUserExist, Long userId) {
        JdCallbackResult result = new JdCallbackResult();
        result.setSuccess(true);
        result.setAuthorize(authorize);
        result.setSyncComplete(syncComplete);
        result.setAccessToken(accessToken);
        result.setJdUserExist(isJdUserExist);
        result.setUserId(userId);
        return result;
    }

    /**
     * 创建成功结果（包含用户存在状态、用户ID和xid）
     */
    public static JdCallbackResult success(boolean authorize, boolean syncComplete, String accessToken, boolean isJdUserExist, Long userId, String xid) {
        JdCallbackResult result = new JdCallbackResult();
        result.setSuccess(true);
        result.setAuthorize(authorize);
        result.setSyncComplete(syncComplete);
        result.setAccessToken(accessToken);
        result.setJdUserExist(isJdUserExist);
        result.setUserId(userId);
        result.setXid(xid);
        return result;
    }
    
    /**
     * 创建失败结果
     */
    public static JdCallbackResult failure(String errorMessage) {
        JdCallbackResult result = new JdCallbackResult();
        result.setSuccess(false);
        result.setAuthorize(false);
        result.setSyncComplete(false);
        result.setErrorMessage(errorMessage);
        return result;
    }
} 