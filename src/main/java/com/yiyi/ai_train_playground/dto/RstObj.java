package com.yiyi.ai_train_playground.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 图片处理结果对象
 * 用于存储图片URL和对应的识别文本
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RstObj {

    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * 识别文本（Markdown格式）
     */
    private String recoTextWithMD;

    /**
     * 处理顺序索引（用于保证结果顺序）
     */
    private Integer orderIndex;

    /**
     * 处理状态
     */
    private String status;

    /**
     * 错误信息（如果处理失败）
     */
    private String errorMessage;

    /**
     * 处理开始时间
     */
    private Long startTime;

    /**
     * 处理结束时间
     */
    private Long endTime;

    /**
     * 构造函数（兼容原有接口）
     *
     * @param imageUrl 图片URL
     * @param recoTextWithMD 识别文本
     */
    public RstObj(String imageUrl, String recoTextWithMD) {
        this.imageUrl = imageUrl;
        this.recoTextWithMD = recoTextWithMD;
        this.status = "SUCCESS";
        this.startTime = System.currentTimeMillis();
        this.endTime = System.currentTimeMillis();
    }

    /**
     * 构造函数（带顺序索引）
     *
     * @param orderIndex 顺序索引
     * @param imageUrl 图片URL
     */
    public RstObj(Integer orderIndex, String imageUrl) {
        this.orderIndex = orderIndex;
        this.imageUrl = imageUrl;
        this.startTime = System.currentTimeMillis();
        this.status = "PROCESSING";
    }

    /**
     * 标记处理成功
     *
     * @param recoTextWithMD 识别结果
     */
    public void markSuccess(String recoTextWithMD) {
        this.recoTextWithMD = recoTextWithMD;
        this.status = "SUCCESS";
        this.endTime = System.currentTimeMillis();
    }

    /**
     * 标记处理失败
     *
     * @param errorMessage 错误信息
     */
    public void markFailure(String errorMessage) {
        this.errorMessage = errorMessage;
        this.status = "FAILED";
        this.endTime = System.currentTimeMillis();
    }

    /**
     * 判断是否处理成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(status);
    }

    /**
     * 判断是否有有效的识别结果
     *
     * @return 是否有有效结果
     */
    public boolean hasValidResult() {
        return isSuccess() && recoTextWithMD != null && !recoTextWithMD.trim().isEmpty();
    }
}
