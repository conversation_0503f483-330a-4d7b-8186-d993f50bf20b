package com.yiyi.ai_train_playground.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 店铺信息与Token响应DTO
 * 用于API响应的数据传输对象
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "ShopWithTokenResponse", description = "店铺信息与Token响应")
public class ShopWithTokenResponse {

    @Schema(description = "团队ID", example = "1001")
    private String teamId;

    @Schema(description = "店铺ID", example = "12345")
    private String shopId;

    @Schema(description = "店铺类型：0-京东，1-淘宝，2-抖店", example = "0")
    private String shopType;

    @Schema(description = "店铺类型名称", example = "京东")
    private String shopTypeName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否授权", example = "true")
    private Boolean isAuthorize;

    @Schema(description = "授权状态名称", example = "已授权")
    private String authorizeStatusName;

    @Schema(description = "是否同步完成：0-未完成，1-同步中，2-已完成", example = "2")
    private Boolean isSyncComplete;

    @Schema(description = "同步状态名称", example = "已完成")
    private String syncStatusName;

    @Schema(description = "访问令牌")
    private String accessToken;

    @Schema(description = "令牌过期时间")
    private LocalDateTime expiresTime;

    @Schema(description = "令牌是否过期", example = "false")
    private Boolean isTokenExpired;

    @Schema(description = "店铺名称", example = "测试店铺")
    private String shopName;

    @Schema(description = "店铺过期时间")
    private LocalDateTime deadLine;

    @Schema(description = "是否过期", example = "false")
    private Boolean isExpired;

    @Schema(description = "过期状态名称", example = "有效")
    private String expiredStatusName;

    /**
     * 获取店铺类型名称
     */
    public String getShopTypeName() {
        if (shopType == null) {
            return "未知";
        }
        switch (shopType) {
            case "0":
                return "京东";
            case "1":
                return "淘宝";
            case "2":
                return "抖店";
            default:
                return "未知";
        }
    }

    /**
     * 获取授权状态名称
     */
    public String getAuthorizeStatusName() {
        if (isAuthorize == null) {
            return "未知";
        }
        return isAuthorize ? "已授权" : "未授权";
    }

    /**
     * 获取同步状态名称
     */
    public String getSyncStatusName() {
        if (isSyncComplete == null) {
            return "未知";
        }
        return isSyncComplete ? "已完成" : "未完成";
    }

    /**
     * 判断令牌是否过期
     */
    public Boolean getIsTokenExpired() {
        if (expiresTime == null) {
            return null;
        }
        return LocalDateTime.now().isAfter(expiresTime);
    }

    /**
     * 获取过期状态名称
     */
    public String getExpiredStatusName() {
        if (isExpired == null) {
            return "未知";
        }
        return isExpired ? "已过期" : "有效";
    }

    /**
     * 从实体对象创建响应DTO
     */
    public static ShopWithTokenResponse fromEntity(com.yiyi.ai_train_playground.entity.TrainTeamShopWithToken entity) {
        if (entity == null) {
            return null;
        }

        return ShopWithTokenResponse.builder()
                .teamId(entity.getTeamId())
                .shopId(entity.getShopId())
                .shopType(entity.getShopType())
                .createTime(entity.getCreateTime())
                .updateTime(entity.getUpdateTime())
                .isAuthorize(entity.isAuthorize())
                .isSyncComplete(entity.isSyncComplete())
                .accessToken(entity.getAccessToken())
                .expiresTime(entity.getExpiresTime())
                .shopName(entity.getShopName())
                .deadLine(entity.getDeadLine())
                .isExpired(entity.getIsExpired())
                .build();
    }
}
