package com.yiyi.ai_train_playground.dto;

import lombok.Data;
import java.util.List;

/**
 * 剧本创建请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-03
 */
@Data
public class ScriptCreateRequest {
    
    /**
     * 剧本名称
     */
    private String name;
    
    /**
     * 生成类型代码
     */
    private Integer generationTypeCode;
    
    /**
     * 分组ID
     */
    private Long groupId;
    
    /**
     * 意图ID
     */
    private Long intentId;
    
    /**
     * 评估计划ID
     */
    private Long evaluationPlanId;
    
    /**
     * 买家需求
     */
    private String buyerRequirement;
    
    /**
     * 订单是否备注
     */
    private Integer orderIsRemarked;
    
    /**
     * 订单优先级
     */
    private Integer orderPriority;
    
    /**
     * 订单备注
     */
    private String orderRemark;
    
    /**
     * 模拟工具
     */
    private String simulationTool;
    
    /**
     * 商品列表
     */
    private List<ProductCreateDTO> productList;
    
    /**
     * 关联图片列表
     */
    private List<RelatedImageCreateDTO> relateImgs;
    
    /**
     * 流程节点列表
     */
    private List<FlowNodeCreateDTO> flowNodes;
    
    /**
     * 商品创建DTO
     */
    @Data
    public static class ProductCreateDTO {
        /**
         * 外部商品ID
         */
        private String externalProductId;
        
        /**
         * 外部商品名称
         */
        private String externalProductName;
        
        /**
         * 外部商品链接
         */
        private String externalProductLink;
        
        /**
         * 外部商品图片
         */
        private String externalProductImage;
    }
    
    /**
     * 关联图片创建DTO
     */
    @Data
    public static class RelatedImageCreateDTO {
        /**
         * 媒体类型
         */
        private Integer mediaType;
        
        /**
         * 上传类型
         */
        private Integer uploadType;
        
        /**
         * 识别文本
         */
        private String recognizedText;
        
        /**
         * URL地址
         */
        private String url;
    }
    
    /**
     * 流程节点创建DTO
     */
    @Data
    public static class FlowNodeCreateDTO {
        /**
         * 节点名称
         */
        private String nodeName;
        
        /**
         * 节点买家需求
         */
        private String nodeBuyerRequirement;
    }
}
