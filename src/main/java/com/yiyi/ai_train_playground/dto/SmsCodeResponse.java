package com.yiyi.ai_train_playground.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 短信验证码发送响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
@Schema(description = "短信验证码发送响应")
public class SmsCodeResponse {
    
    @Schema(description = "验证密钥", example = "abcd1234")
    private String verificationKey;
    
    public SmsCodeResponse(String verificationKey) {
        this.verificationKey = verificationKey;
    }
} 