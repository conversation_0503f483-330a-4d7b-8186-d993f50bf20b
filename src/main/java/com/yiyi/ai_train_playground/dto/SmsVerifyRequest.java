package com.yiyi.ai_train_playground.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 短信验证码验证请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
@Schema(description = "短信验证码验证请求")
public class SmsVerifyRequest {
    
    @NotBlank(message = "验证密钥不能为空")
    @Schema(description = "验证密钥", example = "abcd1234", required = true)
    private String verificationKey;
    
    @NotBlank(message = "验证码不能为空")
    @Pattern(regexp = "^\\d{4}$", message = "验证码必须是4位数字")
    @Schema(description = "验证码", example = "1234", required = true)
    private String verificationCode;

    @NotBlank(message = "xid不能为空")
    @Schema(description = "京东访问令牌唯一标识", example = "jd_xid_123456", required = true)
    private String xid;
} 