package com.yiyi.ai_train_playground.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import java.util.*;

@Data
@JsonInclude(JsonInclude.Include.ALWAYS)
public class ScriptGroupDTO {
    private Long id;
    private Long parentId;
    private String groupTitle;
    private Integer isOfficial;
    private List<ScriptGroupDTO> subGroups;

    // 用于将实体类转换为DTO的静态方法
    public static ScriptGroupDTO fromEntity(com.yiyi.ai_train_playground.entity.ScriptGroup entity) {
        return fromEntity(entity, new HashSet<>());
    }
    
    // 内部方法，用于处理循环引用
    private static ScriptGroupDTO fromEntity(com.yiyi.ai_train_playground.entity.ScriptGroup entity, Set<Long> processedIds) {
        if (entity == null || (entity.getId() != null && !processedIds.add(entity.getId()))) {
            return null;
        }
        
        ScriptGroupDTO dto = new ScriptGroupDTO();
        dto.setId(entity.getId());
        dto.setParentId(entity.getParentId());
        dto.setGroupTitle(entity.getGroupTitle());
        dto.setIsOfficial(Boolean.TRUE.equals(entity.getIsOfficial()) ? 1 : 0);
        
        if (entity.getSubGroups() != null && !entity.getSubGroups().isEmpty()) {
            List<ScriptGroupDTO> subGroups = new ArrayList<>();
            for (com.yiyi.ai_train_playground.entity.ScriptGroup subGroup : entity.getSubGroups()) {
                ScriptGroupDTO subDto = fromEntity(subGroup, processedIds);
                if (subDto != null) {
                    subGroups.add(subDto);
                }
            }
            if (!subGroups.isEmpty()) {
                dto.setSubGroups(subGroups);
            }
        }
        
        return dto;
    }
} 