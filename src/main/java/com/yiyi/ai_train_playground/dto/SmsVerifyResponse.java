package com.yiyi.ai_train_playground.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 短信验证码验证响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-22
 */
@Data
@Schema(description = "短信验证码验证响应")
public class SmsVerifyResponse {
    
    @Schema(description = "用户ID", example = "1")
    private Long userId;
    
    @Schema(description = "用户名", example = "user_12345678")
    private String username;
    
    @Schema(description = "显示名称", example = "手机用户")
    private String displayName;
    
    @Schema(description = "手机号", example = "13800138000")
    private String mobile;
    
    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;
    
    @Schema(description = "重定向URL", example = "http://training.yiyiai.com:8081/pg-guide/auth/1")
    private String redirectUrl;
    
    /**
     * 从SmsLoginResponse创建SmsVerifyResponse
     */
    public static SmsVerifyResponse fromSmsLoginResponse(SmsLoginResponse smsLoginResponse, String redirectUrl) {
        SmsVerifyResponse response = new SmsVerifyResponse();
        response.setUserId(smsLoginResponse.getUserId());
        response.setUsername(smsLoginResponse.getUsername());
        response.setDisplayName(smsLoginResponse.getDisplayName());
        response.setMobile(smsLoginResponse.getMobile());
        response.setToken(smsLoginResponse.getToken());
        response.setRedirectUrl(redirectUrl);
        return response;
    }
}
