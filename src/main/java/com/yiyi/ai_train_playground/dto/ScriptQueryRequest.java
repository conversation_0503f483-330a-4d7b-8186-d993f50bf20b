package com.yiyi.ai_train_playground.dto;

import lombok.Data;

/**
 * 剧本查询请求参数
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-02
 */
@Data
public class ScriptQueryRequest {
    /**
     * 剧本昵称
     */
    private String name;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 生成方式代码：0-商品知识训练；1-实战能力进阶；2-自定义内容
     */
    private Integer generationTypeCode;
    
    /**
     * 开始时间起始点
     */
    private String createTimeStart;
    
    /**
     * 开始时间结束点
     */
    private String createTimeEnd;
    
    /**
     * 更新时间起始点
     */
    private String updateTimeStart;
    
    /**
     * 更新时间结束点
     */
    private String updateTimeEnd;
    
    /**
     * 分组编号
     */
    private Long groupId;
    
    /**
     * 页号
     */
    private Integer page = 1;
    
    /**
     * 每页大小
     */
    private Integer pageSize = 10;
}
