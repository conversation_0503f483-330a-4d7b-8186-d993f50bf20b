package com.yiyi.ai_train_playground.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 京东商品列表查询请求参数
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
@Schema(description = "京东商品列表查询请求参数")
public class JdPrdListRequest {

    /**
     * 商品标题（模糊查询）
     */
    @Schema(description = "商品标题（模糊查询）", example = "小米手环")
    private String jdPrdTitle;

    /**
     * 页号，从1开始
     */
    @Schema(description = "页号，从1开始", example = "1")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;
}
