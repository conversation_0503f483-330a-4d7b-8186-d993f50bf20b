package com.yiyi.ai_train_playground.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.*;

@Data
@JsonInclude(JsonInclude.Include.ALWAYS)
public class EvaluationGroupDTO {
    private Long id;
    private Long teamId;
    private String groupTitle;
    private Long parentId;
    private Integer isOfficial;
    private Integer sortOrder;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String creator;
    private String updater;
    private Long version;
    private String fullPath;
    private List<EvaluationGroupDTO> subGroups = new ArrayList<>();

    // 用于将实体类转换为DTO的静态方法
    public static EvaluationGroupDTO fromEntity(com.yiyi.ai_train_playground.entity.EvaluationGroup entity) {
        return fromEntity(entity, new HashSet<>());
    }

    // 内部方法，用于处理循环引用 - 改为 public 以便测试访问
    public static EvaluationGroupDTO fromEntity(com.yiyi.ai_train_playground.entity.EvaluationGroup entity, Set<Long> processedIds) {
        if (entity == null || (entity.getId() != null && !processedIds.add(entity.getId()))) {
            return null;
        }

        EvaluationGroupDTO dto = new EvaluationGroupDTO();
        dto.setId(entity.getId());
        dto.setTeamId(entity.getTeamId());
        dto.setGroupTitle(entity.getGroupTitle());
        dto.setParentId(entity.getParentId());
        dto.setIsOfficial(Boolean.TRUE.equals(entity.getIsOfficial()) ? 1 : 0);
        dto.setSortOrder(entity.getSortOrder());
        dto.setCreateTime(entity.getCreateTime());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setCreator(entity.getCreator());
        dto.setUpdater(entity.getUpdater());
        dto.setVersion(entity.getVersion());
        dto.setFullPath(entity.getFullPath());

        // 初始化 subGroups 为空列表而不是 null
        dto.setSubGroups(new ArrayList<>());

        if (entity.getSubGroups() != null && !entity.getSubGroups().isEmpty()) {
            List<EvaluationGroupDTO> subGroups = new ArrayList<>();
            for (com.yiyi.ai_train_playground.entity.EvaluationGroup subGroup : entity.getSubGroups()) {
                EvaluationGroupDTO subDto = fromEntity(subGroup, processedIds);
                if (subDto != null) {
                    subGroups.add(subDto);
                }
            }
            dto.setSubGroups(subGroups);
        }

        return dto;
    }
}
