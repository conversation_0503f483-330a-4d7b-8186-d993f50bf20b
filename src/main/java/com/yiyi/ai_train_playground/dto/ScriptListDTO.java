package com.yiyi.ai_train_playground.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 剧本列表响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-02
 */
@Data
public class ScriptListDTO {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 剧本名称
     */
    private String name;
    
    /**
     * 买家需求及背景
     */
    private String buyerRequirement;
    
    /**
     * 意图ID
     */
    private Long intentId;
    
    /**
     * 意图名称
     */
    private String intentName;
    
    /**
     * 生成方式代码：0-商品知识训练；1-实战能力进阶；2-自定义内容
     */
    private Integer generationTypeCode;
    
    /**
     * 生成方式名称
     */
    private String generationType;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
