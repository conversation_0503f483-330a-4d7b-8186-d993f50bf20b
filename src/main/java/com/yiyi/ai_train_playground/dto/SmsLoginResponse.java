package com.yiyi.ai_train_playground.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 短信验证登录响应DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
@Schema(description = "短信验证登录响应")
public class SmsLoginResponse {
    
    @Schema(description = "用户ID", example = "1001")
    private Long userId;
    
    @Schema(description = "用户名", example = "user_13800138000")
    private String username;
    
    @Schema(description = "用户显示名称", example = "手机用户")
    private String displayName;
    
    @Schema(description = "手机号", example = "13800138000")
    private String mobile;
    
    @Schema(description = "JWT访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;
} 