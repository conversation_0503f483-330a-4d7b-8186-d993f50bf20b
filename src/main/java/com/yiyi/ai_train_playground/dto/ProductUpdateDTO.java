package com.yiyi.ai_train_playground.dto;

import lombok.Data;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;

@Data
public class ProductUpdateDTO {
    @NotNull(message = "商品ID不能为空")
    private Long id;
    
    @NotBlank(message = "外部商品ID不能为空")
    private String externalProductId;
    
    @NotBlank(message = "商品名称不能为空")
    private String externalProductName;
    
    @NotBlank(message = "商品链接不能为空")
    private String externalProductLink;
    
    private String externalProductImage;
    private String externalProductDetail;
    private String externalProductDetailCleaned;
    private String status;
    private String category;
    private String tags;
    private String platform;
    private String shopId;
    private String shopName;
} 