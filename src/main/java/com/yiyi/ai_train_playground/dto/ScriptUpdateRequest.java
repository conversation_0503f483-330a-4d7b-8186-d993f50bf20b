package com.yiyi.ai_train_playground.dto;

import lombok.Data;
import java.util.List;

/**
 * 剧本更新请求DTO
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-03
 */
@Data
public class ScriptUpdateRequest {
    
    /**
     * 剧本ID
     */
    private Long id;
    
    /**
     * 剧本名称
     */
    private String name;
    
    /**
     * 生成类型代码
     */
    private Integer generationTypeCode;
    
    /**
     * 分组ID
     */
    private Long groupId;
    
    /**
     * 意图ID
     */
    private Long intentId;
    
    /**
     * 评估计划ID
     */
    private Long evaluationPlanId;
    
    /**
     * 买家需求
     */
    private String buyerRequirement;
    
    /**
     * 订单是否备注
     */
    private Integer orderIsRemarked;
    
    /**
     * 订单优先级
     */
    private Integer orderPriority;
    
    /**
     * 订单备注
     */
    private String orderRemark;

    /**
     * 重试买家需求次数
     */
    private Integer retryBuyerRequirementCounts;

    /**
     * 重试流程节点次数
     */
    private Integer retryFlowNodeCounts;

    /**
     * 模拟工具
     */
    private String simulationTool;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 是否正式
     */
    private Boolean isOfficial;

    /**
     * 商品列表
     */
    private List<ProductUpdateDTO> productList;
    
    /**
     * 关联图片列表
     */
    private List<RelatedImageUpdateDTO> relateImgs;
    
    /**
     * 流程节点列表
     */
    private List<FlowNodeUpdateDTO> flowNodes;
    
    /**
     * 商品更新DTO
     */
    @Data
    public static class ProductUpdateDTO {
        /**
         * 商品ID
         */
        private Long id;
        
        /**
         * 外部商品ID
         */
        private String externalProductId;
        
        /**
         * 外部商品名称
         */
        private String externalProductName;
        
        /**
         * 外部商品链接
         */
        private String externalProductLink;
        
        /**
         * 外部商品图片
         */
        private String externalProductImage;
    }
    
    /**
     * 关联图片更新DTO
     */
    @Data
    public static class RelatedImageUpdateDTO {
        /**
         * 图片ID
         */
        private Long id;
        
        /**
         * 媒体类型
         */
        private Integer mediaType;
        
        /**
         * 上传类型
         */
        private Integer uploadType;
        
        /**
         * 识别文本
         */
        private String recognizedText;
        
        /**
         * URL地址
         */
        private String url;
    }
    
    /**
     * 流程节点更新DTO
     */
    @Data
    public static class FlowNodeUpdateDTO {
        /**
         * 节点ID
         */
        private Long id;
        
        /**
         * 节点名称
         */
        private String nodeName;
        
        /**
         * 节点买家需求
         */
        private String nodeBuyerRequirement;
    }
}
