package com.yiyi.ai_train_playground.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "LoginResponse", description = "用户登录响应")
public class LoginResponse {

    @Schema(description = "用户ID", example = "1")
    private Long userId;

    @Schema(description = "用户名", example = "admin")
    private String username;

    @Schema(description = "邮箱地址", example = "<EMAIL>")
    private String email;

    @Schema(description = "手机号码", example = "13800138000")
    private String mobile;

    @Schema(description = "用户显示名称", example = "管理员")
    private String displayName;

    @Schema(description = "团队ID", example = "1")
    private Long teamId;

    @Schema(description = "JWT访问令牌",
            example = "eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ")
    private String token;
}