package com.yiyi.ai_train_playground.security;

import com.yiyi.ai_train_playground.util.JwtUtil;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.JwtException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Slf4j
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtUtil jwtUtil;
    private final UserDetailsService userDetailsService;

    public JwtAuthenticationFilter(JwtUtil jwtUtil, UserDetailsService userDetailsService) {
        this.jwtUtil = jwtUtil;
        this.userDetailsService = userDetailsService;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                  HttpServletResponse response,
                                  FilterChain filterChain) throws ServletException, IOException {
        
        String token = extractTokenFromRequest(request);
        
        if (token != null) {
            try {
                // 验证token并获取用户信息
                if (jwtUtil.validateToken(token)) {
                    String username = jwtUtil.getUsernameFromToken(token);
                    Long userId = jwtUtil.getUserIdFromToken(token);
                    Long teamId = jwtUtil.getTeamIdFromToken(token);

                    UserDetails userDetails = userDetailsService.loadUserByUsername(username);

                    // 使用自定义的JwtAuthenticationToken来存储userId和teamId
                    JwtAuthenticationToken authentication =
                        new JwtAuthenticationToken(userDetails, null, userDetails.getAuthorities(), userId, teamId);
                    authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                    SecurityContextHolder.getContext().setAuthentication(authentication);
                    log.debug("JWT认证成功，用户: {}, userId: {}, teamId: {}", username, userId, teamId);
                }
            } catch (ExpiredJwtException e) {
                log.warn("JWT token已过期: {}", e.getMessage());
                // 不设置认证信息，让Spring Security的认证入口点处理
            } catch (JwtException e) {
                log.warn("JWT token无效: {}", e.getMessage());
                // 不设置认证信息，让Spring Security的认证入口点处理
            } catch (Exception e) {
                log.error("JWT认证过程中发生异常: {}", e.getMessage());
                // 不设置认证信息，让Spring Security的认证入口点处理
            }
        }
        
        filterChain.doFilter(request, response);
    }

    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
} 