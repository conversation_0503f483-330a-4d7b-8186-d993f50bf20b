package com.yiyi.ai_train_playground.security;

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * 自定义JWT认证Token，包含teamId信息
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
public class JwtAuthenticationToken extends UsernamePasswordAuthenticationToken {

    private final Long userId;
    private final Long teamId;

    public JwtAuthenticationToken(Object principal, Object credentials, 
                                 Collection<? extends GrantedAuthority> authorities,
                                 Long userId, Long teamId) {
        super(principal, credentials, authorities);
        this.userId = userId;
        this.teamId = teamId;
    }

    public Long getUserId() {
        return userId;
    }

    public Long getTeamId() {
        return teamId;
    }
}
