package com.yiyi.ai_train_playground.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.common.Result;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * JWT认证入口点
 * 当用户访问受保护的资源但未提供有效的JWT时，返回401状态码
 */
@Slf4j
@Component
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void commence(HttpServletRequest request, 
                        HttpServletResponse response,
                        AuthenticationException authException) throws IOException, ServletException {
        
        log.warn("JWT认证失败，请求路径: {}, 错误信息: {}", request.getRequestURI(), authException.getMessage());
        
        // 设置响应状态码为401
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");
        
        // 构建错误响应
        Result<Object> result = Result.error(401, "认证失败，请重新登录");
        
        // 写入响应
        response.getWriter().write(objectMapper.writeValueAsString(result));
    }
} 