package com.yiyi.ai_train_playground.security;

import com.yiyi.ai_train_playground.entity.User;
import com.yiyi.ai_train_playground.mapper.UserMapper;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Service
public class CustomUserDetailsService implements UserDetailsService {

    private final UserMapper userMapper;

    public CustomUserDetailsService(UserMapper userMapper) {
        this.userMapper = userMapper;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 支持使用用户名、邮箱或手机号登录
        User user = userMapper.findByIdentity(username);
        if (user == null) {
            throw new UsernameNotFoundException("用户不存在: " + username);
        }

        return new CustomUserDetails(
            user.getId(),
            user.getUsername(),
            user.getPasswordHash(),
            user.getTeamId(),
            !user.getIsLocked(),  // enabled
            user.getFailedAttempts() < 5  // accountNonLocked
        );
    }
} 