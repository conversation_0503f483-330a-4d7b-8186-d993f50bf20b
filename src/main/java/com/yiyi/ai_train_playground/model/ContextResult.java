package com.yiyi.ai_train_playground.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 豆包上下文创建结果实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContextResult {
    
    /**
     * 上下文ID
     */
    private String id;
    
    /**
     * 模型名称
     */
    private String model;
    
    /**
     * 模式
     */
    private String mode;
    
    /**
     * 生存时间（秒）
     */
    private Integer ttl;
    
    /**
     * 截断策略
     */
    private String truncationStrategy;
    
    /**
     * 使用情况
     */
    private Usage usage;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Usage {
        /**
         * 提示词token数
         */
        private Integer promptTokens;
        
        /**
         * 完成token数
         */
        private Integer completionTokens;
        
        /**
         * 总token数
         */
        private Integer totalTokens;
        
        /**
         * 提示词token详情
         */
        private PromptTokensDetails promptTokensDetails;
        
        /**
         * 完成token详情
         */
        private Object completionTokensDetails;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PromptTokensDetails {
        /**
         * 缓存的token数
         */
        private Integer cachedTokens;
    }
}
