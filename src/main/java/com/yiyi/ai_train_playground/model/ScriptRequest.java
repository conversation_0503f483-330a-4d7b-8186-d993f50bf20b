package com.yiyi.ai_train_playground.model;

import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class ScriptRequest {
    // 分组ID
    private Integer groupId;
    // 剧本名称
    private String name;
    // 买家需求
    private String buyerRequirement;
    // 意图
    private Map<String, Object> intents;
    // 关联图片
    private List<Map<String, Object>> relateImgs;
    // 流程节点
    private List<Map<String, Object>> flowNodes;
    // 产品列表
    private List<Map<String, Object>> productList;
    // 团队ID
    private Long teamId;
} 