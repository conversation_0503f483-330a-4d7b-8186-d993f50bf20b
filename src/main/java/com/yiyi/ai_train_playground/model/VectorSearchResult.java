package com.yiyi.ai_train_playground.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 向量搜索结果模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VectorSearchResult {
    
    /**
     * 搜索结果列表
     */
    private List<VectorData> results;
    
    /**
     * 搜索耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 总数量
     */
    private Integer total;
} 