package com.yiyi.ai_train_playground.model;

import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class ScriptChatMessage {
    // 消息类型: INIT-初始化, MESSAGE-用户消息
    private String type;
    // 消息内容
    private String content;
    // 角色: USER-用户, ASSISTANT-助手
    private String role;
    // 系统消息
    private String systemMessage;
    // 分组ID
    private Integer groupId;
    // 剧本名称
    private String name;
    // 买家需求
    private String buyerRequirement;
    // 意图
    private Map<String, Object> intents;
    // 关联图片
    private List<Map<String, Object>> relateImgs;
    // 流程节点
    private List<Map<String, Object>> flowNodes;
    // 会话ID
    private String sessionId;
    // 团队ID
    private Long teamId;
    // 产品列表
    private List<Map<String, Object>> productList;
} 