package com.yiyi.ai_train_playground.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 向量搜索请求模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VectorSearchRequest {
    
    /**
     * 集合名称
     */
    private String collectionName;
    
    /**
     * 查询向量
     */
    private List<Float> vector;
    
    /**
     * 返回结果数量
     */
    @Builder.Default
    private Integer limit = 10;
    
    /**
     * 过滤条件
     */
    private Map<String, Object> filter;
    
    /**
     * 最小相似度分数
     */
    private Float scoreThreshold;
    
    /**
     * 是否返回向量
     */
    @Builder.Default
    private Boolean withVector = false;
    
    /**
     * 是否返回载荷
     */
    @Builder.Default
    private Boolean withPayload = true;
} 