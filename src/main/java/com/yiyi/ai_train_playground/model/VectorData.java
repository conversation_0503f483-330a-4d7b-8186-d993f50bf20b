package com.yiyi.ai_train_playground.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 向量数据模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VectorData {
    
    /**
     * 向量ID
     */
//    private Long id;
    private String id;

    /**
     * 向量值
     */
    private List<Float> vector;
    
    /**
     * 元数据
     */
    private Map<String, Object> payload;
    
    /**
     * 分数（搜索结果时使用）
     */
    private Float score;
} 