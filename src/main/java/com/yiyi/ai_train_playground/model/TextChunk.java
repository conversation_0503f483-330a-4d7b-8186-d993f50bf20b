package com.yiyi.ai_train_playground.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 文本分块结果模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TextChunk {
    
    /**
     * 分块内容
     */
    private String content;
    
    /**
     * 分块序号（从0开始）
     */
    private int index;
    
    /**
     * 分块起始位置（在原文中的字符位置）
     */
    private int startPosition;
    
    /**
     * 分块结束位置（在原文中的字符位置）
     */
    private int endPosition;
    
    /**
     * 分块字符长度
     */
    private int length;
    
    /**
     * 与前一个分块的重叠字符数
     */
    private int overlapLength;
    
    /**
     * 分块中包含的语义单元列表（词语及其词性）
     */
    private List<SemanticUnit> semanticUnits;
    
    /**
     * 语义单元模型
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SemanticUnit {
        /**
         * 词语
         */
        private String word;
        
        /**
         * 词性
         */
        private String nature;
        
        /**
         * 在分块中的起始位置
         */
        private int startPos;
        
        /**
         * 在分块中的结束位置
         */
        private int endPos;
    }
} 