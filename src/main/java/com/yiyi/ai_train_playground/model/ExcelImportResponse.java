package com.yiyi.ai_train_playground.model;

import lombok.Data;
import java.util.List;

@Data
public class ExcelImportResponse {
    private Integer totalCount;
    private Integer successCount;
    private Integer failCount;
    private List<ProductItem> successItems;
    private List<FailedItem> failedItems;
    private String failedExcelUrl;
    
    @Data
    public static class ProductItem {
        private String externalProductId;
        private String externalProductName;
        private String externalProductLink;
        private String externalProductImage;
    }
    
    @Data
    public static class FailedItem {
        private Integer row;
        private String externalProductId;
        private String reason;
    }
} 