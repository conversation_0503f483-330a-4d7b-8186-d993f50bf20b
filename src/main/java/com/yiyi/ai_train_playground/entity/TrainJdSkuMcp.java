package com.yiyi.ai_train_playground.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 京东SKU多种类属性表实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
public class TrainJdSkuMcp {
    /**
     * 京东商品功能表主键ID
     */
    private Long id;
    
    /**
     * 京东skuID
     */
    private Long skuId;
    
    /**
     * 团队ID，0代表系统
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
    
    // 业务字段
    
    /**
     * 属性ID 通过接口 jingdong.category.read.findAttrsByCategoryIdUnlimitCate 按照4级类目获取
     */
    private String attrId;
    
    /**
     * 属性值ID数组 jingdong.category.read.findValuesByAttrIdUnlimit 获取，输入方式类型请填写文字
     */
    private String attrValues;
    
    /**
     * 属性值扩展字段
     */
    private String expands;
    
    /**
     * 属性值单位
     */
    private String units;
} 