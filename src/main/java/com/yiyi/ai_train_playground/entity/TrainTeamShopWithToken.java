package com.yiyi.ai_train_playground.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 店铺信息与京东Token组合实体
 * 包含TrainTeamShops和TrainJdAccessToken的所有字段
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-20
 */
@Data
public class TrainTeamShopWithToken {
    
    // 团队ID（来自TrainTeamShops实体）
    private String teamId;
    
    // 店铺ID（来自TrainTeamShops实体）
    private String shopId;
    
    // 店铺类型（来自TrainTeamShops实体）
    private String shopType;
    
    // 创建时间（来自TrainTeamShops实体）
    private LocalDateTime createTime;
    
    // 更新时间（来自TrainTeamShops实体）
    private LocalDateTime updateTime;
    
    // 是否授权（来自TrainTeamShops实体）
    private boolean isAuthorize;
    
    // 是否同步完成（来自TrainTeamShops实体）
    private boolean isSyncComplete;
    
    // 访问令牌（来自TrainJdAccessToken实体）
    private String accessToken;
    
    // 过期时间（来自TrainJdAccessToken实体）
    private LocalDateTime expiresTime;

    //店铺名称
    private String shopName;

    //店铺过期时间
    private LocalDateTime deadLine;
}