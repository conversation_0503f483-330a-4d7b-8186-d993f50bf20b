package com.yiyi.ai_train_playground.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 剧本商品表实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-02
 */
@Data
public class TrainScriptProducts {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 剧本ID
     */
    private Long scriptId;

    /**
     * 训练商品ID（外键关联train_product表）
     */
    private Long trainProductId;

    /**
     * 外部商品ID
     */
    private String externalProductId;
    
    /**
     * 外部商品名称
     */
    private String externalProductName;
    
    /**
     * 外部商品链接
     */
    private String externalProductLink;
    
    /**
     * 外部商品图片
     */
    private String externalProductImage;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号
     */
    private Long version;
}
