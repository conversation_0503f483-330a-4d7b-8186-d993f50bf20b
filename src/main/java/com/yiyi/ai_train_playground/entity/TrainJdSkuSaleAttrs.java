package com.yiyi.ai_train_playground.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 京东SKU销售属性表实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
public class TrainJdSkuSaleAttrs {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 关联train_jd_sku.sku_id
     */
    private Long skuId;
    
    /**
     * 团队ID，0代表系统
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（乐观锁）
     */
    private Long version;
    
    // 业务字段
    
    /**
     * 属性ID
     */
    private String attrId;
    
    /**
     * 属性值ID
     */
    private String attrValues;
    
    /**
     * 属性值别名
     */
    private String attrValuesAlias;
    
    /**
     * 销售属性维度：1-颜色，2-尺码
     */
    private Integer attrIndex;
    
    /**
     * 销售属性排序值
     */
    private String attrValuesSeqNo;
} 