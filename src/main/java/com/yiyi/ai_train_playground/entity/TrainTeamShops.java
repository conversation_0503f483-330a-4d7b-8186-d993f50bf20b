package com.yiyi.ai_train_playground.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 团队店铺表实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
public class TrainTeamShops {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 团队ID,0代表系统
     */
    private Long teamId;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺类型,0:京东，1：淘宝，2：抖店
     */
    private Integer shopType;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
    
    /**
     * 是否和远程店铺同步
     */
    private Boolean isAuthorize;
    
    /**
     * 商品同步是否已完成,0:未完成，1：同步中，2：已完成
     */
    private Integer isSyncComplete;
} 