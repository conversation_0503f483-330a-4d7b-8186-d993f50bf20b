package com.yiyi.ai_train_playground.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 京东访问令牌表实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
public class TrainJdAccessToken {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 团队ID，0代表系统
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
    
    /**
     * 访问令牌
     */
    private String accessToken;
    
    /**
     * 过期时间
     */
    private LocalDateTime expiresTime;
    
    /**
     * 刷新令牌
     */
    private String refreshToken;
    
    /**
     * 权限范围
     */
    private String scope;
    
    /**
     * 唯一标识
     */
    private String xid;
    
    /**
     * 店铺ID
     */
    private Long shopId;
    
    /**
     * 店铺是否完成授权
     */
    private Integer isAuthorize;
    
    /**
     * 商品同步是否已完成,0:未完成，1：同步中，2：已完成
     */
    private Integer isSyncComplete;
} 