package com.yiyi.ai_train_playground.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 京东商品图片信息表实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Data
public class TrainJdProdImages {
    
    /**
     * 主键，自增
     */
    private Long id;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 京东商品ID
     */
    private Long jdProdId;
    
    /**
     * 京东图片url
     */
    private String imgUrl;
    
    /**
     * 图片内容LLM识别
     */
    private String imgRecoText;
    
    /**
     * 商品同步是否已完成: 0-未完成, 1-同步中, 2-已完成
     */
    private Integer syncStatus;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
}
