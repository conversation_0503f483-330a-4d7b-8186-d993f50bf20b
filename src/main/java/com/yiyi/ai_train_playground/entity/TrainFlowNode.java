package com.yiyi.ai_train_playground.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 流程节点表实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-02
 */
@Data
public class TrainFlowNode {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 剧本ID（外键）
     */
    private Long scriptId;
    
    /**
     * 节点名称（如"产品参数"）
     */
    private String nodeName;
    
    /**
     * 买家要求文本
     */
    private String nodeBuyerRequirement;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号
     */
    private Long version;
}
