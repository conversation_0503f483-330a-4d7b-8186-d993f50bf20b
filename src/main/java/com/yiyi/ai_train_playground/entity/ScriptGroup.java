package com.yiyi.ai_train_playground.entity;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
@JsonIdentityInfo(
    generator = ObjectIdGenerators.PropertyGenerator.class,
    property = "id"
)
@JsonIgnoreProperties({"handler"})
public class ScriptGroup {
    private Long id;
    private Long teamId;
    private String groupTitle;
    private Long parentId;
    private Boolean isOfficial;
    private Integer sortOrder;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String creator;
    private String updater;
    private Integer version;
    private String fullPath;
    
    // Additional fields for tree structure
    private List<ScriptGroup> subGroups;
} 