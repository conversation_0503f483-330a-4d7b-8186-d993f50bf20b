package com.yiyi.ai_train_playground.entity;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 京东商品表实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Data
public class TrainJdProducts {
    /**
     * 京东商品表主键ID
     */
    private Long id;
    
    /**
     * 团队ID，0代表系统
     */
    private Long teamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
    
    // 业务字段 - 商品基本信息
    
    /**
     * 品牌ID
     */
    private Long brandId;
    
    /**
     * 品牌名称
     */
    private String brandName;
    
    /**
     * 一级分类ID
     */
    private Long categoryId;
    
    /**
     * 二级分类ID
     */
    private Long categorySecId;
    
    /**
     * 颜色类型
     */
    private Integer colType;
    
    /**
     * 成本价格
     */
    private BigDecimal costPrice;
    
    /**
     * 京东商品创建时间
     */
    private LocalDateTime created;
    
    /**
     * 商品高度(cm)
     */
    private BigDecimal height;
    
    /**
     * 京东价格
     */
    private BigDecimal jdPrice;
    
    /**
     * 商品长度(cm)
     */
    private BigDecimal length;
    
    /**
     * 商品logo图片URL
     */
    private String logo;
    
    /**
     * 市场价格
     */
    private BigDecimal marketPrice;
    
    /**
     * 京东商品修改时间
     */
    private LocalDateTime modified;
    
    /**
     * 下线时间
     */
    private LocalDateTime offlineTime;

    /**
     * 上线时间
     */
    private LocalDateTime onlineTime;

    /**
     * 京东平台店铺ID
     */
    private Long shopId;
    
    /**
     * SPU ID
     */
    private Long spuId;
    
    /**
     * 库存数量
     */
    private Integer stockNum;
    
    /**
     * 模板ID
     */
    private Long templateId;
    
    /**
     * 商品标题
     */
    private String title;
    
    /**
     * 商品ID
     */
    private Long wareId;
    
    /**
     * 商品状态：1-上架，0-下架
     */
    private Integer wareStatus;
    
    /**
     * 商品重量(kg)
     */
    private BigDecimal weight;
    
    /**
     * 商品宽度(cm)
     */
    private BigDecimal width;
    
    /**
     * 包装信息
     */
    private String wrap;
    
    /**
     * 商品所在仓库位置
     */
    private Integer wareLocation;
    
    /**
     * 商品介绍
     */
    private String introduction;
    
    /**
     * 手机端描述
     */
    private String mobileDesc;
    
    /**
     * APP端适配HTML
     */
    private String fitCaseHtmlApp;
    
    /**
     * 商品功能特性JSON数据
     */
    private String features;
    
    /**
     * 商品多类目属性JSON数据
     */
    private String multiCateProps;

    /**
     * 京东商品详情易蚁展示
     */
    private String jdProdDtl;

    /**
     * 京东商品图像原始链接群
     */
    private String jdProdImgList;

    /**
     * desc的md5签名
     */
    private String descSignature;

    /**
     * 京东商品的同步状态
     */
    private Integer syncStatus;
}