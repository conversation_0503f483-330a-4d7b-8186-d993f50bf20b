package com.yiyi.ai_train_playground.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 关联图片信息表实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-02
 */
@Data
public class TrainRelatedImage {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 团队ID,0代表系统
     */
    private Long teamId;
    
    /**
     * 脚本ID，外键
     */
    private Long scriptId;
    
    /**
     * 图片中识别的文字
     */
    private String recognizedText;
    
    /**
     * 上传类型:1-本地上传 2-链接上传
     */
    private Integer uploadType;
    
    /**
     * 媒体类型:1-图片 2-视频
     */
    private Integer mediaType;
    
    /**
     * 图片在阿里云的URL或者用户自己上传的URL
     */
    private String url;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
}
