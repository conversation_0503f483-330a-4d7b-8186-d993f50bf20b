package com.yiyi.ai_train_playground.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 剧本实体类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-02
 */
@Data
public class TrainScript {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 剧本名称（如"催促派单:性能对标保时捷..."）
     */
    private String name;
    
    /**
     * 生成方式：0-商品知识训练；1-实战能力进阶；2-自定义内容
     */
    private Integer generationType;
    
    /**
     * 分组ID
     */
    private Long groupId;
    
    /**
     * 进线意图ID（外键）
     */
    private Long intentId;
    
    /**
     * 评价方案ID（外键）
     */
    private Long evaluationId;
    
    /**
     * 买家需求及背景
     */
    private String buyerRequirement;
    
    /**
     * 订单备注的优先级:0:灰色；1：红色；2：橙，3：绿；4：蓝，5：紫
     */
    private Integer orderPriority;

    /**
     * 订单是否备注
     */
    private Integer orderIsRemarked;

    /**
     * 订单备注
     */
    private String orderRemark;
    
    /**
     * 买家需求及背景生新生成次数
     */
    private Integer retryBuyerRequirementCounts;
    
    /**
     * 买家进线模拟咨询流程重新生成次数
     */
    private Integer retryFlowNodeCounts;

    /**
     * 模拟工具名称或标识
     */
    private String simulationTool;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号
     */
    private Long version;
    
    /**
     * 是否官方预设：0-否（用户自定义）；1-是（系统预设）
     */
    private Boolean isOfficial;
}
