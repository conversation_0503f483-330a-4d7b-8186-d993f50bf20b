package com.yiyi.ai_train_playground.vo;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 京东商品图片详情VO类
 * 用于承载商品表和图片表的 join 查询结果
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-20
 */
@Data
public class JdProductImageDetailVO {
    
    /**
     * 图片表主键ID
     */
    private Long id;
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 京东商品ID (来自图片表的 jd_prod_id)
     */
    private Long jdProdId;
    
    /**
     * 商品wareId (来自商品表的 ware_id)
     */
    private Long wareId;
    
    /**
     * 京东图片url
     */
    private String imgUrl;
    
    /**
     * 图片内容LLM识别
     */
    private String imgRecoText;
    
    /**
     * 商品同步是否已完成: 0-未完成, 1-同步中, 2-已完成
     */
    private Integer syncStatus;
    
    /**
     * 商品详情 (来自商品表的 jd_prod_dtl)
     */
    private String jdProdDtl;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 更新人
     */
    private String updater;
    
    /**
     * 版本号（用于乐观锁）
     */
    private Long version;
}
