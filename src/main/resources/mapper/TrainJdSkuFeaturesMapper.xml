<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.TrainJdSkuFeaturesMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.TrainJdSkuFeatures">
        <id column="id" property="id"/>
        <result column="team_id" property="teamId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="version" property="version"/>
        <result column="sku_id" property="skuId"/>
        <result column="fid" property="fid"/>
        <result column="fname" property="fname"/>
        <result column="fvalue" property="fvalue"/>
        <result column="order_sort" property="orderSort"/>
    </resultMap>

    <!-- 批量插入SKU特色服务 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO train_jd_sku_features (
            team_id, sku_id, fid, fname, fvalue, order_sort,
            creator, updater, create_time, update_time, version
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.teamId}, #{item.skuId}, #{item.fid}, #{item.fname}, 
            #{item.fvalue}, #{item.orderSort}, #{item.creator}, #{item.updater}, 
            NOW(), NOW(), 0
        )
        </foreach>
    </insert>

    <!-- 根据skuId物理删除特色服务 -->
    <delete id="deleteBySkuId" parameterType="long">
        DELETE FROM train_jd_sku_features
        WHERE sku_id = #{skuId}
    </delete>

    <!-- 根据skuId查询特色服务列表 -->
    <select id="findBySkuId" parameterType="long" resultMap="BaseResultMap">
        SELECT *
        FROM train_jd_sku_features
        WHERE sku_id = #{skuId}
        ORDER BY order_sort, id
    </select>

</mapper> 