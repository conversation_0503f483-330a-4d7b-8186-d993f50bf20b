<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.TrainFlowNodeMapper">

    <!-- 批量插入流程节点 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO train_flow_node (
            team_id, script_id, node_name, node_buyer_requirement, creator, updater, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.teamId}, #{item.scriptId}, #{item.nodeName}, #{item.nodeBuyerRequirement}, #{item.creator}, #{item.updater}, NOW(), NOW())
        </foreach>
    </insert>

    <!-- 单个插入流程节点 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.TrainFlowNode" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_flow_node (
            team_id, script_id, node_name, node_buyer_requirement, creator, updater, create_time, update_time
        ) VALUES (
            #{teamId}, #{scriptId}, #{nodeName}, #{nodeBuyerRequirement}, #{creator}, #{updater}, NOW(), NOW()
        )
    </insert>

    <!-- 根据剧本ID删除流程节点 -->
    <delete id="deleteByScriptId" parameterType="java.lang.Long">
        DELETE FROM train_flow_node WHERE script_id = #{scriptId}
    </delete>

    <!-- 根据剧本ID和团队ID删除流程节点 -->
    <delete id="deleteByScriptIdAndTeamId">
        DELETE FROM train_flow_node
        WHERE script_id = #{scriptId} AND team_id = #{teamId}
    </delete>

    <!-- 根据剧本ID查询流程节点 -->
    <select id="selectByScriptId" parameterType="java.lang.Long" resultType="com.yiyi.ai_train_playground.entity.TrainFlowNode">
        SELECT * FROM train_flow_node WHERE script_id = #{scriptId} ORDER BY id
    </select>

</mapper>
