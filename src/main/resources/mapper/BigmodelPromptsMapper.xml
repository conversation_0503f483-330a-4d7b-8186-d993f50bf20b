<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.BigmodelPromptsMapper">

    <!-- 根据关键词查询系统提示词 -->
    <select id="findSysPromptsByKeyword" resultType="string">
        SELECT sysPrompt 
        FROM bigmodel_prompts 
        WHERE keyword = #{keyword}
        AND sysPrompt IS NOT NULL 
        AND sysPrompt != ''
    </select>

    <!-- 根据关键词查询用户提示词 -->
    <select id="findUsrPromptsByKeyword" resultType="string">
        SELECT usrPrompt 
        FROM bigmodel_prompts 
        WHERE keyword = #{keyword}
        AND usrPrompt IS NOT NULL 
        AND usrPrompt != ''
    </select>

    <!-- 根据关键词查询系统和用户提示词 -->
    <select id="findBothPromptsByKeyword" resultType="string">
        SELECT 
            CASE 
                WHEN sysPrompt IS NOT NULL AND sysPrompt != '' THEN sysPrompt
                ELSE NULL 
            END as prompt
        FROM bigmodel_prompts 
        WHERE keyword = #{keyword}
        AND sysPrompt IS NOT NULL 
        AND sysPrompt != ''
        
        UNION ALL
        
        SELECT 
            CASE 
                WHEN usrPrompt IS NOT NULL AND usrPrompt != '' THEN usrPrompt
                ELSE NULL 
            END as prompt
        FROM bigmodel_prompts 
        WHERE keyword = #{keyword}
        AND usrPrompt IS NOT NULL 
        AND usrPrompt != ''
    </select>

    <!-- 插入提示词记录 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.BigmodelPrompts">
        INSERT INTO bigmodel_prompts 
        (keyword, sysPrompt, usrPrompt, createTime, updateTime, creator, updater, version)
        VALUES 
        (#{keyword}, #{sysPrompt}, #{usrPrompt}, #{createTime}, #{updateTime}, #{creator}, #{updater}, #{version})
    </insert>

</mapper> 