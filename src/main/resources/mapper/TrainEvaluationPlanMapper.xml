<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.TrainEvaluationPlanMapper">

    <!-- 评价方案简短信息结果映射 -->
    <resultMap id="EvaluationPlanShortResultMap" type="com.yiyi.ai_train_playground.dto.EvaluationPlanShortDTO">
        <id column="id" property="id"/>
        <result column="team_id" property="teamId"/>
        <result column="group_id" property="groupId"/>
        <result column="eva_name" property="evaName"/>
        <result column="eva_group_name" property="evaGroupName"/>
    </resultMap>

    <!-- 查询评价方案简短列表 -->
    <select id="selectShortList" resultMap="EvaluationPlanShortResultMap">
        SELECT 
            tep.id,
            tep.team_id,
            tep.group_id,
            tep.name AS eva_name,
            teg.group_title AS eva_group_name
        FROM train_evaluation_plan tep
        LEFT JOIN train_evaluation_group teg ON tep.group_id = teg.id
        WHERE tep.team_id = #{teamId}
        ORDER BY tep.create_time DESC
    </select>

</mapper>
