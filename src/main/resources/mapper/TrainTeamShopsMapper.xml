<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.TrainTeamShopsMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.TrainTeamShops">
        <id column="id" property="id"/>
        <result column="team_id" property="teamId"/>
        <result column="shop_id" property="shopId"/>
        <result column="shop_type" property="shopType"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="version" property="version"/>
        <result column="is_authorize" property="isAuthorize"/>
        <result column="is_sync_complete" property="isSyncComplete"/>
    </resultMap>

    <!-- 店铺和京东token信息的结果映射 -->
    <resultMap id="ShopAndTokenResultMap" type="com.yiyi.ai_train_playground.entity.TrainTeamShopWithToken">
        <result column="team_id" property="teamId"/>
        <result column="shop_id" property="shopId"/>
        <result column="shop_type" property="shopType"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_authorize" property="isAuthorize"/>
        <result column="is_sync_complete" property="isSyncComplete"/>
        <result column="access_token" property="accessToken"/>
        <result column="expires_time" property="expiresTime"/>
        <result column="shop_name" property="shopName"/>
        <result column="dead_line" property="deadLine"/>
        <result column="is_expired" property="isExpired"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, team_id, shop_id, shop_type, create_time, update_time, 
        creator, updater, version, is_authorize, is_sync_complete
    </sql>

    <!-- 根据店铺ID查询店铺信息及对应的京东token信息 -->
    <select id="selectShopAndTokenByShopId" resultMap="ShopAndTokenResultMap" parameterType="string">
        SELECT
            ts.team_id,
            ts.shop_id,
            ts.shop_type,
            ts.create_time,
            ts.update_time,
            ts.is_authorize,
            ts.is_sync_complete,
            ts.dead_line,
            jd.access_token,
            jd.expires_time,
            ts.shop_name,
            CASE
                WHEN jd.expires_time IS NULL THEN 0
                WHEN jd.expires_time &lt; NOW() THEN 1
                ELSE 0
            END AS is_expired
        FROM
            train_team_shops ts
                LEFT JOIN
            train_jd_accesstoken jd
            ON
                ts.team_id = jd.team_id
        WHERE
            ts.shop_id = #{shopId}
    </select>

    <select id="selectShopAndTokenByShopIdlist" resultMap="ShopAndTokenResultMap" parameterType="string">
        SELECT
            ts.team_id,
            ts.shop_id,
            ts.shop_type,
            ts.create_time,
            ts.update_time,
            ts.is_authorize,
            ts.is_sync_complete,
            ts.dead_line,
            jd.access_token,
            jd.expires_time,
            ts.shop_name,
            CASE
                WHEN jd.expires_time IS NULL THEN 0
                WHEN jd.expires_time &lt; NOW() THEN 1
                ELSE 0
            END AS is_expired
        FROM
            train_team_shops ts
                LEFT JOIN
            train_jd_accesstoken jd
            ON
                ts.team_id = jd.team_id
        WHERE
            ts.team_id = #{teamId}
    </select>

    <!-- 根据团队ID查询未过期的店铺列表信息及对应的京东token信息 -->
    <select id="selectValidShopAndTokenByTeamId" resultMap="ShopAndTokenResultMap" parameterType="string">
        SELECT
            ts.team_id,
            ts.shop_id,
            ts.shop_type,
            ts.create_time,
            ts.update_time,
            ts.is_authorize,
            ts.is_sync_complete,
            ts.dead_line,
            jd.access_token,
            jd.expires_time,
            ts.shop_name,
            CASE
                WHEN jd.expires_time IS NULL THEN 0
                WHEN jd.expires_time &lt; NOW() THEN 1
                ELSE 0
            END AS is_expired
        FROM
            train_team_shops ts
                LEFT JOIN
            train_jd_accesstoken jd
            ON
                ts.team_id = jd.team_id
        WHERE
            ts.team_id = #{teamId}
            AND (jd.expires_time IS NULL OR jd.expires_time &gt;= NOW())
    </select>

    <!-- 根据店铺ID更新团队店铺信息（完整实体更新） -->
    <update id="updateEntityByShopId" parameterType="com.yiyi.ai_train_playground.entity.TrainTeamShops">
        UPDATE train_team_shops
        <set>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="shopType != null">shop_type = #{shopType},</if>
            <if test="isAuthorize != null">is_authorize = #{isAuthorize},</if>
            <if test="isSyncComplete != null">is_sync_complete = #{isSyncComplete},</if>
            <if test="updater != null and updater != ''">updater = #{updater},</if>
            update_time = NOW(),
            version = version + 1
        </set>
        WHERE shop_id = #{shopId}
    </update>


</mapper>
