<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.EvaluationGroupMapper">
    
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.EvaluationGroup">
        <id column="id" property="id"/>
        <result column="team_id" property="teamId"/>
        <result column="group_title" property="groupTitle"/>
        <result column="parent_id" property="parentId"/>
        <result column="is_official" property="isOfficial"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="version" property="version"/>
        <result column="full_path" property="fullPath"/>
    </resultMap>

    <!-- 查询评价分组列表 -->
    <select id="selectList" resultMap="BaseResultMap">
        WITH RECURSIVE group_tree AS (
            SELECT 
                id, group_title, parent_id, sort_order, create_time, update_time, 
                is_official, team_id, creator, updater, version,
                group_title as full_path
            FROM train_evaluation_group
            WHERE 1=1
            <if test="groupTitle != null and groupTitle != ''">
                AND group_title LIKE CONCAT('%', #{groupTitle}, '%')
            </if>
            <if test="teamId != null">
                AND (team_id = #{teamId} OR team_id = 0)
            </if>
            
            UNION ALL
            
            SELECT 
                g.id, g.group_title, g.parent_id, g.sort_order, g.create_time, g.update_time,
                g.is_official, g.team_id, g.creator, g.updater, g.version,
                CONCAT(gt.full_path, ' > ', g.group_title)
            FROM train_evaluation_group g
            JOIN group_tree gt ON g.parent_id = gt.id
        )
        SELECT * FROM group_tree
        ORDER BY sort_order, create_time DESC
    </select>

    <!-- 插入新分组 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.EvaluationGroup">
        INSERT INTO train_evaluation_group (
            group_title,
            parent_id,
            team_id,
            is_official,
            create_time,
            update_time
        ) VALUES (
            #{groupTitle},
            #{parentId},
            #{teamId},
            #{isOfficial},
            NOW(),
            NOW()
        )
    </insert>

    <!-- 更新分组 -->
    <update id="update" parameterType="com.yiyi.ai_train_playground.entity.EvaluationGroup">
        UPDATE train_evaluation_group
        SET 
            group_title = #{groupTitle},
            parent_id = #{parentId},
            update_time = NOW()
        WHERE id = #{id} AND team_id = #{teamId}
    </update>

    <!-- 删除分组 -->
    <delete id="deleteByIds">
        DELETE FROM train_evaluation_group 
        WHERE id IN 
        <foreach collection="ids.split(',')" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND team_id = #{teamId}
    </delete>

    <!-- 获取默认分组 -->
    <select id="getDefaultGroup" resultType="com.yiyi.ai_train_playground.entity.EvaluationGroup">
        SELECT * FROM train_evaluation_group WHERE id = -2
    </select>

    <!-- 根据团队ID获取分组 -->
    <select id="getByTeamId" resultType="com.yiyi.ai_train_playground.entity.EvaluationGroup">
        SELECT * FROM train_evaluation_group 
        WHERE team_id = #{teamId}
        ORDER BY create_time DESC
    </select>

    <!-- 获取官方分组 -->
    <select id="getOfficialGroups" resultType="com.yiyi.ai_train_playground.entity.EvaluationGroup">
        SELECT * FROM train_evaluation_group 
        WHERE is_official = true
        ORDER BY create_time DESC
    </select>

    <!-- 查找所有分组（树形结构） -->
    <select id="findAllWithTree" resultType="com.yiyi.ai_train_playground.entity.EvaluationGroup">
        SELECT * FROM train_evaluation_group
        WHERE 1=1
        <if test="groupTitle != null and groupTitle != ''">
            AND group_title LIKE CONCAT('%', #{groupTitle}, '%')
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 查找官方分组 -->
    <select id="findOfficialGroups" resultType="com.yiyi.ai_train_playground.entity.EvaluationGroup">
        SELECT * FROM train_evaluation_group
        WHERE is_official = true
        ORDER BY create_time DESC
    </select>

    <!-- 查找用户分组 -->
    <select id="findUserGroups" resultType="com.yiyi.ai_train_playground.entity.EvaluationGroup">
        SELECT * FROM train_evaluation_group
        WHERE is_official = false
        ORDER BY create_time DESC
    </select>

    <!-- 根据ID查找分组 -->
    <select id="findById" resultType="com.yiyi.ai_train_playground.entity.EvaluationGroup">
        SELECT * FROM train_evaluation_group WHERE id = #{id}
    </select>
</mapper>
