<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.TrainRelatedImageMapper">

    <!-- 批量插入相关图片 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO train_related_image (
            team_id, script_id, recognized_text, upload_type, media_type, url, creator, updater, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.teamId}, #{item.scriptId}, #{item.recognizedText}, #{item.uploadType}, #{item.mediaType}, #{item.url}, #{item.creator}, #{item.updater}, NOW(), NOW())
        </foreach>
    </insert>

    <!-- 单个插入相关图片 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.TrainRelatedImage" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_related_image (
            team_id, script_id, recognized_text, upload_type, media_type, url, creator, updater, create_time, update_time
        ) VALUES (
            #{teamId}, #{scriptId}, #{recognizedText}, #{uploadType}, #{mediaType}, #{url}, #{creator}, #{updater}, NOW(), NOW()
        )
    </insert>

    <!-- 根据剧本ID删除相关图片 -->
    <delete id="deleteByScriptId" parameterType="java.lang.Long">
        DELETE FROM train_related_image WHERE script_id = #{scriptId}
    </delete>

    <!-- 根据剧本ID和团队ID删除相关图片 -->
    <delete id="deleteByScriptIdAndTeamId">
        DELETE FROM train_related_image
        WHERE script_id = #{scriptId} AND team_id = #{teamId}
    </delete>

    <!-- 根据剧本ID查询相关图片 -->
    <select id="selectByScriptId" parameterType="java.lang.Long" resultType="com.yiyi.ai_train_playground.entity.TrainRelatedImage">
        SELECT * FROM train_related_image WHERE script_id = #{scriptId}
    </select>

</mapper>
