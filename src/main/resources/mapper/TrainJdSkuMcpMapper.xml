<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.TrainJdSkuMcpMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.TrainJdSkuMcp">
        <id column="id" property="id"/>
        <result column="team_id" property="teamId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="version" property="version"/>
        <result column="sku_id" property="skuId"/>
        <result column="cate_prop" property="cateProp"/>
        <result column="cate_prop_des" property="catePropDes"/>
        <result column="category_id" property="categoryId"/>
    </resultMap>

    <!-- 批量插入SKU多类目属性 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO train_jd_sku_mcp (
            team_id, sku_id, cate_prop, cate_prop_des, category_id,
            creator, updater, create_time, update_time, version
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.teamId}, #{item.skuId}, #{item.cateProp}, #{item.catePropDes}, 
            #{item.categoryId}, #{item.creator}, #{item.updater}, 
            NOW(), NOW(), 0
        )
        </foreach>
    </insert>

    <!-- 根据skuId物理删除多类目属性 -->
    <delete id="deleteBySkuId" parameterType="long">
        DELETE FROM train_jd_sku_mcp
        WHERE sku_id = #{skuId}
    </delete>

    <!-- 根据skuId查询多类目属性列表 -->
    <select id="findBySkuId" parameterType="long" resultMap="BaseResultMap">
        SELECT *
        FROM train_jd_sku_mcp
        WHERE sku_id = #{skuId}
        ORDER BY id
    </select>

</mapper> 