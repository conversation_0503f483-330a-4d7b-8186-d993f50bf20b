<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.TrainJdSkuPropsMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.TrainJdSkuProps">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="prop_id" property="propId" jdbcType="VARCHAR"/>
        <result column="prop_name" property="propName" jdbcType="VARCHAR"/>
        <result column="prop_value" property="propValue" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="updater" property="updater" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入SKU属性 -->
    <insert id="batchInsert">
        INSERT INTO train_jd_sku_props (
            sku_id,
            prop_id,
            prop_name,
            prop_value,
            creator,
            updater,
            create_time,
            modify_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.skuId},
                #{item.propId},
                #{item.propName},
                #{item.propValue},
                #{item.creator},
                #{item.updater},
                NOW(),
                NOW()
            )
        </foreach>
    </insert>

    <!-- 根据SKU ID删除相关属性 -->
    <delete id="deleteBySkuId">
        DELETE FROM train_jd_sku_props WHERE sku_id = #{skuId}
    </delete>

    <!-- 根据SKU ID查询属性列表 -->
    <select id="findBySkuId" resultMap="BaseResultMap">
        SELECT 
            id,
            sku_id,
            prop_id,
            prop_name,
            prop_value,
            creator,
            updater,
            create_time,
            modify_time
        FROM train_jd_sku_props
        WHERE sku_id = #{skuId}
        ORDER BY id
    </select>

</mapper> 