<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.TrainScriptProductsMapper">

    <!-- 批量插入剧本产品关联 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO train_script_products (
            script_id, train_product_id, creator, updater, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.scriptId}, #{item.trainProductId}, #{item.creator}, #{item.updater}, NOW(), NOW())
        </foreach>
    </insert>

    <!-- 单个插入剧本产品关联 -->
    <insert id="insert" parameterType="com.yiyi.ai_train_playground.entity.TrainScriptProducts" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO train_script_products (
            script_id, train_product_id, creator, updater, create_time, update_time
        ) VALUES (
            #{scriptId}, #{trainProductId}, #{creator}, #{updater}, NOW(), NOW()
        )
    </insert>

    <!-- 根据剧本ID删除关联 -->
    <delete id="deleteByScriptId" parameterType="java.lang.Long">
        DELETE FROM train_script_products WHERE script_id = #{scriptId}
    </delete>

    <!-- 根据剧本ID查询关联的产品 -->
    <select id="selectByScriptId" parameterType="java.lang.Long" resultType="com.yiyi.ai_train_playground.entity.TrainScriptProducts">
        SELECT * FROM train_script_products WHERE script_id = #{scriptId}
    </select>

    <!-- 根据剧本ID和团队ID查询关联的商品ID列表 -->
    <select id="selectTrainProductIdsByScriptIdAndTeamId" resultType="java.lang.Long">
        SELECT train_product_id
        FROM train_script_products
        WHERE script_id = #{scriptId} AND team_id = #{teamId}
    </select>

</mapper>
