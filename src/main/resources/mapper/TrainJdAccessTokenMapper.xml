<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yiyi.ai_train_playground.mapper.TrainJdAccessTokenMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.yiyi.ai_train_playground.entity.TrainJdAccessToken">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="team_id" property="teamId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="version" property="version"/>
        <result column="access_token" property="accessToken"/>
        <result column="expires_time" property="expiresTime"/>
        <result column="refresh_token" property="refreshToken"/>
        <result column="scope" property="scope"/>
        <result column="xid" property="xid"/>
        <result column="shop_id" property="shopId"/>
        <result column="is_authorize" property="isAuthorize"/>
        <result column="is_sync_complete" property="isSyncComplete"/>
    </resultMap>

    <!-- 根据xid动态更新访问令牌（忽略空字段） -->
    <update id="updateJATByXid" parameterType="com.yiyi.ai_train_playground.entity.TrainJdAccessToken">
        UPDATE train_jd_accesstoken
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="teamId != null">
                team_id = #{teamId},
            </if>
            <if test="accessToken != null and accessToken != ''">
                access_token = #{accessToken},
            </if>
            <if test="expiresTime != null">
                expires_time = #{expiresTime},
            </if>
            <if test="refreshToken != null and refreshToken != ''">
                refresh_token = #{refreshToken},
            </if>
            <if test="scope != null and scope != ''">
                scope = #{scope},
            </if>
            <if test="shopId != null">
                shop_id = #{shopId},
            </if>
            <if test="isAuthorize != null">
                is_authorize = #{isAuthorize},
            </if>
            <if test="isSyncComplete != null">
                is_sync_complete = #{isSyncComplete},
            </if>
            <if test="updater != null and updater != ''">
                updater = #{updater},
            </if>
            update_time = NOW(),
            version = version + 1
        </set>
        WHERE xid = #{xid}
    </update>

</mapper>