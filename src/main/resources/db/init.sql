-- 创建数据库
CREATE DATABASE IF NOT EXISTS ai_train_playground DEFAULT CHARSET utf8mb4 COLLATE utf8mb4_general_ci;

USE ai_train_playground;

-- 创建剧本分组表
CREATE TABLE IF NOT EXISTS `train_script_group` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `team_id` bigint NOT NULL DEFAULT 0 COMMENT '团队ID',
  `group_title` varchar(64) NOT NULL COMMENT '分组标题（唯一）',
  `is_official` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否官方预设（0-否，1-是）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_group_title` (`group_title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='剧本分组表'; 