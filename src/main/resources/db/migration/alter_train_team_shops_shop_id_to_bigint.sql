-- 修改train_team_shops表的shop_id字段类型从varchar(64)改为bigint
-- 统一shopId为Long类型

-- 1. 删除包含shop_id的唯一索引
ALTER TABLE `train_team_shops` DROP INDEX `uk_team_shop_type`;

-- 2. 修改shop_id字段类型为bigint
ALTER TABLE `train_team_shops` 
MODIFY COLUMN `shop_id` bigint NOT NULL DEFAULT 1 COMMENT '店铺ID';

-- 3. 重新创建唯一索引
ALTER TABLE `train_team_shops` 
ADD UNIQUE KEY `uk_team_shop_type` (`team_id`, `shop_id`, `shop_type`);

-- 4. 更新注释
ALTER TABLE `train_team_shops` COMMENT = '团队店铺表 - shopId已统一为Long类型';
