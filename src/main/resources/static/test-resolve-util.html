<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ResolveUtil HTML解析工具测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        textarea {
            width: 100%;
            min-height: 200px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .json-output {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .demo-html {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .url-list {
            counter-reset: url-counter;
        }
        .url-item {
            counter-increment: url-counter;
            margin: 5px 0;
            padding: 5px;
            background-color: #f8f9fa;
            border-left: 3px solid #007bff;
        }
        .url-item::before {
            content: counter(url-counter) ". ";
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <h1>ResolveUtil HTML解析工具测试</h1>
    
    <div class="container">
        <h2>功能介绍</h2>
        <p>ResolveUtil是一个HTML解析工具类，主要用于从HTML内容中提取img标签的src属性。</p>
        
        <h3>主要功能：</h3>
        <ul>
            <li>提取所有img标签的src属性，按在HTML中出现的顺序排列</li>
            <li>自动处理相对URL（如//img.com/pic.jpg会转换为https://img.com/pic.jpg）</li>
            <li>支持CSS选择器指定特定区域的图片</li>
            <li>支持过滤空值和data:URL</li>
            <li>返回标准的List&lt;String&gt;格式，可直接转换为JSON</li>
        </ul>
    </div>

    <div class="container">
        <h2>HTML内容输入</h2>
        <div class="form-group">
            <label for="htmlContent">HTML内容:</label>
            <textarea id="htmlContent" placeholder="在这里粘贴HTML内容..."></textarea>
        </div>
        
        <div class="form-group">
            <label for="cssSelector">CSS选择器 (可选):</label>
            <input type="text" id="cssSelector" placeholder="例如: div.detail-content img" value="">
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="filterEmpty" checked> 过滤空的src属性
            </label>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="filterDataUrl" checked> 过滤data:开头的URL
            </label>
        </div>
        
        <div class="form-group">
            <button onclick="parseHtml()">解析HTML</button>
            <button onclick="loadDemo()">加载演示数据</button>
            <button onclick="loadJdDemo()">加载京东HTML演示</button>
            <button onclick="clearAll()">清空</button>
        </div>
    </div>

    <div class="container">
        <h2>解析结果</h2>
        <div id="parseResult" class="result">等待解析...</div>
    </div>

    <div class="container">
        <h2>JSON格式输出</h2>
        <div id="jsonOutput" class="json-output">解析结果将以JSON格式显示在这里...</div>
    </div>

    <div class="container">
        <h2>URL列表</h2>
        <div id="urlList" class="url-list">解析后的URL将按顺序显示在这里...</div>
    </div>

    <div class="container">
        <h2>演示数据</h2>
        <div class="demo-html">
            <h3>基本HTML示例:</h3>
            <pre>&lt;div&gt;
    &lt;img src="http://example.com/1.jpg" alt="图片1"/&gt;
    &lt;img src="//img11.360buyimg.com/devfe/jfs/test.jpg" alt="京东图片"/&gt;
    &lt;img src="https://example.com/2.png"/&gt;
    &lt;p&gt;一些文本&lt;/p&gt;
    &lt;img src="relative/path/3.gif"/&gt;
&lt;/div&gt;</pre>
        </div>
        
        <div class="demo-html">
            <h3>期望输出格式:</h3>
            <pre>[
  "http://example.com/1.jpg",
  "https://img11.360buyimg.com/devfe/jfs/test.jpg",
  "https://example.com/2.png",
  "relative/path/3.gif"
]</pre>
        </div>
    </div>

    <script>
        // 解析HTML
        function parseHtml() {
            const htmlContent = document.getElementById('htmlContent').value.trim();
            const cssSelector = document.getElementById('cssSelector').value.trim();
            const filterEmpty = document.getElementById('filterEmpty').checked;
            const filterDataUrl = document.getElementById('filterDataUrl').checked;
            
            if (!htmlContent) {
                showResult('请输入HTML内容', 'error');
                return;
            }
            
            // 模拟ResolveUtil.extractImageSources的功能
            try {
                const parser = new DOMParser();
                const doc = parser.parseFromString(htmlContent, 'text/html');
                
                let imgElements;
                if (cssSelector) {
                    imgElements = doc.querySelectorAll(cssSelector);
                } else {
                    imgElements = doc.querySelectorAll('img');
                }
                
                const imageSources = [];
                
                imgElements.forEach(img => {
                    let src = img.getAttribute('src');
                    
                    if (src) {
                        // 过滤空值
                        if (filterEmpty && !src.trim()) {
                            return;
                        }
                        
                        // 过滤data URL
                        if (filterDataUrl && src.startsWith('data:')) {
                            return;
                        }
                        
                        // 处理相对URL
                        if (src.startsWith('//')) {
                            src = 'https:' + src;
                        }
                        
                        imageSources.push(src);
                    }
                });
                
                displayResults(imageSources);
                
            } catch (error) {
                showResult('解析HTML时发生错误: ' + error.message, 'error');
            }
        }
        
        // 显示解析结果
        function displayResults(imageSources) {
            const resultText = `成功提取到 ${imageSources.length} 个图片URL：\n\n${imageSources.join('\n')}`;
            showResult(resultText, 'success');
            
            // 显示JSON格式
            const jsonOutput = JSON.stringify(imageSources, null, 2);
            document.getElementById('jsonOutput').textContent = jsonOutput;
            
            // 显示URL列表
            const urlListHtml = imageSources.map(url => `<div class="url-item">${url}</div>`).join('');
            document.getElementById('urlList').innerHTML = urlListHtml || '<div>没有找到图片URL</div>';
        }
        
        // 显示结果
        function showResult(message, type = '') {
            const element = document.getElementById('parseResult');
            element.textContent = message;
            element.className = 'result';
            
            if (type) {
                element.classList.add(type);
            }
        }
        
        // 加载基本演示数据
        function loadDemo() {
            const demoHtml = `<div>
    <img src="http://example.com/1.jpg" alt="图片1"/>
    <img src="//img11.360buyimg.com/devfe/jfs/t19249/266/555939662/10324/447efd03/5a965eb2Nf83fd68c.jpg" alt="京东图片"/>
    <img src="https://example.com/2.png"/>
    <p>一些文本</p>
    <img src="relative/path/3.gif"/>
    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" alt="base64图片"/>
    <img src="" alt="空src"/>
</div>`;
            
            document.getElementById('htmlContent').value = demoHtml;
            document.getElementById('cssSelector').value = '';
            showResult('已加载基本演示数据，点击"解析HTML"开始解析', 'info');
        }
        
        // 加载京东HTML演示数据
        function loadJdDemo() {
            const jdHtml = `<div data-tab="item">
    <div class="module-title">商品详情</div>
    <div class="goods-base">
        <div class="item">
            <div class="text">
                <a href="//list.jd.com/list.html">
                    <img src="//img1.360buyimg.com/brand-logo.jpg" alt="品牌"/>
                </a>
            </div>
        </div>
    </div>
    <div id="img-text-warp">
        <div id="img-text">
            <div class="wrap-scale">
                <div id="quality-life" class="quality-life">
                    <div class="q-logo">
                        <img src="//img11.360buyimg.com/devfe/jfs/t19249/266/555939662/10324/447efd03/5a965eb2Nf83fd68c.jpg" alt="品质生活"/>
                    </div>
                </div>
            </div>
            <div class="detail-content">
                <img src="//detail1.360buyimg.com/image1.png"/>
                <img src="//detail2.360buyimg.com/image2.jpg"/>
            </div>
        </div>
    </div>
</div>`;
            
            document.getElementById('htmlContent').value = jdHtml;
            document.getElementById('cssSelector').value = 'div.detail-content img';
            showResult('已加载京东HTML演示数据，CSS选择器已设置为提取详情区域图片', 'info');
        }
        
        // 清空所有内容
        function clearAll() {
            document.getElementById('htmlContent').value = '';
            document.getElementById('cssSelector').value = '';
            document.getElementById('parseResult').textContent = '等待解析...';
            document.getElementById('parseResult').className = 'result';
            document.getElementById('jsonOutput').textContent = '解析结果将以JSON格式显示在这里...';
            document.getElementById('urlList').innerHTML = '解析后的URL将按顺序显示在这里...';
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('ResolveUtil HTML解析工具测试页面已加载');
        });
    </script>
</body>
</html> 