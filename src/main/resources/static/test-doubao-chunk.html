<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>豆包智能分块服务测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        textarea, input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 200px;
            resize: vertical;
        }
        .params-row {
            display: flex;
            gap: 20px;
        }
        .params-row .form-group {
            flex: 1;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .chunk {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #e0e0e0;
        }
        .chunk-header {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            border-color: #dc3545;
        }
        .loading {
            text-align: center;
            color: #007bff;
        }
        .stats {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .stats h3 {
            margin-top: 0;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 豆包智能分块服务测试</h1>
        
        <form id="chunkForm">
            <div class="form-group">
                <label for="inputText">待分块文本:</label>
                <textarea id="inputText" placeholder="请输入需要分块的文本内容...">品牌：苹果，型号：iPhone 15 Pro，屏幕尺寸：6.1英寸超大屏幕大号版本，屏幕类型：Super Retina XDR OLED，分辨率：2556 x 1179像素，像素密度：460 PPI，刷新率：ProMotion自适应智能适应10-120Hz，处理器：A17 Pro芯片（6核CPU，6核GPU），内存：8GB LPDDR5 RAM，存储容量：128GB/256GB/512GB/1TB NVMe SSD，操作系统：iOS 17（可升级至未来版本），后置摄像头：48MP主摄（f/1.78光圈）+12MP超广角（f/2.2）+12MP长焦（f/2.8，3倍光学变焦），前置摄像头：12MP TrueDepth（f/1.9），视频录制：4K 60fps HDR，支持Dolby Vision，电池容量：3274mAh，充电方式：20W有线快充，15W MagSafe无线充电，7.5W Qi无线充电，防水防尘等级：IP68（最深6米，30分钟），重量：187克，尺寸：146.6毫米（高）×70.6毫米（宽）×7.8毫米（厚），颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异）</textarea>
            </div>
            
            <div class="params-row">
                <div class="form-group">
                    <label for="chunkSize">分块大小（字数）:</label>
                    <input type="number" id="chunkSize" value="200" min="50" max="1000">
                </div>
                <div class="form-group">
                    <label for="overlap">重叠字数:</label>
                    <input type="number" id="overlap" value="50" min="0" max="200">
                </div>
            </div>
            
            <button type="submit" id="submitBtn">开始智能分块</button>
        </form>
        
        <div id="result" style="display: none;"></div>
    </div>

    <script>
        document.getElementById('chunkForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const inputText = document.getElementById('inputText').value.trim();
            const chunkSize = parseInt(document.getElementById('chunkSize').value);
            const overlap = parseInt(document.getElementById('overlap').value);
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');
            
            if (!inputText) {
                alert('请输入待分块的文本');
                return;
            }
            
            if (chunkSize <= 0 || overlap < 0) {
                alert('请输入有效的分块参数');
                return;
            }
            
            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.textContent = '正在处理...';
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="loading">🤖 豆包AI正在智能分析文本，请稍候...</div>';
            
            try {
                const response = await fetch('/api/doubao-chunk/chunk', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        toBeChunkedString: inputText,
                        chunkSize: chunkSize,
                        overlap: overlap
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                displayResult(result, inputText, chunkSize, overlap);
                
            } catch (error) {
                console.error('请求失败:', error);
                resultDiv.innerHTML = `<div class="result error">
                    <h3>❌ 分块失败</h3>
                    <p>错误信息: ${error.message}</p>
                    <p>请检查网络连接和服务器状态</p>
                </div>`;
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '开始智能分块';
            }
        });
        
        function displayResult(chunks, originalText, chunkSize, overlap) {
            const resultDiv = document.getElementById('result');
            
            let html = `
                <div class="result">
                    <h3>✅ 智能分块完成</h3>
                    <div class="stats">
                        <h3>📊 分块统计</h3>
                        <p><strong>原始文本长度:</strong> ${originalText.length} 字</p>
                        <p><strong>设定分块大小:</strong> ${chunkSize} 字</p>
                        <p><strong>重叠字数:</strong> ${overlap} 字</p>
                        <p><strong>生成分块数量:</strong> ${chunks.length} 个</p>
                        <p><strong>平均分块长度:</strong> ${Math.round(chunks.reduce((sum, chunk) => sum + chunk.length, 0) / chunks.length)} 字</p>
                    </div>
                </div>
            `;
            
            chunks.forEach((chunk, index) => {
                html += `
                    <div class="chunk">
                        <div class="chunk-header">第 ${index + 1} 个分块 (${chunk.length} 字)</div>
                        <div>${chunk}</div>
                    </div>
                `;
            });
            
            resultDiv.innerHTML = html;
        }
        
        // 预设文本示例
        function loadExample(exampleText) {
            document.getElementById('inputText').value = exampleText;
        }
        
        // 添加示例按钮
        window.addEventListener('load', function() {
            const container = document.querySelector('.container');
            const exampleDiv = document.createElement('div');
            exampleDiv.innerHTML = `
                <div style="margin-bottom: 20px; text-align: center;">
                    <h3>📝 快速示例</h3>
                    <button type="button" onclick="loadExample('人工智能技术正在快速发展，机器学习、深度学习、自然语言处理等技术不断突破，为各行各业带来了革命性的变化。从智能助手到自动驾驶，从医疗诊断到金融风控，AI技术正在深刻改变我们的生活和工作方式。随着算力的提升和数据的积累，AI模型变得越来越强大，能够处理更复杂的任务。未来，AI技术将继续推动社会进步，创造更多价值。')" style="margin: 5px; padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer;">AI技术文本</button>
                    <button type="button" onclick="loadExample('电商平台商品推荐系统是现代电子商务的核心组成部分，通过分析用户行为数据、商品特征和市场趋势，为用户提供个性化的商品推荐。系统采用协同过滤、内容过滤和深度学习等多种算法，结合用户的浏览历史、购买记录、评价反馈等信息，构建用户画像和商品画像。推荐算法会实时更新，根据用户的最新行为调整推荐策略，提高推荐的准确性和相关性。同时，系统还会考虑商品的库存状态、价格变化、促销活动等因素，确保推荐的商品具有较高的转化率。')" style="margin: 5px; padding: 8px 16px; background: #ffc107; color: black; border: none; border-radius: 3px; cursor: pointer;">电商推荐文本</button>
                </div>
            `;
            container.insertBefore(exampleDiv, document.getElementById('chunkForm'));
        });
    </script>
</body>
</html> 