<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短信验证码API测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-section h2 {
            color: #555;
            margin-top: 0;
            margin-bottom: 15px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="tel"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .user-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .user-info h3 {
            margin-top: 0;
            color: #0056b3;
        }
        .user-info p {
            margin: 5px 0;
        }
        .token-display {
            word-break: break-all;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-size: 12px;
        }
        .countdown {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>短信验证码API测试</h1>
        
        <!-- 发送验证码测试 -->
        <div class="test-section">
            <h2>1. 发送短信验证码</h2>
            <div class="form-group">
                <label for="phone">手机号:</label>
                <input type="tel" id="phone" placeholder="请输入手机号，如：13800138000" value="13800138000">
            </div>
            <button onclick="sendVerificationCode()" id="sendBtn">发送验证码</button>
            <div id="sendResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- 验证码验证测试 -->
        <div class="test-section">
            <h2>2. 验证短信验证码并登录</h2>
            <div class="form-group">
                <label for="verificationKey">验证密钥:</label>
                <input type="text" id="verificationKey" placeholder="从发送验证码接口获取的verificationKey">
            </div>
            <div class="form-group">
                <label for="verificationCode">验证码:</label>
                <input type="text" id="verificationCode" placeholder="请输入4位验证码" maxlength="4">
            </div>
            <button onclick="verifyCode()" id="verifyBtn">验证并登录</button>
            <div id="verifyResult" class="result" style="display: none;"></div>
        </div>
        
        <!-- API文档 -->
        <div class="test-section">
            <h2>API文档</h2>
            <h3>1. 发送短信验证码</h3>
            <p><strong>URL:</strong> POST /api/sms/code</p>
            <p><strong>请求体:</strong></p>
            <pre><code>{
  "phone": "13800138000"
}</code></pre>
            <p><strong>响应:</strong></p>
            <pre><code>{
  "code": 1,
  "message": "发送成功",
  "data": {
    "verificationKey": "abcd1234..."
  }
}</code></pre>
            
            <h3>2. 验证短信验证码并登录</h3>
            <p><strong>URL:</strong> POST /api/sms/verify</p>
            <p><strong>请求体:</strong></p>
            <pre><code>{
  "verificationKey": "abcd1234...",
  "verificationCode": "1234"
}</code></pre>
            <p><strong>响应:</strong></p>
            <pre><code>{
  "code": 1,
  "message": "登录成功",
  "data": {
    "userId": 1001,
    "username": "user_abc12345",
    "displayName": "手机用户",
    "mobile": "13800138000",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}</code></pre>
        </div>
    </div>

    <script>
        let countdown = 0;
        let countdownTimer = null;
        
        function updateSendButton() {
            const sendBtn = document.getElementById('sendBtn');
            if (countdown > 0) {
                sendBtn.disabled = true;
                sendBtn.textContent = `发送验证码 (${countdown}s)`;
                sendBtn.classList.add('countdown');
            } else {
                sendBtn.disabled = false;
                sendBtn.textContent = '发送验证码';
                sendBtn.classList.remove('countdown');
            }
        }
        
        function startCountdown() {
            countdown = 60;
            updateSendButton();
            countdownTimer = setInterval(() => {
                countdown--;
                updateSendButton();
                if (countdown <= 0) {
                    clearInterval(countdownTimer);
                }
            }, 1000);
        }
        
        async function sendVerificationCode() {
            const phone = document.getElementById('phone').value;
            const resultDiv = document.getElementById('sendResult');
            
            if (!phone) {
                showResult(resultDiv, '请输入手机号', 'error');
                return;
            }
            
            // 简单的手机号格式验证
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                showResult(resultDiv, '请输入正确的手机号格式', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/sms/code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        phone: phone
                    })
                });
                
                const data = await response.json();
                
                if (data.code === 1) {
                    showResult(resultDiv, `发送成功！\n验证密钥: ${data.data.verificationKey}`, 'success');
                    // 自动填充验证密钥
                    document.getElementById('verificationKey').value = data.data.verificationKey;
                    // 开始倒计时
                    startCountdown();
                } else {
                    showResult(resultDiv, `发送失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `请求失败: ${error.message}`, 'error');
            }
        }
        
        async function verifyCode() {
            const verificationKey = document.getElementById('verificationKey').value;
            const verificationCode = document.getElementById('verificationCode').value;
            const resultDiv = document.getElementById('verifyResult');
            
            if (!verificationKey) {
                showResult(resultDiv, '请输入验证密钥', 'error');
                return;
            }
            
            if (!verificationCode) {
                showResult(resultDiv, '请输入验证码', 'error');
                return;
            }
            
            if (!/^\d{4}$/.test(verificationCode)) {
                showResult(resultDiv, '验证码必须是4位数字', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/sms/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        verificationKey: verificationKey,
                        verificationCode: verificationCode
                    })
                });
                
                const data = await response.json();
                
                if (data.code === 1) {
                    const userInfo = data.data;
                    let resultText = `登录成功！\n\n用户信息：\n`;
                    resultText += `用户ID: ${userInfo.userId}\n`;
                    resultText += `用户名: ${userInfo.username}\n`;
                    resultText += `显示名称: ${userInfo.displayName}\n`;
                    resultText += `手机号: ${userInfo.mobile}\n`;
                    resultText += `JWT Token: ${userInfo.token.substring(0, 50)}...`;
                    
                    showResult(resultDiv, resultText, 'success');
                    
                    // 显示用户信息卡片
                    showUserInfoCard(userInfo);
                } else {
                    showResult(resultDiv, `验证失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `请求失败: ${error.message}`, 'error');
            }
        }
        
        function showResult(element, message, type) {
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }
        
        function showUserInfoCard(userInfo) {
            // 移除之前的用户信息卡片
            const existingCard = document.querySelector('.user-info');
            if (existingCard) {
                existingCard.remove();
            }
            
            // 创建新的用户信息卡片
            const userInfoCard = document.createElement('div');
            userInfoCard.className = 'user-info';
            userInfoCard.innerHTML = `
                <h3>登录成功 - 用户信息</h3>
                <p><strong>用户ID:</strong> ${userInfo.userId}</p>
                <p><strong>用户名:</strong> ${userInfo.username}</p>
                <p><strong>显示名称:</strong> ${userInfo.displayName}</p>
                <p><strong>手机号:</strong> ${userInfo.mobile}</p>
                <div class="token-display">
                    <strong>JWT Token:</strong><br>
                    ${userInfo.token}
                </div>
            `;
            
            // 将卡片添加到验证结果后面
            const verifyResult = document.getElementById('verifyResult');
            verifyResult.parentNode.insertBefore(userInfoCard, verifyResult.nextSibling);
        }
        
        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 为输入框添加回车键支持
            document.getElementById('phone').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendVerificationCode();
                }
            });
            
            document.getElementById('verificationCode').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    verifyCode();
                }
            });
            
            // 验证码输入框只允许数字
            document.getElementById('verificationCode').addEventListener('input', function(e) {
                this.value = this.value.replace(/[^0-9]/g, '');
            });
        });
    </script>
</body>
</html> 