<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>京东商品同步功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #666;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.success {
            background-color: #28a745;
        }
        .button.warning {
            background-color: #ffc107;
            color: #212529;
        }
        .button.danger {
            background-color: #dc3545;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .param-table th, .param-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .param-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .status-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 京东商品同步功能测试</h1>
        
        <div class="note">
            <strong>重要更新：</strong>京东商品同步功能已集成到京东OAuth2回调流程中。当用户完成京东授权后，系统会自动启动异步商品同步任务，无需手动触发。
        </div>
        
        <div class="note">
            <strong>功能说明：</strong>
            <ul>
                <li>用户完成京东OAuth2授权后，系统自动获取access_token</li>
                <li>保存access_token后立即启动异步商品同步任务</li>
                <li>同步包括商品基本信息、功能特性和多种类属性</li>
                <li>支持分页获取和增量更新</li>
                <li>提供实时同步状态查询</li>
            </ul>
        </div>
        
        <h2>接口信息</h2>
        <table class="param-table">
            <tr>
                <th>接口</th>
                <th>URL</th>
                <th>方法</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>京东回调</td>
                <td>/api/yiyicallback</td>
                <td>GET</td>
                <td>处理京东OAuth2回调，自动启动商品同步</td>
            </tr>
            <tr>
                <td>查询状态</td>
                <td>/api/jd/sync-status</td>
                <td>GET</td>
                <td>查询指定团队的同步状态</td>
            </tr>
        </table>
        
        <h2>同步流程说明</h2>
        <div class="container">
            <ol>
                <li><strong>用户授权：</strong>用户点击京东授权链接，跳转到京东OAuth2页面</li>
                <li><strong>回调处理：</strong>用户授权后，京东回调到 <code>/api/yiyicallback</code> 接口</li>
                <li><strong>获取Token：</strong>系统使用授权码调用京东API获取access_token</li>
                <li><strong>保存Token：</strong>将access_token等信息保存到数据库</li>
                <li><strong>启动同步：</strong>自动启动异步商品同步任务</li>
                <li><strong>同步执行：</strong>后台异步获取商品数据并保存到数据库</li>
                <li><strong>状态查询：</strong>可通过状态接口查询同步进度</li>
            </ol>
        </div>
    </div>

    <div class="container">
        <h2>查询同步状态</h2>
        <div class="form-group">
            <label for="statusTeamId">团队ID *</label>
            <input type="text" id="statusTeamId" placeholder="请输入团队ID" value="1">
        </div>
        
        <button class="button success" onclick="getSyncStatus()">📊 查询同步状态</button>
        <button class="button" onclick="startAutoRefresh()">🔄 开启自动刷新</button>
        <button class="button danger" onclick="stopAutoRefresh()">⏹️ 停止刷新</button>
        
        <div class="status-container">
            <span>自动刷新状态：</span>
            <span id="autoRefreshStatus">已停止</span>
            <div id="loadingIndicator" class="loading" style="display: none;"></div>
        </div>
        
        <div id="statusResult"></div>
    </div>

    <div class="container">
        <h2>数据库表结构</h2>
        <table class="param-table">
            <tr>
                <th>表名</th>
                <th>简称</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>train_jd_products</td>
                <td>TJP</td>
                <td>京东商品基本信息表</td>
            </tr>
            <tr>
                <td>train_jd_products_features</td>
                <td>TJPF</td>
                <td>京东商品功能特性表</td>
            </tr>
            <tr>
                <td>train_jd_products_mcp</td>
                <td>TJPM</td>
                <td>京东商品多种类属性表</td>
            </tr>
        </table>
    </div>

    <div class="container">
        <h2>详细同步流程</h2>
        <div class="note">
            <ol>
                <li><strong>分页获取：</strong>从第1页开始，每页获取配置的商品数量（默认10条）</li>
                <li><strong>数据解析：</strong>解析京东API返回的JSON响应，提取商品信息</li>
                <li><strong>存在性检查：</strong>通过wareId检查商品是否已存在</li>
                <li><strong>时间比较：</strong>如果商品存在，比较modified字段决定是否更新</li>
                <li><strong>批量插入：</strong>同时插入商品基本信息、功能特性和多种类属性</li>
                <li><strong>循环处理：</strong>继续获取下一页，直到返回数据量小于分页大小</li>
            </ol>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;
        
        // 查询同步状态
        async function getSyncStatus() {
            const teamId = document.getElementById('statusTeamId').value.trim();
            
            if (!teamId) {
                showResult('statusResult', '请输入团队ID', 'error');
                return;
            }
            
            try {
                const response = await fetch(`/api/jd/sync-status?teamId=${teamId}`);
                const result = await response.json();
                
                if (result.code === 1) {
                    const timestamp = new Date().toLocaleString();
                    showResult('statusResult', `📊 同步状态：${result.data}\n⏰ 查询时间：${timestamp}`, 'info');
                } else {
                    showResult('statusResult', `❌ ${result.message}`, 'error');
                }
                
            } catch (error) {
                showResult('statusResult', `❌ 请求失败: ${error.message}`, 'error');
            }
        }
        
        // 开启自动刷新
        function startAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
            
            document.getElementById('autoRefreshStatus').textContent = '运行中（每5秒）';
            document.getElementById('loadingIndicator').style.display = 'inline-block';
            
            autoRefreshInterval = setInterval(() => {
                getSyncStatus();
            }, 5000);
        }
        
        // 停止自动刷新
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
            
            document.getElementById('autoRefreshStatus').textContent = '已停止';
            document.getElementById('loadingIndicator').style.display = 'none';
        }
        
        // 显示结果
        function showResult(elementId, content, type) {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.className = `result ${type}`;
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('京东商品同步状态查询页面加载完成');
        });
        
        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            stopAutoRefresh();
        });
    </script>
</body>
</html> 