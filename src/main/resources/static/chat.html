<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模拟客服聊天系统</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 24px;
        }

        .header .info {
            font-size: 14px;
        }

        .main-content {
            flex: 1;
            display: flex;
            padding: 20px;
            gap: 20px;
            overflow: hidden;
        }

        .robot-list {
            width: 300px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }

        .robot-list-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
            border-radius: 10px 10px 0 0;
        }

        .robot-list-header h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .add-robot-btn {
            width: 100%;
            padding: 10px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .add-robot-btn:hover {
            background: #218838;
        }

        .robot-list-content {
            flex: 1;
            overflow-y: auto;
        }

        .robot-item {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
            position: relative;
        }

        .robot-item:hover {
            background: #f8f9fa;
        }

        .robot-item.active {
            background: #e3f2fd;
            border-left: 4px solid #667eea;
        }

        .robot-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .robot-status {
            font-size: 12px;
            color: #666;
        }

        .status-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-online {
            background: #28a745;
        }

        .status-connecting {
            background: #ffc107;
        }

        .chat-area {
            flex: 1;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
            border-radius: 10px 10px 0 0;
        }

        .chat-header h3 {
            color: #333;
            margin-bottom: 5px;
        }

        .chat-header .chat-info {
            font-size: 14px;
            color: #666;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f9f9f9;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            gap: 10px;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #667eea;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: #28a745;
        }

        .message-content {
            flex: 1;
            max-width: 70%;
        }

        .message-bubble {
            background: white;
            padding: 12px 16px;
            border-radius: 18px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            word-wrap: break-word;
        }

        .message.user .message-bubble {
            background: #667eea;
            color: white;
        }

        .message-time {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
            text-align: right;
        }

        .message.user .message-time {
            text-align: left;
        }

        .chat-input {
            padding: 20px;
            border-top: 1px solid #eee;
            background: white;
            border-radius: 0 0 10px 10px;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e1e1e1;
            border-radius: 25px;
            font-size: 14px;
            resize: none;
            max-height: 100px;
            min-height: 44px;
        }

        .message-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .send-btn {
            padding: 12px 24px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }

        .send-btn:hover {
            background: #5a6fd8;
        }

        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #999;
        }

        .empty-state i {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e1e1e1;
            border-radius: 2px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-fill {
            height: 100%;
            background: #667eea;
            transition: width 0.3s ease;
            width: 0%;
        }

        .typing-indicator {
            display: none;
            padding: 10px 16px;
            background: #f0f0f0;
            border-radius: 18px;
            margin-bottom: 10px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #999;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-10px);
            }
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 5px;
            color: white;
            font-size: 14px;
            z-index: 1000;
        }

        .status-connected {
            background: #28a745;
        }

        .status-disconnected {
            background: #dc3545;
        }

        .status-connecting {
            background: #ffc107;
            color: #333;
        }

        /* 产品消息样式 */
        .product-bubble {
            background: #f0f8ff !important;
            border: 1px solid #667eea;
            max-width: none !important;
            padding: 0 !important;
        }

        .product-list-message {
            width: 100%;
        }

        .product-item {
            background: white;
            border: none;
            border-radius: 0;
            padding: 0;
            margin-bottom: 15px;
        }

        .product-item:last-child {
            margin-bottom: 0;
        }

        .product-content {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            padding: 12px;
        }

        .product-image {
            flex-shrink: 0;
            width: 80px;
            height: 80px;
            border-radius: 6px;
            overflow: hidden;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .no-image {
            font-size: 32px;
            color: #ccc;
        }

        .product-details {
            flex: 1;
            min-width: 0;
        }

        .product-name {
            font-size: 14px;
            color: #333;
            line-height: 1.4;
            margin: 0;
            word-wrap: break-word;
            font-weight: normal;
        }

        .product-divider {
            height: 1px;
            background: #e0e0e0;
            margin: 0 12px;
        }

        .product-link {
            padding: 8px 12px;
            font-size: 12px;
        }

        .product-link a {
            color: #1e90ff;
            text-decoration: none;
            word-wrap: break-word;
            display: block;
        }

        .product-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>模拟客服聊天系统</h1>
        <div class="info">
            <span id="serviceInfo">客服: 加载中...</span>
        </div>
    </div>

    <div class="main-content">
        <div class="robot-list">
            <div class="robot-list-header">
                <h3>模拟客户列表</h3>
                <div id="loadingStatus" style="font-size: 14px; color: #666; text-align: center; padding: 10px;">
                    正在初始化客户...
                </div>
                <button class="add-robot-btn" onclick="addNewRobot()" style="display: none;">+ 添加新客户</button>
            </div>
            <div class="robot-list-content" id="robotList">
                <!-- 机器人列表将在这里动态生成 -->
            </div>
        </div>

        <div class="chat-area">
            <div class="chat-header">
                <h3 id="chatTitle">请选择一个客户开始对话</h3>
                <div class="chat-info" id="chatInfo">选择左侧客户列表中的任意客户开始训练</div>
            </div>
            <div class="chat-messages" id="chatMessages">
                <div class="empty-state">
                    <i>💬</i>
                    <p>选择一个模拟客户开始对话训练</p>
                </div>
            </div>
            <div class="chat-input">
                <div class="input-container">
                    <textarea class="message-input" id="messageInput" placeholder="输入您的回复..." disabled></textarea>
                    <button class="send-btn" id="sendBtn" onclick="sendMessage()" disabled>发送</button>
                </div>
            </div>
        </div>
    </div>

    <div class="connection-status status-connecting" id="connectionStatus">
        连接中...
    </div>

    <script>
        // 全局变量
        let stompClient = null;
        let currentSessionId = null;
        let robots = [];
        let urlParams = new URLSearchParams(window.location.search);
        let sceneName = urlParams.get('sceneName');
        let servicerId = urlParams.get('servicerId');
        let token = urlParams.get('token');
        let isThinking = urlParams.get('isThinking') === 'true';
        let isStreaming = urlParams.get('isStreaming') === 'true';
        let robotCount = urlParams.get('robotCount') ? parseInt(urlParams.get('robotCount')) : null;
        let systemPrompt = urlParams.get('systemPrompt'); // 单个提示词
        let systemPrompts = null; // 多个提示词数组
        
        // 解析系统提示词数组
        if (urlParams.get('systemPrompts')) {
            try {
                systemPrompts = JSON.parse(urlParams.get('systemPrompts'));
            } catch (e) {
                console.error('解析systemPrompts失败:', e);
                systemPrompts = null;
            }
        }

        // 解析脚本数据 - 优先从localStorage读取，避免URL过长问题
        let scriptData = {
            buyerRequirement: null,
            productList: null,
            relateImgs: null,
            flowNodes: null,
            intents: null
        };

        // 如果URL参数表明有脚本数据，从localStorage读取
        if (urlParams.get('hasScriptData') === 'true') {
            try {
                const storedScriptData = localStorage.getItem('aiTrainScriptData');
                if (storedScriptData) {
                    const parsedScriptData = JSON.parse(storedScriptData);
                    scriptData = {
                        buyerRequirement: parsedScriptData.buyerRequirement || null,
                        productList: parsedScriptData.productList || null,
                        relateImgs: parsedScriptData.relateImgs || null,
                        flowNodes: parsedScriptData.flowNodes || null,
                        intents: parsedScriptData.intents || null
                    };
                    console.log('已从localStorage加载脚本数据:', scriptData);
                }
            } catch (e) {
                console.error('从localStorage解析脚本数据失败:', e);
            }
        }

        // 检查参数
        if (!sceneName || !token) {
            alert('缺少必要参数，请重新登录');
            window.location.href = 'login.html';
        }

        // 初始化WebSocket连接
        function connect() {
            updateConnectionStatus('connecting', '连接中...');
            
            const socket = new SockJS('/ws-sockjs');
            stompClient = Stomp.over(socket);
            
            stompClient.connect({}, function (frame) {
                updateConnectionStatus('connected', '已连接');
                console.log('Connected: ' + frame);
                
                // 订阅批量初始化响应
                stompClient.subscribe('/topic/initMultiple', function (message) {
                    handleMultipleInitResponse(JSON.parse(message.body));
                });
                
                // 订阅单个初始化响应（兼容性）
                stompClient.subscribe('/topic/init', function (message) {
                    handleInitResponse(JSON.parse(message.body));
                });
                
                // 连接成功后自动初始化4个机器人
                autoInitializeRobots();
                
            }, function (error) {
                updateConnectionStatus('disconnected', '连接失败');
                console.error('Connection error: ' + error);
                setTimeout(() => connect(), 5000); // 5秒后重连
            });
        }

        // 自动初始化机器人
        function autoInitializeRobots() {
            if (!stompClient || !stompClient.connected) {
                console.error('WebSocket未连接');
                updateLoadingStatus('连接失败，无法初始化客户');
                return;
            }

            updateLoadingStatus('正在初始化客户...');

            const initData = {
                sceneName: sceneName,
                servicerId: servicerId,
                token: token,
                isThinking: isThinking,
                isStreaming: isStreaming
            };

            // 添加机器人数量参数
            if (robotCount) {
                initData.robotCount = robotCount;
            }

            // 添加系统提示词参数
            if (sceneName === 'trialOne' && systemPrompt) {
                // 单个机器人使用单个提示词
                initData.systemPrompt = systemPrompt;
            } else if ((sceneName === 'trialFour' || sceneName === 'formal') && systemPrompts && systemPrompts.length > 0) {
                // 多个机器人使用提示词数组
                initData.systemPrompts = systemPrompts;
            }

            // 添加脚本数据
            if (scriptData.buyerRequirement) {
                initData.buyerRequirement = scriptData.buyerRequirement;
            }
            if (scriptData.productList) {
                initData.productList = scriptData.productList;
            }
            if (scriptData.relateImgs) {
                initData.relateImgs = scriptData.relateImgs;
            }
            if (scriptData.flowNodes) {
                initData.flowNodes = scriptData.flowNodes;
            }
            if (scriptData.intents) {
                initData.intents = scriptData.intents;
            }

            console.log('发送初始化数据:', initData);
            stompClient.send("/app/initMultiple", {}, JSON.stringify(initData));
        }

        // 更新加载状态
        function updateLoadingStatus(message) {
            const loadingStatus = document.getElementById('loadingStatus');
            if (loadingStatus) {
                loadingStatus.textContent = message;
            }
        }

        // 处理批量初始化响应
        function handleMultipleInitResponse(response) {
            if (response.error) {
                alert('批量初始化失败: ' + response.message);
                updateLoadingStatus('初始化失败');
                return;
            }

            // 隐藏加载状态，显示添加按钮
            const loadingStatus = document.getElementById('loadingStatus');
            if (loadingStatus) {
                loadingStatus.style.display = 'none';
            }
            const addRobotBtn = document.querySelector('.add-robot-btn');
            if (addRobotBtn) {
                addRobotBtn.style.display = 'block';
            }

            // 处理每个机器人
            response.forEach(robotData => {
                const robot = {
                    sessionId: robotData.sessionId,
                    robotName: robotData.robotName,
                    serviceName: robotData.serviceName,
                    firstMessage: robotData.firstMessage,
                    productList: robotData.productList || [], // 添加产品列表
                    messages: []
                };

                // 添加首条消息
                robot.messages.push({
                    type: 'robot',
                    content: robotData.firstMessage,
                    timestamp: new Date()
                });

                robots.push(robot);

                // 订阅该会话的消息
                stompClient.subscribe(`/topic/chat/${robotData.sessionId}`, function (message) {
                    handleChatMessage(robotData.sessionId, message.body);
                });
            });

            updateRobotList();
            
            // 更新客服信息（从第一个机器人获取）
            if (response.length > 0) {
                document.getElementById('serviceInfo').textContent = `客服: ${response[0].serviceName}`;
            }

            // 自动选择第一个机器人
            if (robots.length > 0) {
                selectRobot(robots[0].sessionId);
            }

            // 如果有产品信息，将产品信息作为机器人消息添加到聊天记录中
            if (response.length > 0 && response[0].productList && response[0].productList.length > 0) {
                // 为每个机器人添加产品信息消息
                response.forEach(robotData => {
                    if (robotData.productList && robotData.productList.length > 0) {
                        const robot = robots.find(r => r.sessionId === robotData.sessionId);
                        if (robot) {
                            // 添加产品信息消息
                            robot.messages.push({
                                type: 'robot-product',
                                content: robotData.productList,
                                timestamp: new Date()
                            });
                        }
                    }
                });
                
                // 如果当前选中的机器人有产品信息，更新聊天界面
                if (currentSessionId) {
                    const currentRobot = robots.find(r => r.sessionId === currentSessionId);
                    if (currentRobot) {
                        updateChatMessages(currentRobot.messages);
                    }
                }
            }
        }

        // 添加单个新机器人（保留兼容性）
        function addNewRobot() {
            if (!stompClient || !stompClient.connected) {
                alert('WebSocket未连接，请稍后再试');
                return;
            }

            const initData = {
                sceneName: sceneName,
                servicerId: servicerId,
                token: token,
                isThinking: isThinking,
                isStreaming: isStreaming
            };

            stompClient.send("/app/init", {}, JSON.stringify(initData));
        }

        // 处理初始化响应
        function handleInitResponse(response) {
            if (response.error) {
                alert('初始化失败: ' + response.message);
                return;
            }

            const robot = {
                sessionId: response.sessionId,
                robotName: response.robotName,
                serviceName: response.serviceName,
                firstMessage: response.firstMessage,
                messages: []
            };

            // 添加首条消息
            robot.messages.push({
                type: 'robot',
                content: response.firstMessage,
                timestamp: new Date()
            });

            robots.push(robot);
            updateRobotList();
            
            // 更新客服信息
            document.getElementById('serviceInfo').textContent = `客服: ${response.serviceName}`;

            // 自动选择新添加的机器人
            selectRobot(robot.sessionId);

            // 订阅该会话的消息
            stompClient.subscribe(`/topic/chat/${response.sessionId}`, function (message) {
                handleChatMessage(response.sessionId, message.body);
            });
        }

        // 更新机器人列表
        function updateRobotList() {
            const robotList = document.getElementById('robotList');
            robotList.innerHTML = '';

            robots.forEach(robot => {
                const robotElement = document.createElement('div');
                robotElement.className = 'robot-item';
                robotElement.onclick = () => selectRobot(robot.sessionId);
                
                if (robot.sessionId === currentSessionId) {
                    robotElement.classList.add('active');
                }

                robotElement.innerHTML = `
                    <div class="robot-name">${robot.robotName}</div>
                    <div class="robot-status">
                        <span class="status-dot status-online"></span>
                        在线 - 最近消息: ${robot.messages.length > 0 ? formatTime(robot.messages[robot.messages.length - 1].timestamp) : '刚刚'}
                    </div>
                `;

                robotList.appendChild(robotElement);
            });
        }

        // 选择机器人
        function selectRobot(sessionId) {
            currentSessionId = sessionId;
            const robot = robots.find(r => r.sessionId === sessionId);
            
            if (!robot) return;

            // 更新聊天标题
            document.getElementById('chatTitle').textContent = `与 ${robot.robotName} 对话`;
            document.getElementById('chatInfo').textContent = `会话ID: ${sessionId}`;

            // 更新消息列表
            updateChatMessages(robot.messages);

            // 启用输入框
            document.getElementById('messageInput').disabled = false;
            document.getElementById('sendBtn').disabled = false;

            // 更新机器人列表选中状态
            updateRobotList();
        }

        // 更新聊天消息
        function updateChatMessages(messages) {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = '';

            messages.forEach(message => {
                const messageElement = createMessageElement(message);
                chatMessages.appendChild(messageElement);
            });

            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 创建消息元素
        function createMessageElement(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${message.type === 'robot-product' ? 'robot' : message.type}`;

            let avatar, content;
            
            if (message.type === 'user') {
                avatar = '👨‍💼';
                content = message.content;
            } else if (message.type === 'robot-product') {
                avatar = '🛍️';
                content = createProductListHTML(message.content);
            } else {
                avatar = '🤖';
                content = message.content;
            }

            messageDiv.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">
                    <div class="message-bubble ${message.type === 'robot-product' ? 'product-bubble' : ''}">${content}</div>
                    <div class="message-time">${formatTime(message.timestamp)}</div>
                </div>
            `;

            return messageDiv;
        }

        // 创建产品列表HTML
        function createProductListHTML(productList) {
            let html = '<div class="product-list-message">';
            
            productList.forEach((product, index) => {
                // 商品名称截断处理
                const productName = product.productName || '未知产品';
                const truncatedName = productName.length > 60 ? productName.substring(0, 60) + '...' : productName;
                
                // 商品链接截断处理
                const productLink = product.productLink || '';
                const truncatedLink = productLink.length > 50 ? productLink.substring(0, 50) + '...' : productLink;
                
                html += `
                    <div class="product-item">
                        <div class="product-content">
                            <div class="product-image">
                                ${product.productImage ? `<img src="${product.productImage}" alt="商品图片" />` : '<div class="no-image">📦</div>'}
                            </div>
                            <div class="product-details">
                                <div class="product-name">${truncatedName}</div>
                            </div>
                        </div>
                        <div class="product-divider"></div>
                        <div class="product-link">
                            ${productLink ? `<a href="${productLink}" target="_blank" title="${productLink}">${truncatedLink}</a>` : '暂无链接'}
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            return html;
        }

        // 发送消息
        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();

            if (!message || !currentSessionId || !stompClient || !stompClient.connected) {
                return;
            }

            // 添加用户消息到界面
            const robot = robots.find(r => r.sessionId === currentSessionId);
            if (robot) {
                robot.messages.push({
                    type: 'user',
                    content: message,
                    timestamp: new Date()
                });
                updateChatMessages(robot.messages);
            }

            // 发送消息到后端
            const sendData = {
                sessionId: currentSessionId,
                message: message
            };

            stompClient.send("/app/send", {}, JSON.stringify(sendData));

            // 清空输入框
            messageInput.value = '';
            
            // 显示正在输入指示器
            showTypingIndicator();
        }

        // 处理聊天消息响应
        function handleChatMessage(sessionId, response) {
            const robot = robots.find(r => r.sessionId === sessionId);
            if (!robot) return;

            // 简化处理：只有明确是错误格式的JSON才解析，其他全部作为文本处理
            let isErrorResponse = false;
            let textContent = response;
            
            // 只检查明确的错误响应格式：{"error":true,"message":"..."}
            if (typeof response === 'string' && 
                response.startsWith('{"error":true') && 
                response.includes('"message"')) {
                try {
                    const parsed = JSON.parse(response);
                    if (parsed.error && parsed.message) {
                        isErrorResponse = true;
                        textContent = `错误: ${parsed.message}`;
                    }
                } catch (e) {
                    // 即使看起来像错误JSON但解析失败，也作为文本处理
                    textContent = response;
                }
            }
            // 其他所有情况都直接作为文本内容处理
            
            if (isErrorResponse) {
                robot.messages.push({
                    type: 'system',
                    content: textContent,
                    timestamp: new Date()
                });
            } else {
                // 处理流式响应 - 直接使用原始响应作为文本
                const lastMessage = robot.messages[robot.messages.length - 1];
                
                if (lastMessage && lastMessage.type === 'robot' && lastMessage.streaming) {
                    // 更新现有的流式消息
                    lastMessage.content += response; // 直接使用原始响应
                    // 如果收到空内容，说明流式响应结束
                    if (response === '') {
                        lastMessage.streaming = false;
                    }
                } else {
                    // 创建新的机器人消息
                    robot.messages.push({
                        type: 'robot',
                        content: response, // 直接使用原始响应
                        streaming: response !== '', // 如果内容为空则不是流式状态
                        timestamp: new Date()
                    });
                }
            }

            // 如果是当前选中的会话，更新界面
            if (sessionId === currentSessionId) {
                updateChatMessages(robot.messages);
            }

            // 隐藏正在输入指示器
            hideTypingIndicator();
        }

        // 显示正在输入指示器
        function showTypingIndicator() {
            // 实现正在输入的视觉反馈
            console.log('Robot is typing...');
        }

        // 隐藏正在输入指示器
        function hideTypingIndicator() {
            console.log('Robot stopped typing.');
        }

        // 更新连接状态
        function updateConnectionStatus(status, message) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.className = `connection-status status-${status}`;
            statusElement.textContent = message;

            // 3秒后隐藏连接状态（仅当连接成功时）
            if (status === 'connected') {
                setTimeout(() => {
                    statusElement.style.display = 'none';
                }, 3000);
            } else {
                statusElement.style.display = 'block';
            }
        }

        // 格式化时间
        function formatTime(date) {
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 监听回车键发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 页面加载完成后连接WebSocket
        window.addEventListener('load', function() {
            connect();
        });

        // 页面卸载时断开连接
        window.addEventListener('beforeunload', function() {
            if (stompClient && stompClient.connected) {
                stompClient.disconnect();
            }
        });

        // 显示产品列表
        function displayProductList(productList) {
            console.log('收到产品列表:', productList);
            
            // 创建产品展示区域
            const existingProductArea = document.getElementById('productArea');
            if (existingProductArea) {
                existingProductArea.remove();
            }

            const productArea = document.createElement('div');
            productArea.id = 'productArea';
            productArea.style.cssText = `
                position: fixed;
                top: 20px;
                left: 20px;
                background: white;
                border: 2px solid #667eea;
                border-radius: 10px;
                padding: 15px;
                max-width: 400px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                z-index: 1000;
            `;

            const header = document.createElement('h3');
            header.textContent = '相关产品信息';
            header.style.cssText = 'margin: 0 0 10px 0; color: #333; font-size: 16px;';
            productArea.appendChild(header);

            productList.forEach((product, index) => {
                const productItem = document.createElement('div');
                productItem.style.cssText = `
                    border: 1px solid #eee;
                    border-radius: 8px;
                    padding: 10px;
                    margin-bottom: 10px;
                    background: #f9f9f9;
                `;

                const productName = document.createElement('div');
                productName.textContent = `🛍️ ${product.productName || '未知产品'}`;
                productName.style.cssText = 'font-weight: bold; margin-bottom: 5px; font-size: 14px;';
                productItem.appendChild(productName);

                if (product.productId) {
                    const productId = document.createElement('div');
                    productId.textContent = `ID: ${product.productId}`;
                    productId.style.cssText = 'font-size: 12px; color: #666; margin-bottom: 5px;';
                    productItem.appendChild(productId);
                }

                if (product.productLink) {
                    const productLink = document.createElement('a');
                    productLink.href = product.productLink;
                    productLink.target = '_blank';
                    productLink.textContent = '查看详情';
                    productLink.style.cssText = 'color: #667eea; text-decoration: none; font-size: 12px;';
                    productItem.appendChild(productLink);
                }

                productArea.appendChild(productItem);
            });

            // 添加关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.textContent = '×';
            closeBtn.style.cssText = `
                position: absolute;
                top: 5px;
                right: 10px;
                background: none;
                border: none;
                font-size: 20px;
                cursor: pointer;
                color: #999;
            `;
            closeBtn.onclick = () => productArea.remove();
            productArea.appendChild(closeBtn);

            document.body.appendChild(productArea);

            // 5秒后自动隐藏
            setTimeout(() => {
                if (productArea.parentNode) {
                    productArea.style.opacity = '0.7';
                }
            }, 5000);
        }
    </script>
</body>
</html> 