<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模拟客服聊天系统 - 登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 400px;
            max-width: 90%;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #666;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e1e1;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .tips {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            font-size: 14px;
            color: #666;
        }

        .tips h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .tips ul {
            padding-left: 20px;
        }

        .tips li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>模拟客服聊天系统</h1>
            <p>与AI模拟客户进行实战训练</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="servicerId">客服ID (可选)</label>
                <input type="text" id="servicerId" name="servicerId" placeholder="留空则使用JWT中的用户ID">
            </div>

            <div class="form-group">
                <label for="sceneName">训练场景</label>
                <select id="sceneName" name="sceneName" required>
                    <option value="">请选择训练场景</option>
                    <option value="trialOne">试用剧本</option>
                    <option value="trialFour">随机剧本</option>
                    <option value="formal">正式剧本</option>
                </select>
            </div>

            <div class="form-group">
                <label for="isThinking">是否使用思考模型</label>
                <select id="isThinking" name="isThinking" required>
                    <option value="false">否 - 使用Normal模型</option>
                    <option value="true">是 - 使用Think模型</option>
                </select>
            </div>

            <div class="form-group">
                <label for="isStreaming">是否使用流式响应</label>
                <select id="isStreaming" name="isStreaming" required>
                    <option value="true">是 - 流式响应</option>
                    <option value="false">否 - 非流式响应</option>
                </select>
            </div>

            <!-- 机器人数量设置 -->
            <div class="form-group" id="robotCountGroup" style="display: none;">
                <label for="robotCount">机器人数量</label>
                <input type="number" id="robotCount" name="robotCount" min="1" max="10" placeholder="留空则使用默认数量">
                <small style="color: #666; font-size: 12px;">试用剧本默认1个，其他场景默认4个</small>
            </div>

            <!-- 单个机器人系统提示词 -->
            <div class="form-group" id="singlePromptGroup" style="display: none;">
                <label for="systemPrompt">系统提示词</label>
                <textarea id="systemPrompt" name="systemPrompt" rows="3" placeholder="留空则使用默认提示词" style="width: 100%; padding: 12px; border: 2px solid #e1e1e1; border-radius: 8px; font-size: 16px; resize: vertical;"></textarea>
                <small style="color: #666; font-size: 12px;">自定义机器人的角色和行为</small>
            </div>

            <!-- 多个机器人系统提示词 -->
            <div class="form-group" id="multiplePromptsGroup" style="display: none;">
                <label for="systemPrompts">系统提示词列表</label>
                <div id="promptsContainer">
                    <div class="prompt-item">
                        <label style="font-size: 14px; color: #666;">机器人1提示词:</label>
                        <textarea name="systemPrompts" rows="2" placeholder="留空则使用默认提示词" style="width: 100%; padding: 8px; border: 2px solid #e1e1e1; border-radius: 5px; font-size: 14px; margin-bottom: 10px; resize: vertical;"></textarea>
                    </div>
                    <div class="prompt-item">
                        <label style="font-size: 14px; color: #666;">机器人2提示词:</label>
                        <textarea name="systemPrompts" rows="2" placeholder="留空则使用默认提示词" style="width: 100%; padding: 8px; border: 2px solid #e1e1e1; border-radius: 5px; font-size: 14px; margin-bottom: 10px; resize: vertical;"></textarea>
                    </div>
                    <div class="prompt-item">
                        <label style="font-size: 14px; color: #666;">机器人3提示词:</label>
                        <textarea name="systemPrompts" rows="2" placeholder="留空则使用默认提示词" style="width: 100%; padding: 8px; border: 2px solid #e1e1e1; border-radius: 5px; font-size: 14px; margin-bottom: 10px; resize: vertical;"></textarea>
                    </div>
                    <div class="prompt-item">
                        <label style="font-size: 14px; color: #666;">机器人4提示词:</label>
                        <textarea name="systemPrompts" rows="2" placeholder="留空则使用默认提示词" style="width: 100%; padding: 8px; border: 2px solid #e1e1e1; border-radius: 5px; font-size: 14px; margin-bottom: 10px; resize: vertical;"></textarea>
                    </div>
                </div>
                <button type="button" id="addPromptBtn" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 5px; font-size: 14px; cursor: pointer; margin-top: 10px;">+ 添加更多机器人</button>
                <small style="color: #666; font-size: 12px; display: block; margin-top: 5px;">为每个机器人自定义角色和行为</small>
            </div>

            <!-- 脚本数据输入 -->
            <div class="form-group" id="scriptDataGroup" style="display: none;">
                <label for="scriptData">完整脚本数据 (JSON格式)</label>
                <textarea id="scriptData" name="scriptData" rows="15" placeholder="粘贴完整的JSON脚本数据，例如exampleA的内容" style="width: 100%; padding: 12px; border: 2px solid #e1e1e1; border-radius: 8px; font-size: 14px; font-family: monospace; resize: vertical;"></textarea>
                <small style="color: #666; font-size: 12px;">包含buyerRequirement、productList、relateImgs、flowNodes、intents等完整信息</small>
                <button type="button" id="loadExampleBtn" style="margin-top: 10px; padding: 8px 16px; background: #17a2b8; color: white; border: none; border-radius: 5px; font-size: 14px; cursor: pointer;">加载示例数据</button>
            </div>

            <div class="form-group">
                <label for="token">JWT令牌</label>
                <input type="text" id="token" name="token" placeholder="输入您的JWT令牌" required>
            </div>

            <button type="submit" class="login-btn">进入聊天室</button>
        </form>

        <div class="tips">
            <h3>使用说明:</h3>
            <ul>
                <li>客服ID可以留空，系统会自动从JWT令牌中获取</li>
                <li>试用剧本：单个模拟客户进行训练</li>
                <li>随机剧本：4个不同角色的客户同时训练</li>
                <li>正式剧本：正式环境的客户模拟</li>
                <li>思考模型：更智能但响应较慢</li>
                <li>流式响应：实时显示回答过程</li>
                <li>机器人数量：可自定义创建的机器人数量</li>
                <li>系统提示词：自定义机器人角色和行为，留空使用默认</li>
                <li>可为每个机器人设置不同的系统提示词以获得不同的对话风格</li>
                <li>JWT令牌用于身份验证和获取团队信息</li>
            </ul>
        </div>
    </div>

    <script>
        // 场景切换时显示/隐藏相应的输入字段
        document.getElementById('sceneName').addEventListener('change', function() {
            const sceneName = this.value;
            const robotCountGroup = document.getElementById('robotCountGroup');
            const singlePromptGroup = document.getElementById('singlePromptGroup');
            const multiplePromptsGroup = document.getElementById('multiplePromptsGroup');
            const scriptDataGroup = document.getElementById('scriptDataGroup');
            
            // 隐藏所有可选字段
            robotCountGroup.style.display = 'none';
            singlePromptGroup.style.display = 'none';
            multiplePromptsGroup.style.display = 'none';
            scriptDataGroup.style.display = 'none';
            
            if (sceneName === 'trialOne') {
                // 试用剧本：显示单个机器人选项或脚本数据选项
                robotCountGroup.style.display = 'block';
                singlePromptGroup.style.display = 'block';
                scriptDataGroup.style.display = 'block';
            } else if (sceneName === 'trialFour' || sceneName === 'formal') {
                // 随机剧本和正式剧本：显示多个机器人选项或脚本数据选项
                robotCountGroup.style.display = 'block';
                multiplePromptsGroup.style.display = 'block';
                scriptDataGroup.style.display = 'block';
            }
        });

        // 添加更多机器人提示词
        document.getElementById('addPromptBtn').addEventListener('click', function() {
            const container = document.getElementById('promptsContainer');
            const currentCount = container.children.length;
            const newIndex = currentCount + 1;
            
            const promptItem = document.createElement('div');
            promptItem.className = 'prompt-item';
            promptItem.innerHTML = `
                <label style="font-size: 14px; color: #666;">机器人${newIndex}提示词:</label>
                <textarea name="systemPrompts" rows="2" placeholder="留空则使用默认提示词" style="width: 100%; padding: 8px; border: 2px solid #e1e1e1; border-radius: 5px; font-size: 14px; margin-bottom: 10px; resize: vertical;"></textarea>
                <button type="button" class="remove-prompt-btn" onclick="removePromptItem(this)" style="padding: 4px 8px; background: #dc3545; color: white; border: none; border-radius: 3px; font-size: 12px; cursor: pointer; margin-bottom: 10px;">删除</button>
            `;
            container.appendChild(promptItem);
        });

        // 删除提示词项
        function removePromptItem(button) {
            button.parentElement.remove();
            updatePromptLabels();
        }

        // 更新提示词标签编号
        function updatePromptLabels() {
            const container = document.getElementById('promptsContainer');
            const items = container.children;
            for (let i = 0; i < items.length; i++) {
                const label = items[i].querySelector('label');
                label.textContent = `机器人${i + 1}提示词:`;
            }
        }

        // 加载示例数据
        document.getElementById('loadExampleBtn').addEventListener('click', function() {
            const exampleData = {
                "groupId": 3,
                "name": "咨询产品:产自新疆阿勒泰天山，昼夜温差大，积累大量糖分，口感香甜软糯，独特地理环境孕育，闲时来一口，体验人间美味",
                "buyerRequirement": "买家是一位美食爱好者，平时就喜欢品尝各种特色水果。听闻新疆水果以香甜著称，一直想尝尝。此次希望购买产自新疆阿勒泰天山的水果，看重这里昼夜温差大，水果积累了大量糖分，口感香甜软糯的特点。期望水果新鲜度高，个头饱满，能在闲暇时光享受这份人间美味，最好是当季新鲜采摘发货的，能原汁原味地体验到独特地理环境孕育出的水果风味。",
                "intents": {
                    "id": 18,
                    "name": "咨询产品",
                    "parentName": "售前"
                },
                "productList": [
                    {
                        "externalProductId": "************",
                        "externalProductName": "对标 Mac Pro 性能，价格仅 1/5，内置全球首款消费级 AI 专用芯片，支持实时语音转写，下班时跑跑AI大模型，直逼世界巅峰",
                        "externalProductLink": "https://detail.tmall.com/item.htm?detail_redpacket_pop=true&id=************&ltk2=1747638740615o83npvj25txk6a5qbuz8s&ns=1&priceTId=2100c80217476387377946307e0c6c&query=%E5%9C%9F%E8%B1%86&skuId=5973554583314&spm=a21n57.1.hoverItem.1&utparam=%7B%22aplus_abtest%22%3A%22b6fe8e6cfe5adb260146b6109becc330%22%7D&xxc=ad_ztc",
                        "externalProductImage": "https://ai-playground.oss-cn-shanghai.aliyuncs.com/proc/pic_1_1749107342365_产品-LV.png"
                    },
                    {
                        "externalProductId": "************",
                        "externalProductName": "性能对标万元级日系风扇，价格仅 1/7，驱蚊功能通过 SGS 防蚊认证，2 米内驱蚊率 98%",
                        "externalProductLink": "https://detail.tmall.com/item.htm?detail_redpacket_pop=true&id=************&ltk2=1747638740615o83npvj25txk6a5qbuz8s&ns=1&priceTId=2100c80217476387377946307e0c6c&query=%E5%9C%9F%E8%B1%86&skuId=5973554583314&spm=a21n57.1.hoverItem.1&utparam=%7B%22aplus_abtest%22%3A%22b6fe8e6cfe5adb260146b6109becc330%22%7D&xxc=ad_ztc",
                        "externalProductImage": "https://ai-playground.oss-cn-shanghai.aliyuncs.com/proc/pic_1_1749107342646_su7.png"
                    }
                ],
                "relateImgs": [
                    {
                        "mediaType": 1,
                        "upload_type": 1,
                        "recognized_text": "图片信息:这是一张展示某款品牌手袋的图片。手袋主体呈长方体形状，表面为棕色，印有金色的品牌经典图案。手袋配有一条金色链条和一条红边的皮质肩带，肩带上有调节扣。背景是一个灰色的圆形表面，可能是桌子或凳子。整体展示方式是将手袋平放在灰色表面上。图片中没有可读的文字信息。 发送场景:在向客服咨询该手袋的颜色、外观细节、肩带调节方式等售前问题时主动发送给客服；或者在售后发现手袋存在外观质量问题（如图案磨损、链条损坏等）向客服反馈时发送。",
                        "url": "https://ai-playground.oss-cn-shanghai.aliyuncs.com/temp/pic/pic_1_1749107342365_产品-LV.png"
                    },
                    {
                        "mediaType": 1,
                        "upload_type": 1,
                        "recognized_text": "图片信息:这是一张展示一辆粉色汽车的图片。图片中的汽车为轿跑车型，整体外观线条流畅，车尾设计简洁，尾灯为贯穿式造型，车牌处标有\"SU 7\"字样。汽车停在一条道路上，背景是盛开的粉色樱花树，树木沿道路两侧整齐排列，上方有路灯杆，环境显得十分优美。展示方式是从汽车后方45度角拍摄，突出汽车的外观设计和背景的美景。 发送场景:顾客向客服咨询该款汽车的外观颜色、车型样式等售前问题时，主动发送给客服；或者客服向顾客确认汽车外观相关信息时，被动发送给客服。",
                        "url": "https://ai-playground.oss-cn-shanghai.aliyuncs.com/temp/pic/pic_1_1749107342646_su7.png"
                    },
                    {
                        "mediaType": 2,
                        "upload_type": 1,
                        "recognized_text": "图片描述的是一个操作视频，发送场景：顾客在向客服咨询自己的操作步骤是否正确，发送给客服；或者客服要求客户录屏时，被动发送给客服",
                        "url": "https://ai-playground.oss-cn-shanghai.aliyuncs.com/temp/video/vid_1_1749107343313_测试视频.mkv"
                    }
                ],
                "flowNodes": [
                    {
                        "nodeName": "水果产地确认",
                        "nodeBuyerRequirement": "请问你们这里有产自新疆阿勒泰天山的水果吗？这里昼夜温差大，水果糖分积累多，口感香甜软糯，我就想要这种水果。能确定是来自这个产地的吗？"
                    },
                    {
                        "nodeName": "水果品质咨询",
                        "nodeBuyerRequirement": "水果的新鲜度怎么样？是不是当季新鲜采摘发货的呀？个头饱满吗？我希望能原汁原味体验到独特地理环境孕育出的水果风味。"
                    },
                    {
                        "nodeName": "购买相关询问",
                        "nodeBuyerRequirement": "价格是怎么算的呢？有没有优惠活动？购买后多久能发货和送达呀，我想在闲暇时光能尽快享受到这份美味。"
                    }
                ]
            };

            document.getElementById('scriptData').value = JSON.stringify(exampleData, null, 2);
        });

        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const servicerId = document.getElementById('servicerId').value.trim();
            const sceneName = document.getElementById('sceneName').value;
            const isThinking = document.getElementById('isThinking').value;
            const isStreaming = document.getElementById('isStreaming').value;
            const token = document.getElementById('token').value.trim();
            
            if (!sceneName || !token) {
                alert('请填写必要信息！');
                return;
            }
            
            // 构建跳转URL
            const params = new URLSearchParams({
                sceneName: sceneName,
                isThinking: isThinking,
                isStreaming: isStreaming,
                token: token
            });
            
            if (servicerId) {
                params.append('servicerId', servicerId);
            }

            // 处理机器人数量
            const robotCount = document.getElementById('robotCount').value.trim();
            if (robotCount) {
                params.append('robotCount', robotCount);
            }

            // 处理系统提示词
            if (sceneName === 'trialOne') {
                // 单个机器人提示词
                const systemPrompt = document.getElementById('systemPrompt').value.trim();
                if (systemPrompt) {
                    params.append('systemPrompt', systemPrompt);
                }
            } else if (sceneName === 'trialFour' || sceneName === 'formal') {
                // 多个机器人提示词
                const promptTextareas = document.querySelectorAll('textarea[name="systemPrompts"]');
                const systemPrompts = [];
                promptTextareas.forEach(textarea => {
                    const prompt = textarea.value.trim();
                    if (prompt) {
                        systemPrompts.push(prompt);
                    }
                });
                if (systemPrompts.length > 0) {
                    params.append('systemPrompts', JSON.stringify(systemPrompts));
                }
            }

            // 处理脚本数据
            const scriptData = document.getElementById('scriptData').value.trim();
            if (scriptData) {
                try {
                    // 验证JSON格式
                    const parsedData = JSON.parse(scriptData);
                    
                    // 将脚本数据存储到localStorage而不是URL参数（避免URL过长）
                    localStorage.setItem('aiTrainScriptData', JSON.stringify(parsedData));
                    params.append('hasScriptData', 'true'); // 只传递标记参数
                    console.log('脚本数据已存储到localStorage');
                } catch (e) {
                    alert('脚本数据JSON格式错误，请检查：' + e.message);
                    return;
                }
            } else {
                // 如果没有脚本数据，清除localStorage中的旧数据
                localStorage.removeItem('aiTrainScriptData');
            }
            
            // 跳转到聊天页面
            window.location.href = `chat.html?${params.toString()}`;
        });
    </script>
</body>
</html> 