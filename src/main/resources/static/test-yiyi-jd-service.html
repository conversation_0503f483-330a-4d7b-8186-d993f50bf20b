<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>易易京东服务测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e1e8ed;
            border-radius: 6px;
            background-color: #fafbfc;
        }
        
        .test-section h2 {
            color: #34495e;
            margin-top: 0;
            margin-bottom: 15px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #2c3e50;
        }
        
        input[type="text"], input[type="number"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .button:hover {
            background-color: #2980b9;
        }
        
        .button.success {
            background-color: #27ae60;
        }
        
        .button.success:hover {
            background-color: #229954;
        }
        
        .button.warning {
            background-color: #f39c12;
        }
        
        .button.warning:hover {
            background-color: #e67e22;
        }
        
        .response {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .response.success {
            background-color: #d5edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .response.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
            align-items: end;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .token-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>易易京东服务测试</h1>
        
        <!-- 动态AccessToken测试 -->
        <div class="test-section">
            <h2>🔑 动态AccessToken测试</h2>
            <div class="token-info">
                <strong>说明：</strong>此功能允许您使用自定义的accessToken来调用京东API，而不使用配置文件中的默认token。
            </div>
            
            <div class="form-group">
                <label for="dynamicAccessToken">AccessToken:</label>
                <textarea id="dynamicAccessToken" placeholder="请输入您的京东AccessToken（长度通常较长）"></textarea>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="dynamicPageNo">页码:</label>
                    <input type="number" id="dynamicPageNo" value="1" min="1">
                </div>
                <div class="form-group">
                    <label for="dynamicPageSize">每页数量:</label>
                    <input type="number" id="dynamicPageSize" value="5" min="1" max="30">
                </div>
                <div class="form-group">
                    <button class="button" onclick="validateToken()">验证Token</button>
                    <button class="button success" onclick="testWithDynamicToken()">测试商品接口</button>
                </div>
            </div>
            
            <div id="dynamicTokenResponse" class="response" style="display: none;"></div>
        </div>
        
        <!-- 默认AccessToken测试 -->
        <div class="test-section">
            <h2>⚙️ 默认AccessToken测试</h2>
            <div class="token-info">
                <strong>说明：</strong>此功能使用application.yml配置文件中的默认accessToken。
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="defaultPageNo">页码:</label>
                    <input type="number" id="defaultPageNo" value="1" min="1">
                </div>
                <div class="form-group">
                    <label for="defaultPageSize">每页数量:</label>
                    <input type="number" id="defaultPageSize" value="10" min="1" max="30">
                </div>
                <div class="form-group">
                    <button class="button warning" onclick="testWithDefaultToken()">测试默认Token</button>
                    <button class="button warning" onclick="testDefaultAll()">测试所有默认参数</button>
                </div>
            </div>
            
            <div id="defaultTokenResponse" class="response" style="display: none;"></div>
        </div>
        
        <!-- 接口说明 -->
        <div class="test-section">
            <h2>📖 接口说明</h2>
            <ul>
                <li><strong>动态AccessToken接口：</strong> <code>POST /api/test/yiyi-jd/products-with-token</code></li>
                <li><strong>默认AccessToken接口：</strong> <code>POST /api/test/yiyi-jd/products</code></li>
                <li><strong>全默认参数接口：</strong> <code>GET /api/test/yiyi-jd/products-default</code></li>
                <li><strong>Token验证接口：</strong> <code>POST /api/test/yiyi-jd/validate-token</code></li>
            </ul>
        </div>
    </div>

    <script>
        function showResponse(elementId, data, isSuccess) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'response ' + (isSuccess ? 'success' : 'error');
            element.textContent = JSON.stringify(data, null, 2);
        }

        function validateToken() {
            const accessToken = document.getElementById('dynamicAccessToken').value.trim();
            
            if (!accessToken) {
                showResponse('dynamicTokenResponse', {error: '请先输入AccessToken'}, false);
                return;
            }

            fetch('/api/test/yiyi-jd/validate-token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    accessToken: accessToken
                })
            })
            .then(response => response.json())
            .then(data => {
                showResponse('dynamicTokenResponse', data, data.code === 0);
            })
            .catch(error => {
                showResponse('dynamicTokenResponse', {error: error.message}, false);
            });
        }

        function testWithDynamicToken() {
            const accessToken = document.getElementById('dynamicAccessToken').value.trim();
            const pageNo = parseInt(document.getElementById('dynamicPageNo').value) || 1;
            const pageSize = parseInt(document.getElementById('dynamicPageSize').value) || 5;
            
            if (!accessToken) {
                showResponse('dynamicTokenResponse', {error: '请先输入AccessToken'}, false);
                return;
            }

            fetch('/api/test/yiyi-jd/products-with-token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    accessToken: accessToken,
                    pageNo: pageNo,
                    pageSize: pageSize
                })
            })
            .then(response => response.json())
            .then(data => {
                showResponse('dynamicTokenResponse', data, data.code === 0);
            })
            .catch(error => {
                showResponse('dynamicTokenResponse', {error: error.message}, false);
            });
        }

        function testWithDefaultToken() {
            const pageNo = parseInt(document.getElementById('defaultPageNo').value) || 1;
            const pageSize = parseInt(document.getElementById('defaultPageSize').value) || 10;

            fetch('/api/test/yiyi-jd/products', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    pageNo: pageNo,
                    pageSize: pageSize
                })
            })
            .then(response => response.json())
            .then(data => {
                showResponse('defaultTokenResponse', data, data.code === 0);
            })
            .catch(error => {
                showResponse('defaultTokenResponse', {error: error.message}, false);
            });
        }

        function testDefaultAll() {
            fetch('/api/test/yiyi-jd/products-default', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                showResponse('defaultTokenResponse', data, data.code === 0);
            })
            .catch(error => {
                showResponse('defaultTokenResponse', {error: error.message}, false);
            });
        }
        
        // 页面加载时自动填入一个示例token（您可以根据需要修改）
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('dynamicAccessToken').value = '89fd9dcc03d34c6d997fc66e019700bcy2mw';
        });
    </script>
</body>
</html> 