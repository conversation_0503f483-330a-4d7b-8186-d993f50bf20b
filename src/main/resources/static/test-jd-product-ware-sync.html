<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>京东商品Ware同步测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        input[type="text"], input[type="number"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .success {
            background-color: #27ae60;
        }
        .success:hover {
            background-color: #229954;
        }
        .warning {
            background-color: #f39c12;
        }
        .warning:hover {
            background-color: #e67e22;
        }
        .danger {
            background-color: #e74c3c;
        }
        .danger:hover {
            background-color: #c0392b;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .result.success {
            background-color: #d5f4e6;
            border: 1px solid #27ae60;
            color: #27ae60;
        }
        .result.error {
            background-color: #fadbd8;
            border: 1px solid #e74c3c;
            color: #e74c3c;
        }
        .result.info {
            background-color: #d6eaf8;
            border: 1px solid #3498db;
            color: #2980b9;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-error { background-color: #e74c3c; }
        .status-info { background-color: #3498db; }
    </style>
</head>
<body>
    <div class="container">
        <h1>京东商品Ware同步测试页面</h1>
        
        <div class="grid">
            <!-- 左侧：Ware列表测试 -->
            <div>
                <h2>1. 获取Ware列表测试</h2>
                <div class="form-group">
                    <label for="wareAccessToken">访问令牌：</label>
                    <input type="text" id="wareAccessToken" placeholder="请输入京东访问令牌" value="测试令牌">
                </div>
                <div class="form-group">
                    <label for="warePageNo">页码：</label>
                    <input type="number" id="warePageNo" value="1" min="1">
                </div>
                <div class="form-group">
                    <label for="warePageSize">每页数量：</label>
                    <input type="number" id="warePageSize" value="5" min="1" max="30">
                </div>
                <button onclick="testGetWareList()">获取Ware列表</button>
                <div id="wareListResult" class="result" style="display: none;"></div>
            </div>

            <!-- 右侧：商品同步测试 -->
            <div>
                <h2>2. 商品同步测试</h2>
                <div class="form-group">
                    <label for="syncAccessToken">访问令牌：</label>
                    <input type="text" id="syncAccessToken" placeholder="请输入京东访问令牌" value="测试令牌">
                </div>
                <div class="form-group">
                    <label for="syncTeamId">团队ID：</label>
                    <input type="number" id="syncTeamId" value="1" min="1">
                </div>
                <div class="form-group">
                    <label for="syncCreator">创建者：</label>
                    <input type="text" id="syncCreator" value="testUser" placeholder="创建者名称">
                </div>
                <button onclick="startSync()" class="success">启动同步</button>
                <button onclick="checkSyncStatus()" class="warning">检查状态</button>
                <div id="syncResult" class="result" style="display: none;"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>3. 数据结构分析</h2>
        <button onclick="analyzeWareStructure()" class="info">分析Ware数据结构</button>
        <button onclick="testWareFeatures()" class="info">测试Features字段</button>
        <button onclick="testWareMultiCateProps()" class="info">测试MultiCateProps字段</button>
        <div id="analysisResult" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>4. 错误测试</h2>
        <button onclick="testInvalidToken()" class="danger">测试无效令牌</button>
        <button onclick="testEmptyToken()" class="danger">测试空令牌</button>
        <button onclick="testDuplicateSync()" class="danger">测试重复同步</button>
        <div id="errorTestResult" class="result" style="display: none;"></div>
    </div>

    <script>
        // API 基础路径
        const API_BASE = '';

        // 显示结果的通用函数
        function showResult(elementId, data, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            
            if (typeof data === 'object') {
                element.textContent = JSON.stringify(data, null, 2);
            } else {
                element.textContent = data;
            }
        }

        // 1. 获取Ware列表
        async function testGetWareList() {
            const accessToken = document.getElementById('wareAccessToken').value;
            const pageNo = document.getElementById('warePageNo').value;
            const pageSize = document.getElementById('warePageSize').value;

            if (!accessToken.trim()) {
                showResult('wareListResult', '错误：请输入访问令牌', 'error');
                return;
            }

            try {
                showResult('wareListResult', '正在获取Ware列表...', 'info');
                
                const response = await fetch(`${API_BASE}/api/yiyi-jd/ware-list?accessToken=${encodeURIComponent(accessToken)}&pageNo=${pageNo}&pageSize=${pageSize}`);
                const data = await response.json();
                
                if (response.ok) {
                    let resultText = `获取成功！\n数量: ${data.length}\n\n`;
                    if (data.length > 0) {
                        resultText += `第一个Ware对象结构:\n${JSON.stringify(data[0], null, 2)}`;
                    }
                    showResult('wareListResult', resultText, 'success');
                } else {
                    showResult('wareListResult', `请求失败: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('wareListResult', `网络错误: ${error.message}`, 'error');
            }
        }

        // 2. 启动同步
        async function startSync() {
            const accessToken = document.getElementById('syncAccessToken').value;
            const teamId = document.getElementById('syncTeamId').value;
            const creator = document.getElementById('syncCreator').value;

            if (!accessToken.trim() || !teamId || !creator.trim()) {
                showResult('syncResult', '错误：请填写所有必需字段', 'error');
                return;
            }

            try {
                showResult('syncResult', '正在启动同步...', 'info');
                
                const response = await fetch(`${API_BASE}/api/jd-product-sync/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        accessToken: accessToken,
                        teamId: parseInt(teamId),
                        creator: creator
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    showResult('syncResult', `同步启动成功！\n团队ID: ${teamId}\n创建者: ${creator}\n状态: ${data.message}`, 'success');
                    // 自动检查状态
                    setTimeout(() => checkSyncStatus(), 2000);
                } else {
                    showResult('syncResult', `同步启动失败: ${data.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult('syncResult', `网络错误: ${error.message}`, 'error');
            }
        }

        // 3. 检查同步状态
        async function checkSyncStatus() {
            const teamId = document.getElementById('syncTeamId').value;

            if (!teamId) {
                showResult('syncResult', '错误：请输入团队ID', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/jd-product-sync/status/${teamId}`);
                const data = await response.json();
                
                if (response.ok) {
                    let statusType = 'info';
                    if (data.message.includes('完成')) {
                        statusType = 'success';
                    } else if (data.message.includes('失败') || data.message.includes('错误')) {
                        statusType = 'error';
                    } else if (data.message.includes('进行中')) {
                        statusType = 'warning';
                    }
                    
                    showResult('syncResult', `同步状态: ${data.message}\n团队ID: ${teamId}\n检查时间: ${new Date().toLocaleString()}`, statusType);
                } else {
                    showResult('syncResult', `获取状态失败: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('syncResult', `网络错误: ${error.message}`, 'error');
            }
        }

        // 4. 分析Ware数据结构
        async function analyzeWareStructure() {
            const accessToken = document.getElementById('wareAccessToken').value || "测试令牌";
            
            try {
                showResult('analysisResult', '正在分析Ware数据结构...', 'info');
                
                const response = await fetch(`${API_BASE}/api/yiyi-jd/ware-list?accessToken=${encodeURIComponent(accessToken)}&pageNo=1&pageSize=3`);
                const data = await response.json();
                
                if (response.ok && data.length > 0) {
                    const ware = data[0];
                    let analysis = `Ware数据结构分析:\n\n`;
                    analysis += `基本字段:\n`;
                    analysis += `- wareId: ${ware.wareId} (${typeof ware.wareId})\n`;
                    analysis += `- title: ${ware.title} (${typeof ware.title})\n`;
                    analysis += `- brandName: ${ware.brandName} (${typeof ware.brandName})\n`;
                    analysis += `- categoryId: ${ware.categoryId} (${typeof ware.categoryId})\n\n`;
                    
                    analysis += `特殊字段:\n`;
                    analysis += `- features: ${ware.features ? `存在 (${typeof ware.features})` : '不存在'}\n`;
                    if (ware.features) {
                        analysis += `  类型: ${Array.isArray(ware.features) ? 'Array' : ware.features.constructor.name}\n`;
                        analysis += `  内容: ${JSON.stringify(ware.features)}\n`;
                    }
                    
                    analysis += `- multiCateProps: ${ware.multiCateProps ? `存在 (${typeof ware.multiCateProps})` : '不存在'}\n`;
                    if (ware.multiCateProps) {
                        analysis += `  类型: ${Array.isArray(ware.multiCateProps) ? 'Array' : ware.multiCateProps.constructor.name}\n`;
                        analysis += `  内容: ${JSON.stringify(ware.multiCateProps)}\n`;
                    }
                    
                    showResult('analysisResult', analysis, 'success');
                } else {
                    showResult('analysisResult', '分析失败：无法获取Ware数据', 'error');
                }
            } catch (error) {
                showResult('analysisResult', `分析错误: ${error.message}`, 'error');
            }
        }

        // 5. 测试Features字段
        async function testWareFeatures() {
            const accessToken = document.getElementById('wareAccessToken').value || "测试令牌";
            
            try {
                showResult('analysisResult', '正在测试Features字段...', 'info');
                
                const response = await fetch(`${API_BASE}/api/yiyi-jd/ware-list?accessToken=${encodeURIComponent(accessToken)}&pageNo=1&pageSize=5`);
                const data = await response.json();
                
                if (response.ok) {
                    let analysis = `Features字段测试结果:\n\n`;
                    let featuresCount = 0;
                    
                    data.forEach((ware, index) => {
                        if (ware.features) {
                            featuresCount++;
                            analysis += `商品${index + 1} (wareId: ${ware.wareId}):\n`;
                            analysis += `  Features类型: ${Array.isArray(ware.features) ? 'Array' : typeof ware.features}\n`;
                            analysis += `  Features内容: ${JSON.stringify(ware.features, null, 2)}\n\n`;
                        }
                    });
                    
                    analysis += `总结: ${data.length}个商品中有${featuresCount}个包含features字段`;
                    showResult('analysisResult', analysis, 'success');
                } else {
                    showResult('analysisResult', '测试失败：无法获取Ware数据', 'error');
                }
            } catch (error) {
                showResult('analysisResult', `测试错误: ${error.message}`, 'error');
            }
        }

        // 6. 测试MultiCateProps字段
        async function testWareMultiCateProps() {
            const accessToken = document.getElementById('wareAccessToken').value || "测试令牌";
            
            try {
                showResult('analysisResult', '正在测试MultiCateProps字段...', 'info');
                
                const response = await fetch(`${API_BASE}/api/yiyi-jd/ware-list?accessToken=${encodeURIComponent(accessToken)}&pageNo=1&pageSize=5`);
                const data = await response.json();
                
                if (response.ok) {
                    let analysis = `MultiCateProps字段测试结果:\n\n`;
                    let mcpCount = 0;
                    
                    data.forEach((ware, index) => {
                        if (ware.multiCateProps) {
                            mcpCount++;
                            analysis += `商品${index + 1} (wareId: ${ware.wareId}):\n`;
                            analysis += `  MultiCateProps类型: ${Array.isArray(ware.multiCateProps) ? 'Array' : typeof ware.multiCateProps}\n`;
                            analysis += `  MultiCateProps内容: ${JSON.stringify(ware.multiCateProps, null, 2)}\n\n`;
                        }
                    });
                    
                    analysis += `总结: ${data.length}个商品中有${mcpCount}个包含multiCateProps字段`;
                    showResult('analysisResult', analysis, 'success');
                } else {
                    showResult('analysisResult', '测试失败：无法获取Ware数据', 'error');
                }
            } catch (error) {
                showResult('analysisResult', `测试错误: ${error.message}`, 'error');
            }
        }

        // 7. 测试无效令牌
        async function testInvalidToken() {
            try {
                showResult('errorTestResult', '正在测试无效令牌...', 'info');
                
                const response = await fetch(`${API_BASE}/api/yiyi-jd/ware-list?accessToken=invalid_token_12345&pageNo=1&pageSize=3`);
                const data = await response.json();
                
                let result = `无效令牌测试结果:\n`;
                result += `状态码: ${response.status}\n`;
                result += `返回数据: ${JSON.stringify(data, null, 2)}\n`;
                result += `预期行为: 应返回空列表或错误信息`;
                
                showResult('errorTestResult', result, response.ok ? 'warning' : 'success');
            } catch (error) {
                showResult('errorTestResult', `无效令牌测试错误: ${error.message}`, 'error');
            }
        }

        // 8. 测试空令牌
        async function testEmptyToken() {
            try {
                showResult('errorTestResult', '正在测试空令牌...', 'info');
                
                const response = await fetch(`${API_BASE}/api/yiyi-jd/ware-list?accessToken=&pageNo=1&pageSize=3`);
                const data = await response.json();
                
                let result = `空令牌测试结果:\n`;
                result += `状态码: ${response.status}\n`;
                result += `返回数据: ${JSON.stringify(data, null, 2)}\n`;
                result += `预期行为: 应返回空列表或错误信息`;
                
                showResult('errorTestResult', result, response.ok ? 'warning' : 'success');
            } catch (error) {
                showResult('errorTestResult', `空令牌测试错误: ${error.message}`, 'error');
            }
        }

        // 9. 测试重复同步
        async function testDuplicateSync() {
            const teamId = 999; // 使用固定的测试团队ID
            
            try {
                showResult('errorTestResult', '正在测试重复同步...', 'info');
                
                // 第一次启动同步
                const firstResponse = await fetch(`${API_BASE}/api/jd-product-sync/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        accessToken: "测试令牌",
                        teamId: teamId,
                        creator: "testUser"
                    })
                });
                const firstData = await firstResponse.json();
                
                // 立即第二次启动同步
                const secondResponse = await fetch(`${API_BASE}/api/jd-product-sync/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        accessToken: "测试令牌",
                        teamId: teamId,
                        creator: "testUser"
                    })
                });
                const secondData = await secondResponse.json();
                
                let result = `重复同步测试结果:\n`;
                result += `第一次同步: ${firstData.success ? '成功' : '失败'} - ${firstData.message}\n`;
                result += `第二次同步: ${secondData.success ? '成功' : '失败'} - ${secondData.message}\n`;
                result += `预期行为: 第一次成功，第二次失败（任务已在进行中）`;
                
                const success = firstData.success && !secondData.success;
                showResult('errorTestResult', result, success ? 'success' : 'warning');
                
            } catch (error) {
                showResult('errorTestResult', `重复同步测试错误: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('京东商品Ware同步测试页面已加载');
        });
    </script>
</body>
</html> 