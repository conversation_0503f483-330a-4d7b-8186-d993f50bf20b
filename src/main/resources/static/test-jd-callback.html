<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>京东回调服务测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #666;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
        }
        .test-section {
            margin-bottom: 30px;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-button.success {
            background-color: #28a745;
        }
        .test-button.success:hover {
            background-color: #1e7e34;
        }
        .test-button.danger {
            background-color: #dc3545;
        }
        .test-button.danger:hover {
            background-color: #c82333;
        }
        .result-container {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            min-height: 50px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .info-box {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            color: #0c5460;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .status-indicator {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>京东回调服务测试</h1>
        
        <div class="info-box">
            <h3>📋 服务重构说明</h3>
            <p>本页面用于测试重构后的京东回调服务。我们已经将原来Controller中的业务逻辑封装到JdCallbackService中，遵循了分层架构的原则。</p>
            <p><strong>重构内容：</strong></p>
            <ul>
                <li>✅ 创建了JdCallbackService接口和实现类</li>
                <li>✅ 将业务逻辑从Controller移到Service层</li>
                <li>✅ Controller只负责HTTP请求处理</li>
                <li>✅ 创建了完整的单元测试</li>
                <li>✅ 所有测试都通过验证</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🔄 同步状态查询测试</h2>
            <p>测试获取京东商品同步状态的接口。</p>
            
            <div class="form-group">
                <label for="teamId">团队ID：</label>
                <input type="number" id="teamId" value="1" placeholder="请输入团队ID">
            </div>
            
            <button class="test-button" onclick="getSyncStatus()">获取同步状态</button>
            <button class="test-button success" onclick="getSyncStatus(999)">测试不存在的团队</button>
            <button class="test-button danger" onclick="getSyncStatus(null)">测试空团队ID</button>
            
            <div id="syncStatusResult" class="result-container"></div>
        </div>

        <div class="test-section">
            <h2>📊 服务架构信息</h2>
            <p>显示重构后的服务架构层次：</p>
            <div style="background-color: white; padding: 15px; border-radius: 4px; border: 1px solid #ddd;">
                <p><strong>📱 Controller层</strong> → JdCallbackController</p>
                <p style="margin-left: 20px;">- 处理HTTP请求</p>
                <p style="margin-left: 20px;">- 参数验证和转换</p>
                <p style="margin-left: 20px;">- 返回HTTP响应</p>
                <p style="margin-left: 20px;">↓</p>
                <p><strong>🔧 Service层</strong> → JdCallbackService</p>
                <p style="margin-left: 20px;">- 处理业务逻辑</p>
                <p style="margin-left: 20px;">- 调用京东API</p>
                <p style="margin-left: 20px;">- 数据验证和转换</p>
                <p style="margin-left: 20px;">↓</p>
                <p><strong>💾 Mapper层</strong> → TrainJdAccessTokenMapper</p>
                <p style="margin-left: 20px;">- 数据库访问</p>
                <p style="margin-left: 20px;">- 数据持久化</p>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 单元测试结果</h2>
            <p>JdCallbackServiceTest测试结果：</p>
            <div style="background-color: white; padding: 15px; border-radius: 4px; border: 1px solid #ddd;">
                <p>✅ testHandleCallback_Success <span class="status-indicator status-success">通过</span></p>
                <p>✅ testHandleCallback_InvalidState <span class="status-indicator status-success">通过</span></p>
                <p>✅ testHandleCallback_EmptyApiResponse <span class="status-indicator status-success">通过</span></p>
                <p>✅ testHandleCallback_MissingRequiredFields <span class="status-indicator status-success">通过</span></p>
                <p>✅ testHandleCallback_UpdateExistingToken <span class="status-indicator status-success">通过</span></p>
                <p>✅ testHandleCallback_SyncFailureNotAffectMainFlow <span class="status-indicator status-success">通过</span></p>
                <p><strong>测试结果：Tests run: 6, Failures: 0, Errors: 0, Skipped: 0</strong></p>
            </div>
        </div>
    </div>

    <script>
        // 获取同步状态
        async function getSyncStatus(teamId) {
            const resultDiv = document.getElementById('syncStatusResult');
            resultDiv.textContent = '正在查询...';
            
            try {
                // 如果没有传入teamId，从输入框获取
                if (teamId === undefined) {
                    teamId = document.getElementById('teamId').value;
                }
                
                let url = '/api/jd/sync-status';
                if (teamId !== null) {
                    url += `?teamId=${teamId}`;
                }
                
                const response = await fetch(url);
                const data = await response.json();
                
                resultDiv.textContent = `响应状态: ${response.status}\n响应数据: ${JSON.stringify(data, null, 2)}`;
                
            } catch (error) {
                resultDiv.textContent = `请求失败: ${error.message}`;
            }
        }
        
        // 页面加载完成后自动获取一次状态
        window.addEventListener('load', function() {
            setTimeout(() => {
                getSyncStatus();
            }, 500);
        });
    </script>
</body>
</html> 