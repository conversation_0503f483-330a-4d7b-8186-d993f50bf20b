<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>京东商品同步测试 - JSON字段版</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .result {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .param-table th, .param-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .param-table th {
            background-color: #f2f2f2;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .json-display {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>京东商品同步测试 - JSON字段版本</h1>
    
    <div class="container">
        <h2>功能说明</h2>
        <div class="note">
            <p><strong>新功能特性：</strong></p>
            <ul>
                <li>商品表新增 <code>features</code> 和 <code>multi_cate_props</code> 两个JSON字段</li>
                <li>将京东API返回的复杂对象转换为JSON字符串存储</li>
                <li>移除了对相关表的依赖，简化了数据结构</li>
                <li>提供更灵活的数据查询和处理方式</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>同步参数配置</h2>
        <div class="form-group">
            <label for="accessToken">访问令牌 (Access Token):</label>
            <input type="text" id="accessToken" placeholder="请输入京东API访问令牌">
        </div>
        <div class="form-group">
            <label for="teamId">团队ID:</label>
            <input type="number" id="teamId" value="1" placeholder="请输入团队ID">
        </div>
        <div class="form-group">
            <label for="creator">创建人:</label>
            <input type="text" id="creator" value="testUser" placeholder="请输入创建人">
        </div>
        <div class="form-group">
            <button onclick="startSync()">开始同步</button>
            <button onclick="checkStatus()">检查状态</button>
            <button onclick="clearResults()">清空结果</button>
        </div>
    </div>

    <div class="container">
        <h2>同步状态</h2>
        <div id="statusResult" class="result">等待开始同步...</div>
    </div>

    <div class="container">
        <h2>同步结果</h2>
        <div id="syncResult" class="result">尚未开始同步</div>
    </div>

    <div class="container">
        <h2>数据结构说明</h2>
        <table class="param-table">
            <tr>
                <th>字段名</th>
                <th>类型</th>
                <th>说明</th>
                <th>示例</th>
            </tr>
            <tr>
                <td>features</td>
                <td>TEXT (JSON)</td>
                <td>商品功能特性JSON数据</td>
                <td>[{"featureKey":"key1","featureValue":["value1"],"featureCn":"特色服务"}]</td>
            </tr>
            <tr>
                <td>multi_cate_props</td>
                <td>TEXT (JSON)</td>
                <td>商品多类目属性JSON数据</td>
                <td>[{"attrId":"233846","attrValues":["1335194"],"attrValueAlias":["QC协议"]}]</td>
            </tr>
        </table>
    </div>

    <div class="container">
        <h2>JSON格式示例</h2>
        <h3>Features字段示例：</h3>
        <div class="json-display">[
  {
    "featureKey": "feature1",
    "featureValue": ["value1", "value2"],
    "featureCn": "特色服务1"
  },
  {
    "featureKey": "feature2", 
    "featureValue": ["value3"],
    "featureCn": "特色服务2"
  }
]</div>

        <h3>MultiCateProps字段示例：</h3>
        <div class="json-display">[
  {
    "attrId": "233846",
    "attrValues": ["1335194"],
    "attrValueAlias": ["QC协议"]
  },
  {
    "attrId": "233847",
    "attrValues": ["1335202"], 
    "attrValueAlias": ["氮化镓"],
    "expands": null,
    "units": null
  }
]</div>
    </div>

    <div class="container">
        <h2>测试功能</h2>
        <div class="form-group">
            <button onclick="testJsonParsing()">测试JSON解析</button>
            <button onclick="validateDataStructure()">验证数据结构</button>
            <button onclick="simulateSync()">模拟同步流程</button>
        </div>
        <div id="testResult" class="result">测试结果将显示在这里...</div>
    </div>

    <script>
        // 全局变量
        let syncInProgress = false;
        let statusCheckInterval = null;

        // 开始同步
        async function startSync() {
            const accessToken = document.getElementById('accessToken').value.trim();
            const teamId = document.getElementById('teamId').value.trim();
            const creator = document.getElementById('creator').value.trim();

            if (!accessToken) {
                showResult('syncResult', '请输入访问令牌', 'error');
                return;
            }

            if (!teamId) {
                showResult('syncResult', '请输入团队ID', 'error');
                return;
            }

            if (!creator) {
                showResult('syncResult', '请输入创建人', 'error');
                return;
            }

            if (syncInProgress) {
                showResult('syncResult', '同步正在进行中，请等待...', 'info');
                return;
            }

            try {
                syncInProgress = true;
                showResult('syncResult', '正在启动同步...', 'info');

                const response = await fetch('/api/jd/sync/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        accessToken: accessToken,
                        teamId: parseInt(teamId),
                        creator: creator
                    })
                });

                const result = await response.text();
                
                if (response.ok) {
                    if (result === 'true') {
                        showResult('syncResult', '同步已成功启动！正在后台处理...', 'success');
                        startStatusCheck();
                    } else {
                        showResult('syncResult', '同步启动失败：' + result, 'error');
                        syncInProgress = false;
                    }
                } else {
                    showResult('syncResult', '请求失败：' + result, 'error');
                    syncInProgress = false;
                }
            } catch (error) {
                showResult('syncResult', '网络错误：' + error.message, 'error');
                syncInProgress = false;
            }
        }

        // 检查同步状态
        async function checkStatus() {
            const teamId = document.getElementById('teamId').value.trim();
            
            if (!teamId) {
                showResult('statusResult', '请输入团队ID', 'error');
                return;
            }

            try {
                const response = await fetch(`/api/jd/sync/status?teamId=${teamId}`);
                const status = await response.text();
                
                if (response.ok) {
                    showResult('statusResult', `同步状态：${status}`, 'info');
                    
                    // 如果同步完成或失败，停止状态检查
                    if (status.includes('完成') || status.includes('失败')) {
                        syncInProgress = false;
                        stopStatusCheck();
                    }
                } else {
                    showResult('statusResult', '状态查询失败：' + status, 'error');
                }
            } catch (error) {
                showResult('statusResult', '网络错误：' + error.message, 'error');
            }
        }

        // 开始状态检查
        function startStatusCheck() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }
            
            statusCheckInterval = setInterval(() => {
                checkStatus();
            }, 3000); // 每3秒检查一次
        }

        // 停止状态检查
        function stopStatusCheck() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
                statusCheckInterval = null;
            }
        }

        // 清空结果
        function clearResults() {
            document.getElementById('syncResult').innerHTML = '尚未开始同步';
            document.getElementById('statusResult').innerHTML = '等待开始同步...';
            document.getElementById('testResult').innerHTML = '测试结果将显示在这里...';
            
            // 重置状态
            syncInProgress = false;
            stopStatusCheck();
        }

        // 测试JSON解析
        function testJsonParsing() {
            try {
                // 测试features JSON解析
                const featuresJson = `[
                    {
                        "featureKey": "feature1",
                        "featureValue": ["value1", "value2"],
                        "featureCn": "特色服务1"
                    }
                ]`;
                
                const featuresData = JSON.parse(featuresJson);
                
                // 测试multiCateProps JSON解析
                const mcpJson = `[
                    {
                        "attrId": "233846",
                        "attrValues": ["1335194"],
                        "attrValueAlias": ["QC协议"]
                    }
                ]`;
                
                const mcpData = JSON.parse(mcpJson);
                
                const result = `JSON解析测试成功！
                
Features数据：
${JSON.stringify(featuresData, null, 2)}

MultiCateProps数据：
${JSON.stringify(mcpData, null, 2)}`;
                
                showResult('testResult', result, 'success');
            } catch (error) {
                showResult('testResult', 'JSON解析测试失败：' + error.message, 'error');
            }
        }

        // 验证数据结构
        function validateDataStructure() {
            const validationResults = [];
            
            // 验证features结构
            const featuresStructure = {
                required: ['featureKey', 'featureValue', 'featureCn'],
                optional: []
            };
            
            // 验证multiCateProps结构
            const mcpStructure = {
                required: ['attrId', 'attrValues'],
                optional: ['attrValueAlias', 'expands', 'units']
            };
            
            validationResults.push('✓ Features字段结构验证：');
            validationResults.push(`  必需字段：${featuresStructure.required.join(', ')}`);
            validationResults.push(`  可选字段：${featuresStructure.optional.join(', ') || '无'}`);
            
            validationResults.push('\n✓ MultiCateProps字段结构验证：');
            validationResults.push(`  必需字段：${mcpStructure.required.join(', ')}`);
            validationResults.push(`  可选字段：${mcpStructure.optional.join(', ')}`);
            
            validationResults.push('\n✓ JSON存储优势：');
            validationResults.push('  - 减少数据库表数量');
            validationResults.push('  - 提高查询灵活性');
            validationResults.push('  - 简化数据同步逻辑');
            validationResults.push('  - 支持动态字段结构');
            
            showResult('testResult', validationResults.join('\n'), 'success');
        }

        // 模拟同步流程
        function simulateSync() {
            const steps = [
                '1. 调用京东API获取商品列表',
                '2. 解析Ware对象中的features和multiCateProps',
                '3. 将复杂对象转换为JSON字符串',
                '4. 更新train_jd_products表中的JSON字段',
                '5. 跳过相关表的同步（已移除）',
                '6. 继续SKU数据同步',
                '7. 完成同步流程'
            ];
            
            let currentStep = 0;
            const stepInterval = setInterval(() => {
                if (currentStep < steps.length) {
                    const progress = steps.slice(0, currentStep + 1).join('\n');
                    showResult('testResult', `模拟同步流程：\n\n${progress}`, 'info');
                    currentStep++;
                } else {
                    clearInterval(stepInterval);
                    showResult('testResult', `模拟同步流程：\n\n${steps.join('\n')}\n\n✓ 模拟同步完成！`, 'success');
                }
            }, 1000);
        }

        // 显示结果
        function showResult(elementId, message, type = '') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'result';
            
            if (type) {
                element.classList.add(type);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('京东商品同步测试页面已加载 - JSON字段版本');
        });
    </script>
</body>
</html> 