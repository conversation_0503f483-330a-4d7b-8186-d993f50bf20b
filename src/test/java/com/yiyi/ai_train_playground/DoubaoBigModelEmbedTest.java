package com.yiyi.ai_train_playground;

import com.yiyi.ai_train_playground.service.SuperBigModelInterface;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.Arrays;
import java.util.List;

/**
 * 豆包大模型向量化功能单元测试
 * 注意：此测试需要配置有效的ARK_API_KEY环境变量
 */
@SpringBootTest
@TestPropertySource(properties = {
        "my.doubao.embed.model-name=doubao-embedding-text-240715",
        "my.doubao.connection-pool.max-requests=50",
        "my.doubao.connection-pool.max-requests-per-host=25"
})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class DoubaoBigModelEmbedTest {

    @Autowired
    private SuperBigModelInterface bigModelService;

    @BeforeEach
    public void setUp() {
        System.out.println("豆包大模型向量化测试初始化");
    }

    @Test
    @Order(1)
    public void testEmbedSingleText() {
        System.out.println("=== 测试单个文本向量化 ===");
        
        List<String> texts = Arrays.asList("花椰菜又称菜花、花菜，是一种常见的蔬菜。");
        
        List<List<Double>> embeddings = bigModelService.embed(texts);
        
        Assertions.assertNotNull(embeddings, "向量化结果不应为null");
        Assertions.assertEquals(1, embeddings.size(), "应该返回1个向量");
        Assertions.assertNotNull(embeddings.get(0), "向量不应为null");
        Assertions.assertTrue(embeddings.get(0).size() > 0, "向量维度应大于0");
        
        System.out.println("向量维度: " + embeddings.get(0).size());
        System.out.println("向量前5个维度: " + embeddings.get(0).subList(0, Math.min(5, embeddings.get(0).size())));
        System.out.println("单个文本向量化测试通过");
    }

    @Test
    @Order(2)
    public void testEmbedMultipleTexts() {
        System.out.println("=== 测试多个文本向量化 ===");
        
        List<String> texts = Arrays.asList(
                "花椰菜又称菜花、花菜，是一种常见的蔬菜。",
                "苹果是一种营养丰富的水果。",
                "人工智能是计算机科学的一个分支。",
                "Spring Boot是一个Java开发框架。"
        );
        
        List<List<Double>> embeddings = bigModelService.embed(texts);
        
        Assertions.assertNotNull(embeddings, "向量化结果不应为null");
        Assertions.assertEquals(4, embeddings.size(), "应该返回4个向量");
        
        // 检查每个向量
        for (int i = 0; i < embeddings.size(); i++) {
            List<Double> embedding = embeddings.get(i);
            Assertions.assertNotNull(embedding, "第" + (i+1) + "个向量不应为null");
            Assertions.assertTrue(embedding.size() > 0, "第" + (i+1) + "个向量维度应大于0");
            
            // 检查向量维度一致性
            if (i > 0) {
                Assertions.assertEquals(embeddings.get(0).size(), embedding.size(), 
                        "所有向量的维度应该相同");
            }
        }
        
        System.out.println("向量数量: " + embeddings.size());
        System.out.println("向量维度: " + embeddings.get(0).size());
        System.out.println("多个文本向量化测试通过");
    }

    @Test
    @Order(3)
    public void testEmbedEmptyList() {
        System.out.println("=== 测试空列表向量化 ===");
        
        List<String> texts = Arrays.asList();
        
        try {
            List<List<Double>> embeddings = bigModelService.embed(texts);
            Assertions.assertNotNull(embeddings, "向量化结果不应为null");
            Assertions.assertEquals(0, embeddings.size(), "空列表应该返回空的向量列表");
            System.out.println("空列表向量化测试通过");
        } catch (RuntimeException e) {
            // API可能不接受空列表，这种情况也是合理的
            System.out.println("空列表向量化抛出异常（符合预期）: " + e.getMessage());
            Assertions.assertTrue(e.getMessage().contains("豆包"), "异常信息应包含豆包相关信息");
        }
    }

    @Test
    @Order(4)
    public void testEmbedChineseAndEnglishTexts() {
        System.out.println("=== 测试中英文混合文本向量化 ===");
        
        List<String> texts = Arrays.asList(
                "这是一段中文文本",
                "This is an English text",
                "中英文混合 Mixed Chinese and English text",
                "数字123和符号!@#"
        );
        
        List<List<Double>> embeddings = bigModelService.embed(texts);
        
        Assertions.assertNotNull(embeddings, "向量化结果不应为null");
        Assertions.assertEquals(4, embeddings.size(), "应该返回4个向量");
        
        // 验证每个向量都有合理的值
        for (List<Double> embedding : embeddings) {
            Assertions.assertNotNull(embedding, "向量不应为null");
            Assertions.assertTrue(embedding.size() > 0, "向量维度应大于0");
            
            // 检查向量值是否在合理范围内（通常embedding值在-1到1之间）
            boolean hasValidValues = embedding.stream()
                    .anyMatch(val -> val != null && !val.isNaN() && !val.isInfinite());
            Assertions.assertTrue(hasValidValues, "向量应包含有效的数值");
        }
        
        System.out.println("中英文混合向量化测试通过");
    }

    @Test
    @Order(5)
    @Disabled("仅在需要进行性能测试时启用")
    public void testEmbedPerformance() {
        System.out.println("=== 向量化性能测试 ===");
        
        List<String> texts = Arrays.asList(
                "性能测试文本1",
                "性能测试文本2", 
                "性能测试文本3",
                "性能测试文本4",
                "性能测试文本5"
        );
        
        long startTime = System.currentTimeMillis();
        
        List<List<Double>> embeddings = bigModelService.embed(texts);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("向量化耗时: " + duration + "ms");
        System.out.println("平均每个文本耗时: " + (duration / texts.size()) + "ms");
        
        Assertions.assertNotNull(embeddings);
        Assertions.assertEquals(texts.size(), embeddings.size());
        
        // 性能应该在合理范围内（比如10秒内）
        Assertions.assertTrue(duration < 10000, "向量化耗时应在10秒内");
    }

    @AfterEach
    public void tearDown() {
        System.out.println("测试完成\n");
    }
} 