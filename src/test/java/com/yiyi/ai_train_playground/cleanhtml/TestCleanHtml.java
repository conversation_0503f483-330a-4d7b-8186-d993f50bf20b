package com.yiyi.ai_train_playground.cleanhtml;

import com.yiyi.ai_train_playground.service.impl.HtmlSanitizerService;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestCleanHtml {


    HtmlSanitizerService service = new HtmlSanitizerService();

    @Test
    public void testReplace() {
        String str = """
                
               <p><strong>品牌</strong>：禾果小镇</p><p><strong>城市</strong>：定西市</p><p><strong>上市时间</strong>：11月 10月 9月</p><p><strong>售卖方式</strong>：产地直销</p><p><strong>重量</strong>:2500g 4500g 3斤 1斤</p><p><strong>配送频次</strong>：1周1次</p><p><strong>套餐份量</strong>：5人份</p><p><strong>产地</strong>：中国大陆</p><p><strong>省份</strong>：甘肃省</p><p><strong>套餐周期</strong>：1周</p><p><strong>包装方式</strong>：包装</p><p><strong>特产品类</strong>：定西马铃薯</p><p><strong>单果规格</strong>：中果 大果 小果</p><p><strong>食用方式</strong>：<u>蒸煮食用</u></p><p><strong>厂名</strong>：禾果小镇</p><p><strong>厂址</strong>：甘肃省定西市临洮县</p><p><strong>厂家联系方式</strong>:15309331947</p><p><strong>保质期</strong>:11天</p>
                
                """;
        System.out.println("原始字符串为"+ str);
        str = service.htmlToPlainText(str);
        String newStr = str.replaceAll("[\\u0000-\\u001F]", "")
                .replaceAll("\\s+", " ");
        System.out.println("替换后的字符串为:"+newStr);
    }

    @Test
    public void testReplaceWithJsoupAndWasp() {
        // 输入HTML
        String inputHtml = """
                
                <p><strong>品牌</strong>：禾果小镇</p><p><strong>城市</strong>：定西市</p><p><strong>上市时间</strong>：11月 10月 9月</p><p><strong>售卖方式</strong>：产地直销</p><p><strong>重量</strong>:2500g 4500g 3斤 1斤</p><p><strong>配送频次</strong>：1周1次</p><p><strong>套餐份量</strong>：5人份</p><p><strong>产地</strong>：中国大陆</p><p><strong>省份</strong>：甘肃省</p><p><strong>套餐周期</strong>：1周</p><p><strong>包装方式</strong>：包装</p><p><strong>特产品类</strong>：定西马铃薯</p><p><strong>单果规格</strong>：中果 大果 小果</p><p><strong>食用方式</strong>：<u>蒸煮食用</u></p><p><strong>厂名</strong>：禾果小镇</p><p><strong>厂址</strong>：甘肃省定西市临洮县</p><p><strong>厂家联系方式</strong>:15309331947</p><p><strong>保质期</strong>:11天</p>
                
                """;


        inputHtml = """
                
                
                <div data-tab="item">
                                            <div class="module-title"> 商品详情 </div>
                                                                                                    <div class="goods-base">
                                                                                                                            <div class="item">
                            <div class="flex-center">
                                <div class="name">品牌</div>
                            </div>
                            <div class="adaptive">
                                <div class="text">
                                    <a href="//list.jd.com/list.html?cat=670,686,689&amp;tid=40161&amp;ev=exbrand_90095" clstag="undefinedshangpin|keycount|product|pinpai_1" target="_blank">阿米洛（Varmilo）</a>
                                </div>
                            </div>
                        </div>
                            <div class="item">
                        <div class="flex-center">
                            <div class="name">商品编号</div>
                        </div>
                        <div class="adaptive">
                            <div class="text">
                                10083506954652            </div>
                        </div>
                    </div>
                                        <div class="item">
                                <div class="flex-center">
                                    <div class="name">店铺</div>
                                </div>
                                <div class="adaptive">
                                    <div class="text">
                                        <a href="//amiluo.jd.com" target="_blank">阿米洛官方旗舰店</a>
                                    </div>
                                </div>
                            </div>
                                                                                    <div class="item">
                                        <div class="flex-center">
                                            <div class="name">兼容系统</div>
                                                                    </div>
                                        <div class="adaptive">
                                            <div class="text">
                                                Windows，MacOS                            </div>
                                        </div>
                                    </div>
                                                                    <div class="item">
                                        <div class="flex-center">
                                            <div class="name">背光灯效</div>
                                                                    </div>
                                        <div class="adaptive">
                                            <div class="text">
                                                单光                            </div>
                                        </div>
                                    </div>
                                                                    <div class="item">
                                        <div class="flex-center">
                                            <div class="name">插拔类型</div>
                                                                    </div>
                                        <div class="adaptive">
                                            <div class="text">
                                                不支持热插拔                            </div>
                                        </div>
                                    </div>
                                                                    <div class="item">
                                        <div class="flex-center">
                                            <div class="name">连接方式</div>
                                                                    </div>
                                        <div class="adaptive">
                                            <div class="text">
                                                有线                            </div>
                                        </div>
                                    </div>
                                                                    <div class="item">
                                        <div class="flex-center">
                                            <div class="name">类型</div>
                                                                    </div>
                                        <div class="adaptive">
                                            <div class="text">
                                                非客制化机械键盘                            </div>
                                        </div>
                                    </div>
                                                                    <div class="item">
                                        <div class="flex-center">
                                            <div class="name">按键数</div>
                                                                    </div>
                                        <div class="adaptive">
                                            <div class="text">
                                                61-70键                            </div>
                                        </div>
                                    </div>
                                                                    <div class="item">
                                        <div class="flex-center">
                                            <div class="name">同时连接设备</div>
                                                                    </div>
                                        <div class="adaptive">
                                            <div class="text">
                                                1台                            </div>
                                        </div>
                                    </div>
                                                                    <div class="item">
                                        <div class="flex-center">
                                            <div class="name">颜色</div>
                                                                    </div>
                                        <div class="adaptive">
                                            <div class="text">
                                                拼色                            </div>
                                        </div>
                                    </div>
                                                                    <div class="item">
                                        <div class="flex-center">
                                            <div class="name">型号</div>
                                                                    </div>
                                        <div class="adaptive">
                                            <div class="text">
                                                金属68键                            </div>
                                        </div>
                                    </div>
                                                                    <div class="item">
                                        <div class="flex-center">
                                            <div class="name">键帽材质</div>
                                                                    </div>
                                        <div class="adaptive">
                                            <div class="text">
                                                PBT                            </div>
                                        </div>
                                    </div>
                                                                    <div class="item">
                                        <div class="flex-center">
                                            <div class="name">供电方式</div>
                                                                    </div>
                                        <div class="adaptive">
                                            <div class="text">
                                                有线供电                            </div>
                                        </div>
                                    </div>
                                                                    <div class="item">
                                        <div class="flex-center">
                                            <div class="name">键帽字符工艺</div>
                                                                    </div>
                                        <div class="adaptive">
                                            <div class="text">
                                                热升华                            </div>
                                        </div>
                                    </div>
                                                                    <div class="item">
                                        <div class="flex-center">
                                            <div class="name">电池容量</div>
                                                                    </div>
                                        <div class="adaptive">
                                            <div class="text">
                                                1000-3999mAh                            </div>
                                        </div>
                                    </div>
                                                                    <div class="item">
                                        <div class="flex-center">
                                            <div class="name">键盘结构</div>
                                                                    </div>
                                        <div class="adaptive">
                                            <div class="text">
                                                其他                            </div>
                                        </div>
                                    </div>
                                                                                                                                            <div class="item">        <div class="flex-center">          <div class="name"></div>        </div>        <div class="adaptive"><div class="text"></div></div>      </div><div class="exclusive-row item">
                                                        <div class="flex-center">
                                                            <div class="name">包装清单</div>
                                                        </div>
                                                        <div class="adaptive">
                                                            <div class="text">
                                                                勇士翱翔机械键盘*1  彩盒*1                                            </div>
                                                        </div>
                                                    </div>
                                                                                                            </div>
                                                                        <div id="img-text-warp">
                                                <div id="img-text">
                                                    <div class="wrap-scale" style="height: auto;"><div id="quality-life" class="quality-life" style="display: none;" clstag="undefinedshangpin|keycount|product|pinzhishenghuo">
                                                        <div class="q-logo">
                                                            <img src="//img11.360buyimg.com/devfe/jfs/t19249/266/555939662/10324/447efd03/5a965eb2Nf83fd68c.jpg" alt="品质生活"/>
                                                        </div>
                                                        <ul class="quality-icon">
                                                                                                                                                                                                                        <li class="J-ql-iframe ql-ico-1" data-type="1" data-text="质量承诺" style="display:none" data-title="质量承诺" clstag="undefinedshangpin|keycount|product|zhijianchengnuo">
                                                            <a href="#"><i></i><span>质量承诺</span></a>
                                                        </li>
                                                        <li class="ql-ico-5" data-type="5" data-text="耐久性标签" style="display:none" clstag="undefinedshangpin|keycount|product|naijiuxingbiaoqian">
                                                            <a href="#"><i></i><span>耐久性标签</span></a>
                                                        </li>
                                                        <li class="ql-ico-3" data-type="3" data-text="吊牌" style="display:none" clstag="undefinedshangpin|keycount|product|diaopai">
                                                            <a href="#"><i></i><span>吊牌</span></a>
                                                        </li>
                                                        <li class="ql-ico-4" data-type="4" data-text="质检报告" style="display:none" clstag="undefinedshangpin|keycount|product|zhijianbaogao">
                                                            <a href="#"><i></i><span>质检报告</span></a>
                                                        </li>
                                                        <li class="ql-ico-2" data-type="2" data-text="CCC证书" style="display:none" clstag="undefinedshangpin|keycount|product|3czhengshu">
                                                            <a href="#"><i></i><span>CCC证书</span></a>
                                                        </li>
                                                                                                                                            <li class="fresh-ico-1" data-text="实时温控" data-type="v1" style="display:none" clstag="undefinedshangpin|keycount|product|shishiwenkong">
                                                                <a href="#"><i></i><span class="J-fresh-wd fresh-wd"></span><span>实时温控</span></a>
                                                            </li>
                                                            <li class="fresh-ico-2" data-text="检验报告" data-type="v2" style="display:none" clstag="undefinedshangpin|keycount|product|jiancebaogao">
                                                                <a href="#"><i></i><span>检验报告</span></a>
                                                            </li>
                                                                                                </ul>
                                                    </div></div>
                                                    <div class="wrap-scale" style="height: auto;"><div id="suyuan-video" style=""></div></div>
                                                        <div class="wrap-scale" style="height: auto;"><div id="J-detail-banner" style=""></div></div>
                                                       \s
                                                                                                    <div class="wrap-scale" style="height: auto;"><div id="J-detail-pop-poster" clstag="undefinedshangpin|keycount|product|pop-sjhb" style="">
                                                                                                            </div></div>
                                                                                                                                        <div class="wrap-scale" style="height: auto;"><div id="J-detail-pop-tpl-top-new" clstag="undefinedshangpin|keycount|product|pop-glbs" style="">
                                                                                                </div></div>
                                                        <div class="wrap-scale" style="height: 27411.7px;"><div class="detail-content clearfix" data-name="z-have-detail-nav" style="transform: scale(1.09067); transform-origin: 0px 0px;">
                                                            <div class="detail-content-wrap">
                                                                                                                                                   \s
                                                                <div class="detail-content-item">
                                                                    <div id="J-detail-top"></div>
                                                                                                                        <div id="J-detail-content" style="font-family: pingfangSC;font-size: 14px;color: #757575;"><br/><div cssurl="//sku-market-gw.jd.com/css/pc/10024286971190.css?t=1744993972090"></div><div id="zbViewModulesH" value="25079"></div><div skudesign="100010"></div><div class="ssd-module-wrap">
                            <div class="ssd-module M17004709977721 animate-M17004709977721" data-id="M17004709977721">
                       \s
                </div>
                <div class="ssd-module M17004709977802 animate-M17004709977802" data-id="M17004709977802">
                       \s
                </div>
                <div class="ssd-module M17004709977893 animate-M17004709977893" data-id="M17004709977893">
                       \s
                </div>
                <div class="ssd-module M17004709978014 animate-M17004709978014" data-id="M17004709978014">
                       \s
                </div>
                <div class="ssd-module M17004709978096 animate-M17004709978096" data-id="M17004709978096">
                       \s
                </div>
                <div class="ssd-module M17004709978197 animate-M17004709978197" data-id="M17004709978197">
                       \s
                </div>
                <div class="ssd-module M17004709978298 animate-M17004709978298" data-id="M17004709978298">
                       \s
                </div>
                <div class="ssd-module M17004709978399 animate-M17004709978399" data-id="M17004709978399">
                       \s
                </div>
                <div class="ssd-module M170047099784910 animate-M170047099784910" data-id="M170047099784910">
                       \s
                </div>
                <div class="ssd-module M170047099786011 animate-M170047099786011" data-id="M170047099786011">
                       \s
                </div>
                <div class="ssd-module M170047099787112 animate-M170047099787112" data-id="M170047099787112">
                       \s
                </div>
                <div class="ssd-module M170047099788213 animate-M170047099788213" data-id="M170047099788213">
                       \s
                </div>
                <div class="ssd-module M170047099789514 animate-M170047099789514" data-id="M170047099789514">
                       \s
                </div>
                <div class="ssd-module M170047099791015 animate-M170047099791015" data-id="M170047099791015">
                       \s
                </div>
                <div class="ssd-module M170047099792516 animate-M170047099792516" data-id="M170047099792516">
                       \s
                </div>
                <div class="ssd-module M170047099793717 animate-M170047099793717" data-id="M170047099793717">
                       \s
                </div>
                <div class="ssd-module M170047099794918 animate-M170047099794918" data-id="M170047099794918">
                       \s
                </div>
                <div class="ssd-module M170047099796219 animate-M170047099796219" data-id="M170047099796219">
                       \s
                </div>
                <div class="ssd-module M170047099797821 animate-M170047099797821" data-id="M170047099797821">
                       \s
                </div>
                <div class="ssd-module M170047099799123 animate-M170047099799123" data-id="M170047099799123">
                       \s
                </div>
                <div class="ssd-module M170047099800525 animate-M170047099800525" data-id="M170047099800525">
                       \s
                </div>
                <div class="ssd-module M170047099801826 animate-M170047099801826" data-id="M170047099801826">
                       \s
                </div>
                <div class="ssd-module M170047099803427 animate-M170047099803427" data-id="M170047099803427">
                       \s
                </div>
                <div class="ssd-module M170047099805028 animate-M170047099805028" data-id="M170047099805028">
                       \s
                </div>
                <div class="ssd-module M170047099806529 animate-M170047099806529" data-id="M170047099806529">
                       \s
                </div>
                                
                    </div>
                <!-- 2023-12-08 01:52:01 --> <br/><br/></div><!-- #J-detail-content -->
                                                                    <div id="J-detail-bottom"></div>
                                                                                                                                                                            <div id="activity_footer" clstag="undefinedshangpin|keycount|product|activityfooter"></div>
                                                                                                                        </div>
                                                                </div>
                                                               \s
                                                            </div></div>
                                                                                                        <div class="wrap-scale" style="height: auto;"><div id="J-detail-pop-tpl-bottom-new" clstag="undefinedshangpin|keycount|product|pop-glbs" style="">
                                                                                                        </div></div>
                                                            <div class="wrap-scale" style="height: auto;"><div class="clb" style=""></div></div>
                                                </div>
                                            </div>
                                                                                <div class="module-title">售后保障</div>
                                                        <div class="m m-content guarantee" id="guarantee">
                                
                                <div class="mc">
                            <div class="item-detail item-detail-copyright">
                            <div class="serve-agree-bd">
                    <dl>
                                                                               \s
                               \s
                                        <dt>
                            <i class="goods"></i>
                            <strong>卖家服务</strong>
                        </dt>
                        <dd>
                                                                                            <div id="zhibaoqi" class="zbxx">
                                                </div> <br/>
                                                                                                                            </dd>
                                                <dt>
                            <i class="goods"></i>
                            <strong>京东承诺</strong>
                        </dt>
                        <dd>
                                            京东平台卖家销售并发货的商品，由平台卖家提供发票和相应的售后服务。请您放心购买！<br/>
                                                        注：因厂家会在没有任何提前通知的情况下更改产品包装、产地或者一些附件，本司不能确保客户收到的货物与商城图片、产地、附件说明完全一致。只能确保为原厂正货！并且保证与当时市场上同样主流新品一致。若本商城没有及时更新，请大家谅解！
                        </dd>
                                                <dt>
                            <i class="goods"></i><strong>
                             正品行货             </strong>
                        </dt>
                                        <dd>京东商城向您保证所售商品均为正品行货，京东自营商品开具机打发票或电子发票。</dd>
                                                                    </dl>
                </div>
                                <div id="state">
                                    <strong>权利声明：</strong><br/>京东上的所有商品信息、客户评价、商品咨询、网友讨论等内容，是京东重要的经营资源，未经许可，禁止非法转载使用。
                                    <p><b>注：</b>本站商品信息均来自于合作方，其真实性、准确性和合法性由信息拥有者（合作方）负责。本站不提供任何保证，并不承担任何法律责任。</p>
                                                    <br/>
                                                    <p><b>预估到手价/到手价：</b>是在商品标价基础上减去各种折扣、可用优惠政策叠加金额之后的一种预估的价格，计算公式示例：预估到手价=商品标价-优惠券-满减（或折扣）-新客优惠（如有）-商家会员优惠（如有），用户在进入结算页面之后，根据满足条件可享的活动（如所购商品超出商品促销价限购数量）、优惠券组合，可能导致最终到手价与显示预估到手价有差异和变化，并且不同用户可能因为可以享受的优惠不同（造成这种不同的原因包括是否属于新客或商家会员，可用优惠券差异、购买多件商品而满减活动仅支持享受1次等）而导致最终到手价不同，并且可能出现未登录和登录状态下的到手价差异。</p> <p><b>京东价：</b>京东价为商品的销售价，是您最终决定是否购买商品的依据。</p> <p><b>划线价：</b>商品展示的划横线价格为参考价，并非原价，该价格可能是品牌专柜标价、商品吊牌价或由品牌供应商提供的正品零售价（如厂商指导价、建议零售价等）或其他真实有依据的价格；由于地区、时间的差异性和市场行情波动，品牌专柜标价、商品吊牌价等可能会与您购物时展示的不一致，该价格仅供您参考。</p> <p><b>折扣：</b>如无特殊说明，折扣指销售商在原价、或划线价（如品牌专柜标价、商品吊牌价、厂商指导价、厂商建议零售价）等某一价格基础上计算出的优惠比例或优惠金额；如有疑问，您可在购买前联系销售商进行咨询。</p> <p><b>异常问题：</b>商品促销信息以商品详情页“促销”栏中的信息为准；商品的具体售价以订单结算页价格为准；如您发现活动商品售价或促销信息有异常，建议购买前先联系销售商咨询。</p>                                </div>
                    </div>
                </div>
                </div>
                                                                                                                                                                                                </div>
                
                """;




        // 期望输出
        String expected = "禾果小镇定西马铃薯 新鲜产地直销，当季现挖现发！ 特惠价：¥29.9/5斤装 规格：中果（150-200g/个） 保存方法：阴凉通风处保存 点击领取优惠券";
        String result = service.sanitizeAndExtractText(inputHtml);
        System.out.println(result);
//        assertEquals(expected, result);

    }
}
