package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.TrainTeamShops;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TrainTeamShopsMapper测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-16
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TrainTeamShopsMapperTest {

    @Autowired
    private TrainTeamShopsMapper trainTeamShopsMapper;

    @Test
    public void testFindByShopId() {
        // 准备测试数据
        TrainTeamShops shop1 = new TrainTeamShops();
        shop1.setTeamId(1L);
        shop1.setShopId(12345L);
        shop1.setShopType(0); // 京东
        shop1.setCreator("test_user");
        shop1.setUpdater("test_user");
        shop1.setIsAuthorize(true);
        shop1.setIsSyncComplete(0);
        
        TrainTeamShops shop2 = new TrainTeamShops();
        shop2.setTeamId(2L);
        shop2.setShopId(12345L); // 同一个shopId
        shop2.setShopType(0); // 京东
        shop2.setCreator("test_user2");
        shop2.setUpdater("test_user2");
        shop2.setIsAuthorize(false);
        shop2.setIsSyncComplete(1);

        // 插入测试数据
        trainTeamShopsMapper.insert(shop1);
        trainTeamShopsMapper.insert(shop2);

        // 测试findByShopId方法
        TrainTeamShops result = trainTeamShopsMapper.findByShopId(12345L);

        // 验证结果
        assertNotNull(result);
        assertEquals(12345L, result.getShopId());

        // 由于LIMIT 1，只会返回第一条记录（通常是team_id=1的记录）
        assertEquals(1L, result.getTeamId());
        assertTrue(result.getIsAuthorize());
        assertEquals(0, result.getIsSyncComplete());
    }

    @Test
    public void testUpdateByShopId() {
        // 准备测试数据
        TrainTeamShops shop1 = new TrainTeamShops();
        shop1.setTeamId(1L);
        shop1.setShopId(54321L);
        shop1.setShopType(0);
        shop1.setCreator("test_user");
        shop1.setUpdater("test_user");
        shop1.setIsAuthorize(false);
        shop1.setIsSyncComplete(0);
        
        TrainTeamShops shop2 = new TrainTeamShops();
        shop2.setTeamId(2L);
        shop2.setShopId(54321L);
        shop2.setShopType(1); // 淘宝
        shop2.setCreator("test_user2");
        shop2.setUpdater("test_user2");
        shop2.setIsAuthorize(false);
        shop2.setIsSyncComplete(0);

        // 插入测试数据
        trainTeamShopsMapper.insert(shop1);
        trainTeamShopsMapper.insert(shop2);

        // 测试updateByShopId方法
        int updatedRows = trainTeamShopsMapper.updateByShopId(54321L, true, 2, "updated_user");

        // 验证更新结果
        assertEquals(2, updatedRows); // 应该更新了2行

        // 验证更新后的数据（只验证第一条记录）
        TrainTeamShops result = trainTeamShopsMapper.findByShopId(54321L);
        assertNotNull(result);
        assertEquals(54321L, result.getShopId());
        assertTrue(result.getIsAuthorize());
        assertEquals(2, result.getIsSyncComplete());
        assertEquals("updated_user", result.getUpdater());
    }

    @Test
    public void testFindByShopIdNotFound() {
        // 测试查询不存在的shopId
        TrainTeamShops result = trainTeamShopsMapper.findByShopId(99999L);

        assertNull(result);
    }

    @Test
    public void testUpdateByShopIdNotFound() {
        // 测试更新不存在的shopId
        int updatedRows = trainTeamShopsMapper.updateByShopId(99999L, true, 2, "test_user");
        
        assertEquals(0, updatedRows);
    }
}
