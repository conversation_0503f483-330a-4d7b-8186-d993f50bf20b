package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.TrainTeamShopWithToken;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * TrainTeamShopsMapper 未过期Token查询测试类
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class TrainTeamShopsValidTokenTest {

    @Autowired
    private TrainTeamShopsMapper trainTeamShopsMapper;

    @Test
    @DisplayName("测试查询未过期的店铺token")
    void testSelectValidShopAndTokenByTeamId() {
        log.info("=== 测试查询未过期的店铺token ===");

        // 测试查询不存在的团队（验证SQL语法正确性）
        String nonExistentTeamId = "999999";
        List<TrainTeamShopWithToken> result = trainTeamShopsMapper.selectValidShopAndTokenByTeamId(nonExistentTeamId);
        
        // 结果应该为空列表，但不应该抛出SQL异常
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();
        log.info("查询不存在的团队ID: {}, SQL执行正常，返回空列表", nonExistentTeamId);
    }

    @Test
    @DisplayName("验证未过期查询的过滤条件")
    void testValidTokenFilterCondition() {
        log.info("=== 验证未过期查询的过滤条件 ===");
        log.info("过滤条件说明:");
        log.info("WHERE ts.team_id = #{teamId}");
        log.info("AND (jd.expires_time IS NULL OR jd.expires_time >= NOW())");
        log.info("");
        log.info("这个条件确保了:");
        log.info("1. 只查询指定团队的店铺");
        log.info("2. 只返回未过期的token:");
        log.info("   - expires_time 为 NULL 的记录（永久有效）");
        log.info("   - expires_time >= NOW() 的记录（尚未过期）");
        log.info("3. 过滤掉所有已过期的token");
        
        // 测试有效的团队ID查询（即使没有数据也应该正常执行）
        String validTeamId = "1001";
        List<TrainTeamShopWithToken> result = trainTeamShopsMapper.selectValidShopAndTokenByTeamId(validTeamId);
        
        // 结果应该是非null的列表（可能为空）
        assertThat(result).isNotNull();
        log.info("查询团队ID: {}, 未过期店铺数量: {}", validTeamId, result.size());
        
        // 如果有数据，验证所有返回的记录都是未过期的
        if (!result.isEmpty()) {
            for (TrainTeamShopWithToken shop : result) {
                log.info("店铺: shopId={}, expiresTime={}, isExpired={}", 
                        shop.getShopId(), shop.getExpiresTime(), shop.getIsExpired());
                
                // 验证isExpired字段应该为false（未过期）
                if (shop.getIsExpired() != null) {
                    assertThat(shop.getIsExpired()).isFalse();
                }
            }
        }
    }

    @Test
    @DisplayName("对比全量查询和未过期查询的区别")
    void testCompareAllVsValidQuery() {
        log.info("=== 对比全量查询和未过期查询的区别 ===");
        
        String testTeamId = "1001";
        
        // 查询全量数据
        List<TrainTeamShopWithToken> allShops = trainTeamShopsMapper.selectShopAndTokenByShopIdlist(testTeamId);
        
        // 查询未过期数据
        List<TrainTeamShopWithToken> validShops = trainTeamShopsMapper.selectValidShopAndTokenByTeamId(testTeamId);
        
        assertThat(allShops).isNotNull();
        assertThat(validShops).isNotNull();
        
        log.info("团队ID: {}", testTeamId);
        log.info("全量店铺数量: {}", allShops.size());
        log.info("未过期店铺数量: {}", validShops.size());
        
        // 未过期的数量应该小于等于全量数量
        assertThat(validShops.size()).isLessThanOrEqualTo(allShops.size());
        
        log.info("查询对比完成，过滤逻辑正常");
    }
}
