package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.TrainTeamShopWithToken;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * TrainTeamShopsMapper 过期状态测试类
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class TrainTeamShopsMapperExpiredTest {

    @Autowired
    private TrainTeamShopsMapper trainTeamShopsMapper;

    @Test
    @DisplayName("测试查询店铺过期状态逻辑")
    void testShopExpiredStatus() {
        log.info("=== 测试查询店铺过期状态逻辑 ===");

        // 测试查询不存在的店铺（验证SQL语法正确性）
        String nonExistentShopId = "999999";
        TrainTeamShopWithToken result = trainTeamShopsMapper.selectShopAndTokenByShopId(nonExistentShopId);
        
        // 结果应该为null（没有找到数据），但不应该抛出SQL异常
        assertThat(result).isNull();
        log.info("查询不存在的店铺ID: {}, SQL执行正常", nonExistentShopId);
    }

    @Test
    @DisplayName("测试查询团队店铺列表过期状态逻辑")
    void testTeamShopsExpiredStatus() {
        log.info("=== 测试查询团队店铺列表过期状态逻辑 ===");

        // 测试查询不存在的团队（验证SQL语法正确性）
        String nonExistentTeamId = "999999";
        List<TrainTeamShopWithToken> result = trainTeamShopsMapper.selectShopAndTokenByShopIdlist(nonExistentTeamId);
        
        // 结果应该为空列表，但不应该抛出SQL异常
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();
        log.info("查询不存在的团队ID: {}, SQL执行正常，返回空列表", nonExistentTeamId);
    }

    @Test
    @DisplayName("验证过期状态字段映射")
    void testExpiredFieldMapping() {
        log.info("=== 验证过期状态字段映射 ===");

        // 测试查询，验证isExpired字段能正确映射
        String testShopId = "12345";
        TrainTeamShopWithToken result = trainTeamShopsMapper.selectShopAndTokenByShopId(testShopId);
        
        // 即使没有数据，也验证了SQL中的CASE语句语法正确
        log.info("过期状态字段映射测试完成，SQL语法正确");
        
        // 如果有数据，验证isExpired字段不为null
        if (result != null) {
            log.info("找到店铺数据: shopId={}, isExpired={}, deadLine={}", 
                    result.getShopId(), result.getIsExpired(), result.getDeadLine());
            
            // isExpired应该是Boolean类型，不应该为null（因为CASE语句总是返回0或1）
            assertThat(result.getIsExpired()).isNotNull();
        }
    }

    @Test
    @DisplayName("验证过期状态逻辑说明")
    void testExpiredLogicExplanation() {
        log.info("=== 过期状态逻辑说明 ===");
        log.info("过期状态判断逻辑:");
        log.info("1. 如果 dead_line 为 NULL，则 is_expired = 0 (未过期)");
        log.info("2. 如果 dead_line < NOW()，则 is_expired = 1 (已过期)");
        log.info("3. 其他情况，is_expired = 0 (未过期)");
        log.info("这个逻辑确保了:");
        log.info("- 没有设置过期时间的店铺被认为是永久有效的");
        log.info("- 只有明确设置了过期时间且已经过了当前时间的店铺才被标记为过期");
    }
}
