package com.yiyi.ai_train_playground.mapper;

import com.yiyi.ai_train_playground.entity.TrainJdProducts;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TrainJdProductsMapper 测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@SpringBootTest
@Transactional
public class TrainJdProductsMapperTest {
    
    @Autowired
    private TrainJdProductsMapper trainJdProductsMapper;
    
    @Test
    @DisplayName("测试插入和查询商品信息")
    public void testInsertAndFindProduct() {
        // 准备测试数据
        TrainJdProducts product = new TrainJdProducts();
        product.setTeamId(1L);
        product.setWareId(12345L);
        product.setBrandId(100L);
        product.setBrandName("测试品牌");
        product.setCategoryId(200L);
        product.setCategorySecId(201L);
        product.setTitle("测试商品标题");
        product.setJdPrice(new BigDecimal("99.99"));
        product.setMarketPrice(new BigDecimal("129.99"));
        product.setStockNum(100);
        product.setWareStatus(1);
        product.setIntroduction("测试商品介绍");
        product.setMobileDesc("手机端描述");
        product.setJdProdDtl("京东商品详情易蚁展示内容");
        product.setCreator("test_user");
        product.setUpdater("test_user");
        
        // 测试插入
        int insertResult = trainJdProductsMapper.insert(product);
        assertEquals(1, insertResult, "插入应该成功");
        assertNotNull(product.getId(), "插入后应该生成ID");
        
        // 测试查询
        TrainJdProducts foundProduct = trainJdProductsMapper.findByWareIdAndTeamId(12345L, 1L);
        assertNotNull(foundProduct, "应该能查询到插入的商品");
        assertEquals("测试品牌", foundProduct.getBrandName());
        assertEquals("测试商品标题", foundProduct.getTitle());
        assertEquals("京东商品详情易蚁展示内容", foundProduct.getJdProdDtl());
        
        System.out.println("✅ 插入和查询测试通过");
        System.out.println("商品ID: " + foundProduct.getId());
        System.out.println("商品标题: " + foundProduct.getTitle());
        System.out.println("京东商品详情: " + foundProduct.getJdProdDtl());
    }
    
    @Test
    @DisplayName("测试更新商品信息")
    public void testUpdateProduct() {
        // 先插入一个商品
        TrainJdProducts product = new TrainJdProducts();
        product.setTeamId(1L);
        product.setWareId(12346L);
        product.setBrandId(100L);
        product.setBrandName("原始品牌");
        product.setTitle("原始标题");
        product.setJdPrice(new BigDecimal("50.00"));
        product.setJdProdDtl("原始详情");
        product.setCreator("test_user");
        product.setUpdater("test_user");
        
        trainJdProductsMapper.insert(product);
        
        // 更新商品信息
        product.setBrandName("更新后品牌");
        product.setTitle("更新后标题");
        product.setJdPrice(new BigDecimal("75.00"));
        product.setJdProdDtl("更新后的京东商品详情");
        product.setUpdater("update_user");
        
        int updateResult = trainJdProductsMapper.updateByWareIdAndTeamId(product);
        assertEquals(1, updateResult, "更新应该成功");
        
        // 验证更新结果
        TrainJdProducts updatedProduct = trainJdProductsMapper.findByWareIdAndTeamId(12346L, 1L);
        assertNotNull(updatedProduct);
        assertEquals("更新后品牌", updatedProduct.getBrandName());
        assertEquals("更新后标题", updatedProduct.getTitle());
        assertEquals(new BigDecimal("75.00"), updatedProduct.getJdPrice());
        assertEquals("更新后的京东商品详情", updatedProduct.getJdProdDtl());
        assertEquals("update_user", updatedProduct.getUpdater());
        
        System.out.println("✅ 更新测试通过");
        System.out.println("更新后品牌: " + updatedProduct.getBrandName());
        System.out.println("更新后详情: " + updatedProduct.getJdProdDtl());
    }
    
    @Test
    @DisplayName("测试新字段jdProdDtl的处理")
    public void testJdProdDtlField() {
        // 测试空值处理
        TrainJdProducts product1 = new TrainJdProducts();
        product1.setTeamId(1L);
        product1.setWareId(12347L);
        product1.setBrandName("测试品牌");
        product1.setTitle("测试标题");
        product1.setJdProdDtl(null); // 设置为null
        product1.setCreator("test_user");
        product1.setUpdater("test_user");
        
        trainJdProductsMapper.insert(product1);
        TrainJdProducts found1 = trainJdProductsMapper.findByWareIdAndTeamId(12347L, 1L);
        assertNull(found1.getJdProdDtl(), "null值应该正确处理");
        
        // 测试长文本处理
        String longText = "这是一个很长的京东商品详情内容，".repeat(100);
        TrainJdProducts product2 = new TrainJdProducts();
        product2.setTeamId(1L);
        product2.setWareId(12348L);
        product2.setBrandName("测试品牌2");
        product2.setTitle("测试标题2");
        product2.setJdProdDtl(longText);
        product2.setCreator("test_user");
        product2.setUpdater("test_user");
        
        trainJdProductsMapper.insert(product2);
        TrainJdProducts found2 = trainJdProductsMapper.findByWareIdAndTeamId(12348L, 1L);
        assertEquals(longText, found2.getJdProdDtl(), "长文本应该正确存储和读取");
        
        System.out.println("✅ jdProdDtl字段测试通过");
        System.out.println("长文本长度: " + found2.getJdProdDtl().length());
    }
}
