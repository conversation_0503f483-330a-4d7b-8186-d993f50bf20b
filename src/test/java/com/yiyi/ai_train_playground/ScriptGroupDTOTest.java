package com.yiyi.ai_train_playground;

import com.yiyi.ai_train_playground.dto.ScriptGroupDTO;
import com.yiyi.ai_train_playground.entity.ScriptGroup;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class ScriptGroupDTOTest {

    @Test
    public void testIsOfficialFieldConversion() {
        // 测试官方分组 (isOfficial = true -> 1)
        ScriptGroup officialGroup = new ScriptGroup();
        officialGroup.setId(1L);
        officialGroup.setGroupTitle("官方分组");
        officialGroup.setIsOfficial(true);
        
        ScriptGroupDTO officialDTO = ScriptGroupDTO.fromEntity(officialGroup);
        assertEquals(1, officialDTO.getIsOfficial(), "官方分组的isOfficial应该为1");
        
        // 测试用户分组 (isOfficial = false -> 0)
        ScriptGroup userGroup = new ScriptGroup();
        userGroup.setId(2L);
        userGroup.setGroupTitle("用户分组");
        userGroup.setIsOfficial(false);
        
        ScriptGroupDTO userDTO = ScriptGroupDTO.fromEntity(userGroup);
        assertEquals(0, userDTO.getIsOfficial(), "用户分组的isOfficial应该为0");
        
        // 测试null值 (isOfficial = null -> 0)
        ScriptGroup nullGroup = new ScriptGroup();
        nullGroup.setId(3L);
        nullGroup.setGroupTitle("null分组");
        nullGroup.setIsOfficial(null);
        
        ScriptGroupDTO nullDTO = ScriptGroupDTO.fromEntity(nullGroup);
        assertEquals(0, nullDTO.getIsOfficial(), "null分组的isOfficial应该为0");
    }
} 