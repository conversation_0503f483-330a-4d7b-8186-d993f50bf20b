package com.yiyi.ai_train_playground;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.enums.SceneName;
import org.junit.jupiter.api.Test;
import org.springframework.messaging.converter.MappingJackson2MessageConverter;
import org.springframework.messaging.simp.stomp.*;
import org.springframework.web.socket.client.standard.StandardWebSocketClient;
import org.springframework.web.socket.messaging.WebSocketStompClient;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * WebSocket STOMP客户端完整测试
 * 
 * 这个测试类作为独立的STOMP客户端，不依赖SpringBoot应用上下文
 * 展示完整的WebSocket交互过程：连接 -> 单个初始化 -> 批量初始化 -> 聊天
 * 
 * 使用方法：
 * 1. 先启动SpringBoot应用: mvn spring-boot:run
 * 2. 运行测试: mvn test -Dtest=StompClientTest
 */
public class StompClientTest {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final String WEBSOCKET_URL = "ws://localhost:8080/ws";
    private static final String TEST_SERVICE_ID = "12345";
    private static final String TEST_TOKEN = "eyJhbGciOiJIUzI1NiJ9.eyJ0ZWFtSWQiOjEsInVzZXJJZCI6MSwic3ViIjoidXNlcjEiLCJpYXQiOjE3NDk0Mzc5MDgsImV4cCI6MTc1MDA0MjcwOH0.7fJv4KudfP9AY2xWVhB39-0QYzRG23JDIeO9RZnphoQ";

    /**
     * 测试1: WebSocket STOMP基础连接
     * 验证能够成功连接到WebSocket服务器并建立STOMP会话
     */
    @Test
    public void testStompConnection() throws Exception {
        System.out.println("🔗 测试1: WebSocket STOMP基础连接测试");
        System.out.println("=" + "=".repeat(60));

        WebSocketStompClient stompClient = new WebSocketStompClient(new StandardWebSocketClient());
        stompClient.setMessageConverter(new MappingJackson2MessageConverter());

        CountDownLatch connectLatch = new CountDownLatch(1);

        StompSessionHandler sessionHandler = new StompSessionHandlerAdapter() {
            @Override
            public void afterConnected(StompSession session, StompHeaders connectedHeaders) {
                System.out.println("✅ STOMP连接成功!");
                System.out.println("📞 会话ID: " + session.getSessionId());
                System.out.println("🔗 连接头信息: " + connectedHeaders);
                System.out.println("🌐 WebSocket端点: " + WEBSOCKET_URL);
                connectLatch.countDown();
            }

            @Override
            public void handleException(StompSession session, StompCommand command,
                                        StompHeaders headers, byte[] payload, Throwable exception) {
                System.err.println("❌ STOMP异常: " + exception.getMessage());
                if (exception.getCause() != null) {
                    System.err.println("🔍 根本原因: " + exception.getCause().getMessage());
                }
            }

            @Override
            public void handleTransportError(StompSession session, Throwable exception) {
                System.err.println("❌ 传输错误: " + exception.getMessage());
            }
        };

        try {
            System.out.println("🚀 正在连接到: " + WEBSOCKET_URL);
            
            StompSession session = stompClient.connect(WEBSOCKET_URL, sessionHandler)
                    .get(15, TimeUnit.SECONDS);

            assertTrue(connectLatch.await(15, TimeUnit.SECONDS), "WebSocket连接超时");

            System.out.println("✅ 连接状态: " + (session.isConnected() ? "已连接" : "未连接"));
            System.out.println("📊 测试1结果: 连接测试成功 ✅");

            session.disconnect();
            System.out.println("🔌 连接已断开");

        } catch (Exception e) {
            System.err.println("❌ 连接失败: " + e.getMessage());
            System.err.println("💡 请确保SpringBoot应用正在8080端口运行: mvn spring-boot:run");
            throw e;
        }

        System.out.println();
    }

    /**
     * 测试2: 单个会话初始化
     * 测试 /app/init 端点和 /topic/init 主题
     */
    @Test
    public void testSingleSessionInitialization() throws Exception {
        System.out.println("🤖 测试2: 单个会话初始化测试");
        System.out.println("=" + "=".repeat(60));

        WebSocketStompClient stompClient = new WebSocketStompClient(new StandardWebSocketClient());
        stompClient.setMessageConverter(new MappingJackson2MessageConverter());

        CountDownLatch connectLatch = new CountDownLatch(1);
        CountDownLatch responseLatch = new CountDownLatch(1);

        StompSessionHandler sessionHandler = new StompSessionHandlerAdapter() {
            @Override
            public void afterConnected(StompSession session, StompHeaders connectedHeaders) {
                System.out.println("✅ 步骤1: 连接建立成功");
                connectLatch.countDown();
            }
        };

        StompSession session = stompClient.connect(WEBSOCKET_URL, sessionHandler).get(15, TimeUnit.SECONDS);
        assertTrue(connectLatch.await(15, TimeUnit.SECONDS));

        System.out.println("✅ 步骤2: 订阅单个初始化主题 (/topic/init)");
        
        // 订阅单个初始化响应
        session.subscribe("/topic/init", new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return String.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                try {
                    System.out.println("✅ 步骤4: 收到单个机器人初始化响应");
                    
                    Map<String, Object> robot = objectMapper.readValue(
                            payload.toString(), 
                            new TypeReference<Map<String, Object>>() {}
                    );
                    
                    System.out.println("🤖 机器人信息:");
                    System.out.println("   - 会话ID: " + robot.get("sessionId"));
                    System.out.println("   - 机器人名称: " + robot.get("robotName"));
                    System.out.println("   - 服务名称: " + robot.get("serviceName"));
                    System.out.println("   - 首条消息: " + robot.get("firstMessage"));
                    
                } catch (Exception e) {
                    System.err.println("❌ 解析单个初始化响应失败: " + e.getMessage());
                    System.out.println("📄 原始响应: " + payload);
                }
                
                responseLatch.countDown();
            }
        });

        System.out.println("✅ 步骤3: 发送单个初始化请求 (/app/init)");
        
        // 发送单个初始化请求
        Map<String, Object> initRequest = Map.of(
                "sceneName", "TRIAL_ONE",
                "servicerId", TEST_SERVICE_ID,
                "token", TEST_TOKEN
        );

        session.send("/app/init", objectMapper.writeValueAsString(initRequest));
        System.out.println("📤 请求内容: " + objectMapper.writeValueAsString(initRequest));

        // 等待响应
        boolean responseReceived = responseLatch.await(20, TimeUnit.SECONDS);
        System.out.println("📊 测试2结果: " + (responseReceived ? "单个初始化成功 ✅" : "响应超时 ❌"));

        session.disconnect();
        System.out.println();
    }

    /**
     * 测试3: 批量会话初始化
     * 测试 /app/initMultiple 端点和 /topic/initMultiple 主题
     */
    @Test
    public void testMultipleSessionInitialization() throws Exception {
        System.out.println("🤖🤖🤖🤖 测试3: 批量会话初始化测试");
        System.out.println("=" + "=".repeat(60));

        WebSocketStompClient stompClient = new WebSocketStompClient(new StandardWebSocketClient());
        stompClient.setMessageConverter(new MappingJackson2MessageConverter());

        CountDownLatch connectLatch = new CountDownLatch(1);
        CountDownLatch responseLatch = new CountDownLatch(1);

        StompSessionHandler sessionHandler = new StompSessionHandlerAdapter() {
            @Override
            public void afterConnected(StompSession session, StompHeaders connectedHeaders) {
                System.out.println("✅ 步骤1: 连接建立成功");
                connectLatch.countDown();
            }
        };

        StompSession session = stompClient.connect(WEBSOCKET_URL, sessionHandler).get(15, TimeUnit.SECONDS);
        assertTrue(connectLatch.await(15, TimeUnit.SECONDS));

        System.out.println("✅ 步骤2: 订阅批量初始化主题 (/topic/initMultiple)");

        // 订阅批量初始化响应
        session.subscribe("/topic/initMultiple", new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return String.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                try {
                    System.out.println("✅ 步骤4: 收到批量机器人初始化响应");
                    
                    List<Map<String, Object>> robots = objectMapper.readValue(
                            payload.toString(), 
                            new TypeReference<List<Map<String, Object>>>() {}
                    );
                    
                    System.out.println("📊 成功初始化机器人数量: " + robots.size());
                    System.out.println();
                    
                    for (int i = 0; i < robots.size(); i++) {
                        Map<String, Object> robot = robots.get(i);
                        System.out.println("🤖 机器人 " + (i + 1) + ":");
                        System.out.println("   - 会话ID: " + robot.get("sessionId"));
                        System.out.println("   - 机器人名称: " + robot.get("robotName"));
                        System.out.println("   - 服务名称: " + robot.get("serviceName"));
                        System.out.println("   - 首条消息: " + robot.get("firstMessage"));
                        System.out.println();
                    }
                    
                } catch (Exception e) {
                    System.err.println("❌ 解析批量初始化响应失败: " + e.getMessage());
                    System.out.println("📄 原始响应: " + payload);
                }
                
                responseLatch.countDown();
            }
        });

        System.out.println("✅ 步骤3: 发送批量初始化请求 (/app/initMultiple)");

        // 发送批量初始化请求
        Map<String, Object> initRequest = Map.of(
                "sceneName", SceneName.TRIAL_ONE.getCode(),
                "servicerId", TEST_SERVICE_ID,
                "token", TEST_TOKEN
        );

        session.send("/app/initMultiple", objectMapper.writeValueAsString(initRequest));
        System.out.println("📤 请求内容: " + objectMapper.writeValueAsString(initRequest));

        // 等待响应
        boolean responseReceived = responseLatch.await(20, TimeUnit.SECONDS);
        System.out.println("📊 测试3结果: " + (responseReceived ? "批量初始化成功 ✅" : "响应超时 ❌"));

        session.disconnect();
        System.out.println();
    }

    /**
     * 测试4: 完整的WebSocket工作流程
     * 连接 -> 批量初始化 -> 聊天交互 -> 断开
     */
    @Test
    public void testCompleteWebSocketWorkflow() throws Exception {
        System.out.println("🚀 测试4: 完整WebSocket工作流程测试");
        System.out.println("=" + "=".repeat(60));

        WebSocketStompClient stompClient = new WebSocketStompClient(new StandardWebSocketClient());
        stompClient.setMessageConverter(new MappingJackson2MessageConverter());

        CountDownLatch connectLatch = new CountDownLatch(1);
        CountDownLatch initLatch = new CountDownLatch(1);
        CountDownLatch chatLatch = new CountDownLatch(1);

        String[] firstSessionId = new String[1];

        StompSessionHandler sessionHandler = new StompSessionHandlerAdapter() {
            @Override
            public void afterConnected(StompSession session, StompHeaders connectedHeaders) {
                System.out.println("✅ 步骤1: WebSocket STOMP连接建立成功");
                System.out.println("📞 STOMP会话ID: " + session.getSessionId());
                connectLatch.countDown();
            }

            @Override
            public void handleException(StompSession session, StompCommand command,
                                        StompHeaders headers, byte[] payload, Throwable exception) {
                System.err.println("❌ STOMP异常 [" + command + "]: " + exception.getMessage());
            }
        };

        StompSession session = stompClient.connect(WEBSOCKET_URL, sessionHandler).get(15, TimeUnit.SECONDS);
        assertTrue(connectLatch.await(15, TimeUnit.SECONDS));

        System.out.println("✅ 步骤2: 订阅批量初始化主题 (/topic/initMultiple)");

        // 订阅批量初始化响应
        session.subscribe("/topic/initMultiple", new StompFrameHandler() {
            @Override
            public Type getPayloadType(StompHeaders headers) {
                return String.class;
            }

            @Override
            public void handleFrame(StompHeaders headers, Object payload) {
                try {
                    System.out.println("✅ 步骤4: 收到批量初始化响应，解析机器人列表");
                    
                    List<Map<String, Object>> robots = objectMapper.readValue(
                            payload.toString(), 
                            new TypeReference<List<Map<String, Object>>>() {}
                    );
                    
                    if (!robots.isEmpty()) {
                        firstSessionId[0] = (String) robots.get(0).get("sessionId");
                        String robotName = (String) robots.get(0).get("robotName");
                        
                        System.out.println("🎯 选择第一个机器人进行聊天测试:");
                        System.out.println("   - 会话ID: " + firstSessionId[0]);
                        System.out.println("   - 机器人名称: " + robotName);
                        
                        System.out.println("✅ 步骤5: 订阅聊天主题 (/topic/chat/" + firstSessionId[0] + ")");
                        
                        // 订阅第一个机器人的聊天响应
                        session.subscribe("/topic/chat/" + firstSessionId[0], new StompFrameHandler() {
                            @Override
                            public Type getPayloadType(StompHeaders headers) {
                                return String.class;
                            }

                            @Override
                            public void handleFrame(StompHeaders headers, Object payload) {
                                System.out.println("💬 AI流式响应: " + payload);
                                chatLatch.countDown();
                            }
                        });
                    }
                    
                    initLatch.countDown();
                    
                } catch (Exception e) {
                    System.err.println("❌ 处理批量初始化响应失败: " + e.getMessage());
                    initLatch.countDown();
                }
            }
        });

        System.out.println("✅ 步骤3: 发送批量初始化请求 (/app/initMultiple)");

        // 发送批量初始化请求
        Map<String, Object> initRequest = Map.of(
                "sceneName", "TRIAL_ONE",
                "servicerId", TEST_SERVICE_ID,
                "token", TEST_TOKEN
        );

        session.send("/app/initMultiple", objectMapper.writeValueAsString(initRequest));

        // 等待初始化完成
        assertTrue(initLatch.await(20, TimeUnit.SECONDS), "批量初始化超时");

        if (firstSessionId[0] != null) {
            System.out.println("✅ 步骤6: 发送聊天消息测试 AI 响应");
            
            // 发送聊天消息
            Map<String, Object> chatRequest = Map.of(
                    "sessionId", firstSessionId[0],
                    "message", "你好，请简单介绍一下你们的产品特色功能"
            );

            session.send("/app/send", objectMapper.writeValueAsString(chatRequest));
            System.out.println("📤 聊天消息: " + chatRequest.get("message"));

            // 等待聊天响应
            boolean chatReceived = chatLatch.await(30, TimeUnit.SECONDS);
            System.out.println("✅ 步骤7: " + (chatReceived ? "成功接收到AI响应" : "聊天响应超时"));
            
        } else {
            System.err.println("❌ 未能获取有效的会话ID，跳过聊天测试");
        }

        session.disconnect();
        System.out.println("✅ 步骤8: WebSocket连接已断开");
        System.out.println("🎉 完整工作流程测试完成!");
        System.out.println("=" + "=".repeat(60));
    }

    /**
     * 测试5: STOMP协议格式演示
     * 展示标准STOMP协议帧格式
     */
    @Test
    public void demonstrateStompProtocol() {
        System.out.println("📋 测试5: STOMP协议格式演示");
        System.out.println("=" + "=".repeat(60));
        
        System.out.println("🔗 1. CONNECT帧 (连接到服务器):");
        System.out.println("CONNECT");
        System.out.println("accept-version:1.2");
        System.out.println("heart-beat:10000,10000");
        System.out.println("host:/");
        System.out.println();
        System.out.println("^@");
        System.out.println();
        
        System.out.println("📥 2. SUBSCRIBE帧 (订阅主题):");
        System.out.println("SUBSCRIBE");
        System.out.println("id:sub-1");
        System.out.println("destination:/topic/initMultiple");
        System.out.println();
        System.out.println("^@");
        System.out.println();
        
        System.out.println("📤 3. SEND帧 (发送消息):");
        System.out.println("SEND");
        System.out.println("destination:/app/initMultiple");
        System.out.println("content-type:application/json");
        System.out.println();
        System.out.println("{\"sceneName\":\"TRIAL_ONE\",\"servicerId\":\"12345\",\"token\":\"test-token\"}^@");
        System.out.println();
        
        System.out.println("📨 4. MESSAGE帧 (接收消息):");
        System.out.println("MESSAGE");
        System.out.println("destination:/topic/initMultiple");
        System.out.println("message-id:123");
        System.out.println("subscription:sub-1");
        System.out.println();
        System.out.println("[{\"sessionId\":\"robot1_12345\",\"robotName\":\"试用-洗碗机客户\",...}]^@");
        System.out.println();
        
        System.out.println("🔌 5. DISCONNECT帧 (断开连接):");
        System.out.println("DISCONNECT");
        System.out.println();
        System.out.println("^@");
        System.out.println();
        
        System.out.println("💡 注意事项:");
        System.out.println("   - ^@ 表示NULL字符 (\\0)，用于标识帧结束");
        System.out.println("   - 每行使用 \\n 换行符");
        System.out.println("   - header和body之间需要空行");
        System.out.println("   - 所有字符串都是UTF-8编码");
        System.out.println();
    }
} 