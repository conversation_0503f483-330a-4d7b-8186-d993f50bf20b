package com.yiyi.ai_train_playground;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class ChatWebSocketControllerTest {

    @Test
    public void testSystemPromptsParameterParsing() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        
        // 模拟请求数据
        Map<String, Object> request = new HashMap<>();
        request.put("sceneName", "trialFour");
        request.put("token", "test-token");
        request.put("isThinking", true);
        request.put("isStreaming", false);
        request.put("systemPrompts", Arrays.asList("提示词1", "提示词2", "提示词3"));
        request.put("robotCount", 3);
        
        String message = objectMapper.writeValueAsString(request);
        
        // 解析测试
        Map<String, Object> parsedRequest = objectMapper.readValue(message, Map.class);
        
        // 验证基本参数
        assertEquals("trialFour", parsedRequest.get("sceneName"));
        assertTrue((Boolean) parsedRequest.get("isThinking"));
        assertFalse((Boolean) parsedRequest.get("isStreaming"));
        
        // 验证系统提示词数组解析
        Object systemPromptsObj = parsedRequest.get("systemPrompts");
        assertNotNull(systemPromptsObj);
        assertTrue(systemPromptsObj instanceof java.util.List);
        
        // 验证机器人数量解析
        Object robotCountObj = parsedRequest.get("robotCount");
        assertNotNull(robotCountObj);
        assertTrue(robotCountObj instanceof Number);
        assertEquals(3, ((Number) robotCountObj).intValue());
    }
    
    @Test
    public void testSingleSystemPromptParsing() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        
        // 模拟单个机器人请求
        Map<String, Object> request = new HashMap<>();
        request.put("sceneName", "trialOne");
        request.put("token", "test-token");
        request.put("systemPrompt", "单个机器人的系统提示词");
        
        String message = objectMapper.writeValueAsString(request);
        Map<String, Object> parsedRequest = objectMapper.readValue(message, Map.class);
        
        // 验证系统提示词
        String systemPrompt = (String) parsedRequest.get("systemPrompt");
        assertNotNull(systemPrompt);
        assertEquals("单个机器人的系统提示词", systemPrompt);
    }
} 