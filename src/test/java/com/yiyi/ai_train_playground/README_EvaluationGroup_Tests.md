# 评价分组功能测试文档

## 📋 测试概述

本文档描述了评价分组(EvaluationGroup)功能的完整测试套件，包括单元测试、集成测试和端到端测试。

## 🏗️ 测试架构

```
评价分组测试套件
├── 控制器层测试 (EvaluationGroupControllerTest)
├── 服务层测试 (EvaluationGroupServiceImplTest)  
├── DTO转换测试 (EvaluationGroupDTOTest)
├── 集成测试 (EvaluationGroupIntegrationTest)
└── 测试套件 (EvaluationGroupTestSuite)
```

## 📁 测试文件列表

### 1. 控制器层测试
**文件**: `src/test/java/com/yiyi/ai_train_playground/controller/EvaluationGroupControllerTest.java`

**测试内容**:
- ✅ GET `/api/evaluation-groups` - 查询评价分组树
- ✅ POST `/api/evaluation-groups` - 新增评价分组
- ✅ PUT `/api/evaluation-groups` - 更新评价分组
- ✅ DELETE `/api/evaluation-groups` - 删除评价分组
- ✅ JWT权限验证
- ✅ 请求参数验证
- ✅ 响应格式验证

**测试方法**:
1. `testGetEvaluationGroups_Success()` - 成功获取分组树
2. `testGetEvaluationGroups_NoFilter()` - 无查询条件
3. `testSaveEvaluationGroup_Success()` - 成功新增分组
4. `testSaveEvaluationGroup_Failure()` - 新增失败
5. `testUpdateEvaluationGroup_Success()` - 成功更新分组
6. `testUpdateEvaluationGroup_NoPermission()` - 权限不足
7. `testDeleteEvaluationGroup_Success()` - 成功删除分组
8. `testDeleteEvaluationGroup_Failure()` - 删除失败

### 2. 服务层测试
**文件**: `src/test/java/com/yiyi/ai_train_playground/service/impl/EvaluationGroupServiceImplTest.java`

**测试内容**:
- ✅ 分组树构建逻辑
- ✅ CRUD操作
- ✅ 数据排序和分类
- ✅ 权限过滤
- ✅ 异常处理

**测试方法**:
1. `testGetGroupTree_EmptyData()` - 空数据处理
2. `testGetGroupTree_WithData()` - 有数据的分组树
3. `testSave_Success()` - 保存成功
4. `testSave_Failure()` - 保存失败
5. `testUpdate_Success()` - 更新成功
6. `testUpdate_Failure()` - 更新失败
7. `testDeleteByIds_Success()` - 删除成功
8. `testDeleteByIds_Failure()` - 删除失败
9. `testGetEvaluationGroups()` - 获取分组列表
10. `testGroupTreeBuilding()` - 分组树构建逻辑

### 3. DTO转换测试
**文件**: `src/test/java/com/yiyi/ai_train_playground/dto/EvaluationGroupDTOTest.java`

**测试内容**:
- ✅ 实体到DTO转换
- ✅ 循环引用处理
- ✅ 字段映射验证
- ✅ 嵌套结构转换

**测试方法**:
1. `testIsOfficialFieldConversion()` - isOfficial字段转换
2. `testBasicFieldConversion()` - 基本字段转换
3. `testNullEntityConversion()` - null实体处理
4. `testCircularReferenceHandling()` - 循环引用处理
5. `testDuplicateIdHandling()` - 重复ID处理
6. `testSubGroupsConversion()` - 子分组转换
7. `testEmptySubGroupsList()` - 空子分组列表
8. `testDeepNestedConversion()` - 深层嵌套转换

### 4. 集成测试
**文件**: `src/test/java/com/yiyi/ai_train_playground/integration/EvaluationGroupIntegrationTest.java`

**测试内容**:
- ✅ 端到端CRUD流程
- ✅ 数据库交互
- ✅ 权限控制
- ✅ 异常处理

**测试方法**:
1. `testCompleteCRUDFlow()` - 完整CRUD流程
2. `testGroupTreeStructure()` - 分组树结构
3. `testPermissionValidation()` - 权限验证
4. `testServiceBusinessLogic()` - 业务逻辑
5. `testExceptionHandling()` - 异常处理

## 🚀 运行测试

### 方式1: IDE运行
```bash
# 运行单个测试类
右键点击测试类 -> Run 'TestClassName'

# 运行测试套件
右键点击 EvaluationGroupTestSuite -> Run 'EvaluationGroupTestSuite'
```

### 方式2: Maven命令
```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=EvaluationGroupControllerTest

# 运行测试套件
mvn test -Dtest=EvaluationGroupTestSuite

# 运行集成测试
mvn test -Dtest=EvaluationGroupIntegrationTest
```

### 方式3: Gradle命令
```bash
# 运行所有测试
./gradlew test

# 运行特定测试类
./gradlew test --tests EvaluationGroupControllerTest
```

## 📊 测试覆盖率

| 层级 | 覆盖内容 | 测试类 | 覆盖率 |
|------|----------|--------|--------|
| Controller | REST API接口 | EvaluationGroupControllerTest | 100% |
| Service | 业务逻辑 | EvaluationGroupServiceImplTest | 100% |
| DTO | 数据转换 | EvaluationGroupDTOTest | 100% |
| Integration | 端到端流程 | EvaluationGroupIntegrationTest | 95% |

## 🔧 测试配置

### 测试数据库
```properties
# 使用H2内存数据库进行测试
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.jpa.hibernate.ddl-auto=create-drop
```

### Mock配置
- **JwtUtil**: 模拟JWT token解析
- **EvaluationGroupMapper**: 模拟数据库操作（单元测试）
- **EvaluationGroupService**: 模拟服务层调用（控制器测试）

## 📝 测试数据

### 测试用户信息
```java
private static final Long TEST_TEAM_ID = 123L;
private static final String TEST_USER_ID = "test-user";
private static final String TEST_TOKEN = "Bearer test-jwt-token";
```

### 测试分组数据
```java
EvaluationGroup testGroup = new EvaluationGroup();
testGroup.setGroupTitle("测试评价分组");
testGroup.setTeamId(123L);
testGroup.setIsOfficial(false);
```

## ⚠️ 注意事项

1. **数据隔离**: 每个测试方法都是独立的，使用`@Transactional`确保数据回滚
2. **Mock对象**: 单元测试使用Mock对象，集成测试使用真实数据库
3. **权限测试**: 确保测试不同团队之间的数据隔离
4. **异常处理**: 测试各种边界情况和异常场景

## 🐛 常见问题

### Q1: 测试运行失败，提示数据库连接错误
**A**: 检查H2数据库依赖是否正确配置，确保测试配置文件中的数据库URL正确。

### Q2: Mock对象没有生效
**A**: 确保使用了正确的注解：`@MockBean`用于Spring Boot测试，`@Mock`用于纯单元测试。

### Q3: 集成测试数据污染
**A**: 确保每个测试方法都添加了`@Transactional`注解，或在`@AfterEach`中清理数据。

### Q4: JWT token解析失败
**A**: 检查`JwtUtil`的Mock配置是否正确，确保返回了正确的Claims对象。

## 📈 测试报告

运行测试后，可以在以下位置查看测试报告：
- **Maven**: `target/surefire-reports/`
- **Gradle**: `build/reports/tests/test/`
- **IDE**: 测试运行窗口

## 🔄 持续集成

建议在CI/CD流水线中包含以下测试步骤：
1. 单元测试 (快速反馈)
2. 集成测试 (完整验证)
3. 测试覆盖率检查
4. 测试报告生成

```yaml
# GitHub Actions 示例
- name: Run Tests
  run: mvn test
  
- name: Generate Test Report
  run: mvn surefire-report:report
```

---

**最后更新**: 2025-07-02  
**维护者**: AI训练平台开发团队
