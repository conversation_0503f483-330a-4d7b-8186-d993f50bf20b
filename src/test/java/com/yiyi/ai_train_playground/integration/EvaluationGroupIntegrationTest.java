package com.yiyi.ai_train_playground.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.entity.EvaluationGroup;
import com.yiyi.ai_train_playground.mapper.EvaluationGroupMapper;
import com.yiyi.ai_train_playground.service.EvaluationGroupService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.impl.DefaultClaims;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@TestPropertySource(properties = {
    "my.doubao.normal.endpoint.name=ep-20250629195408-gtv9c"
})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Transactional
public class EvaluationGroupIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private EvaluationGroupService evaluationGroupService;

    @Autowired
    private EvaluationGroupMapper evaluationGroupMapper;

    @MockBean
    private JwtUtil jwtUtil;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    private static final String TEST_TOKEN = "Bearer test-jwt-token";
    private static final Long TEST_TEAM_ID = 123L;
    private static final String TEST_USER_ID = "test-user";

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // 模拟JWT解析
        Claims claims = new DefaultClaims();
        claims.put("teamId", TEST_TEAM_ID);
        claims.put("userId", TEST_USER_ID);
        when(jwtUtil.parseToken("test-jwt-token")).thenReturn(claims);
    }

    @Test
    @Order(1)
    @DisplayName("集成测试1: 完整的CRUD流程")
    void testCompleteCRUDFlow() throws Exception {
        System.out.println("=== 集成测试1: 完整的CRUD流程 ===");

        // 1. 创建评价分组
        EvaluationGroup newGroup = createTestEvaluationGroup();
        newGroup.setGroupTitle("集成测试分组");

        String createResponse = mockMvc.perform(post("/api/evaluation-groups")
                        .header("Authorization", TEST_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(newGroup)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andReturn().getResponse().getContentAsString();

        System.out.println("✅ 创建分组成功: " + createResponse);

        // 2. 查询分组树（验证创建的分组存在）
        String queryResponse = mockMvc.perform(get("/api/evaluation-groups")
                        .param("groupTitle", "集成测试")
                        .header("Authorization", TEST_TOKEN))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.groups").isArray())
                .andReturn().getResponse().getContentAsString();

        System.out.println("✅ 查询分组成功: " + queryResponse.substring(0, Math.min(200, queryResponse.length())));

        // 3. 更新分组（需要先获取ID）
        List<EvaluationGroup> groups = evaluationGroupService.getEvaluationGroups("集成测试", TEST_TEAM_ID);
        if (!groups.isEmpty()) {
            EvaluationGroup existingGroup = groups.get(0);
            existingGroup.setGroupTitle("更新后的集成测试分组");

            String updateResponse = mockMvc.perform(put("/api/evaluation-groups")
                            .header("Authorization", TEST_TOKEN)
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(existingGroup)))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andReturn().getResponse().getContentAsString();

            System.out.println("✅ 更新分组成功: " + updateResponse);

            // 4. 删除分组
            String deleteResponse = mockMvc.perform(delete("/api/evaluation-groups")
                            .param("ids", existingGroup.getId().toString())
                            .header("Authorization", TEST_TOKEN))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andReturn().getResponse().getContentAsString();

            System.out.println("✅ 删除分组成功: " + deleteResponse);
        }

        System.out.println("✅ 完整CRUD流程测试通过");
    }

    @Test
    @Order(2)
    @DisplayName("集成测试2: 分组树结构测试")
    void testGroupTreeStructure() {
        System.out.println("=== 集成测试2: 分组树结构测试 ===");

        // 创建父分组
        EvaluationGroup parentGroup = createTestEvaluationGroup();
        parentGroup.setGroupTitle("父级分组");
        boolean parentSaved = evaluationGroupService.save(parentGroup);
        assertTrue(parentSaved, "父分组应该保存成功");

        // 获取保存后的父分组（获取ID）
        List<EvaluationGroup> savedParents = evaluationGroupService.getEvaluationGroups("父级分组", TEST_TEAM_ID);
        assertFalse(savedParents.isEmpty(), "应该能找到保存的父分组");
        
        EvaluationGroup savedParent = savedParents.get(0);
        assertNotNull(savedParent.getId(), "父分组应该有ID");

        // 创建子分组
        EvaluationGroup childGroup = createTestEvaluationGroup();
        childGroup.setGroupTitle("子级分组");
        childGroup.setParentId(savedParent.getId());
        boolean childSaved = evaluationGroupService.save(childGroup);
        assertTrue(childSaved, "子分组应该保存成功");

        // 获取分组树
        Map<String, Object> groupTree = evaluationGroupService.getGroupTree(null, TEST_TEAM_ID);
        assertNotNull(groupTree, "分组树不应为null");
        assertTrue(groupTree.containsKey("groups"), "应该包含groups键");

        System.out.println("✅ 分组树结构: " + groupTree);
        System.out.println("✅ 分组树结构测试通过");
    }

    @Test
    @Order(3)
    @DisplayName("集成测试3: 权限验证测试")
    void testPermissionValidation() {
        System.out.println("=== 集成测试3: 权限验证测试 ===");

        // 创建属于其他团队的分组
        EvaluationGroup otherTeamGroup = createTestEvaluationGroup();
        otherTeamGroup.setTeamId(999L); // 不同的团队ID
        otherTeamGroup.setGroupTitle("其他团队分组");

        // 直接通过Mapper保存（绕过Service层的权限检查）
        int insertResult = evaluationGroupMapper.insert(otherTeamGroup);
        assertTrue(insertResult > 0, "其他团队分组应该保存成功");

        // 尝试查询（应该查不到其他团队的分组）
        List<EvaluationGroup> groups = evaluationGroupService.getEvaluationGroups("其他团队", TEST_TEAM_ID);
        assertTrue(groups.isEmpty(), "不应该能查到其他团队的分组");

        // 尝试删除其他团队的分组（应该失败）
        boolean deleteResult = evaluationGroupService.deleteByIds("1", TEST_TEAM_ID);
        // 删除结果取决于具体实现，这里主要验证不会出现异常

        System.out.println("✅ 权限验证测试通过");
    }

    @Test
    @Order(4)
    @DisplayName("集成测试4: 服务层业务逻辑测试")
    void testServiceBusinessLogic() {
        System.out.println("=== 集成测试4: 服务层业务逻辑测试 ===");

        // 测试保存分组
        EvaluationGroup group1 = createTestEvaluationGroup();
        group1.setGroupTitle("业务逻辑测试分组1");
        group1.setIsOfficial(false);
        
        boolean saved1 = evaluationGroupService.save(group1);
        assertTrue(saved1, "用户分组应该保存成功");

        // 测试保存官方分组
        EvaluationGroup group2 = createTestEvaluationGroup();
        group2.setGroupTitle("业务逻辑测试分组2");
        group2.setIsOfficial(true);
        group2.setTeamId(0L); // 官方分组团队ID为0
        
        boolean saved2 = evaluationGroupService.save(group2);
        assertTrue(saved2, "官方分组应该保存成功");

        // 获取分组树，验证分组分类逻辑
        Map<String, Object> groupTree = evaluationGroupService.getGroupTree(null, TEST_TEAM_ID);
        assertNotNull(groupTree, "分组树不应为null");

        System.out.println("分组树结构: " + groupTree);

        // 测试更新分组
        List<EvaluationGroup> userGroups = evaluationGroupService.getEvaluationGroups("业务逻辑测试分组1", TEST_TEAM_ID);
        if (!userGroups.isEmpty()) {
            EvaluationGroup userGroup = userGroups.get(0);
            userGroup.setGroupTitle("更新后的业务逻辑测试分组1");
            
            boolean updated = evaluationGroupService.update(userGroup);
            assertTrue(updated, "分组更新应该成功");
        }

        System.out.println("✅ 服务层业务逻辑测试通过");
    }

    @Test
    @Order(5)
    @DisplayName("集成测试5: 异常情况处理")
    void testExceptionHandling() {
        System.out.println("=== 集成测试5: 异常情况处理 ===");

        // 测试保存null分组
        assertDoesNotThrow(() -> {
            boolean result = evaluationGroupService.save(null);
            assertFalse(result, "保存null分组应该返回false");
        }, "保存null分组不应该抛出异常");

        // 测试更新不存在的分组
        EvaluationGroup nonExistentGroup = createTestEvaluationGroup();
        nonExistentGroup.setId(99999L); // 不存在的ID
        
        assertDoesNotThrow(() -> {
            boolean result = evaluationGroupService.update(nonExistentGroup);
            assertFalse(result, "更新不存在的分组应该返回false");
        }, "更新不存在的分组不应该抛出异常");

        // 测试删除不存在的分组
        assertDoesNotThrow(() -> {
            boolean result = evaluationGroupService.deleteByIds("99999", TEST_TEAM_ID);
            assertFalse(result, "删除不存在的分组应该返回false");
        }, "删除不存在的分组不应该抛出异常");

        // 测试查询空字符串
        assertDoesNotThrow(() -> {
            Map<String, Object> result = evaluationGroupService.getGroupTree("", TEST_TEAM_ID);
            assertNotNull(result, "查询结果不应为null");
        }, "查询空字符串不应该抛出异常");

        System.out.println("✅ 异常情况处理测试通过");
    }

    // 辅助方法：创建测试用的评价分组
    private EvaluationGroup createTestEvaluationGroup() {
        EvaluationGroup group = new EvaluationGroup();
        group.setGroupTitle("测试评价分组");
        group.setTeamId(TEST_TEAM_ID);
        group.setParentId(null);
        group.setIsOfficial(false);
        group.setSortOrder(0);
        group.setCreateTime(LocalDateTime.now());
        group.setUpdateTime(LocalDateTime.now());
        group.setCreator(TEST_USER_ID);
        group.setUpdater(TEST_USER_ID);
        group.setVersion(0L);
        return group;
    }
}
