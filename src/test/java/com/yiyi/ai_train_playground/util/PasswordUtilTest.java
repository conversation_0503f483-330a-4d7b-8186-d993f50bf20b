package com.yiyi.ai_train_playground.util;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import static org.junit.jupiter.api.Assertions.*;

//@SpringBootTest
class PasswordUtilTest {

    @Test
    void generatePasswordHash() {
        // 要测试的密码
        String password = "123456";
        
        // 生成密码哈希
        String hashedPassword = PasswordUtil.hashPassword(password);
        
        // 打印结果
        System.out.println("原始密码: " + password);
        System.out.println("加密后的密码: " + hashedPassword);
        
        // 验证密码
        assertTrue(PasswordUtil.verifyPassword(password, hashedPassword));
    }

    @Test
    void verifyPassword() {
        // 生成一个密码哈希
        String password = "123456";
        String hashedPassword = PasswordUtil.hashPassword(password);
        System.out.println("加密过后的密码为:"+hashedPassword);
        // 正确密码应该验证通过
        assertTrue(PasswordUtil.verifyPassword(password, hashedPassword));
        
        // 错误密码应该验证失败
        assertFalse(PasswordUtil.verifyPassword("wrong_password", hashedPassword));
    }

    @Test
    void verifyDifferentHashesForSamePassword() {
        // 同一个密码，每次加密应该生成不同的哈希
        String password = "123456";
        String hash1 = PasswordUtil.hashPassword(password);
        String hash2 = PasswordUtil.hashPassword(password);
        
        // 哈希值应该不同
        assertNotEquals(hash1, hash2);
        
        // 但都能验证通过
        assertTrue(PasswordUtil.verifyPassword(password, hash1));
        assertTrue(PasswordUtil.verifyPassword(password, hash2));
    }

    @Test
    void verifyPasswordLength() {
        // 测试生成的哈希长度是否符合BCrypt标准
        String password = "simple_password";
        String hashedPassword = PasswordUtil.hashPassword(password);
        
        // BCrypt哈希的长度应该是60个字符
        assertEquals(60, hashedPassword.length());
    }
} 