package com.yiyi.ai_train_playground.util;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

/**
 * ResolveUtil手动测试类
 * 演示如何使用ResolveUtil解析HTML并提取图片URL
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
public class ResolveUtilManualTest {
    
    public static void main(String[] args) {
        System.out.println("=== ResolveUtil HTML解析工具演示 ===\n");
        
        // 测试1: 基本HTML解析
        testBasicHtmlParsing();
        
        // 测试2: 您提供的HTML格式
        testYourHtmlFormat();
        
        // 测试3: CSS选择器
        testCssSelector();
        
        // 测试4: 过滤功能
        testFiltering();
        
        // 测试5: 读取文件
        testReadHtmlFile();
    }
    
    private static void testBasicHtmlParsing() {
        System.out.println("1. 基本HTML解析测试");
        System.out.println("==================");
        
        String html = """
            <div>
                <img src="http://example.com/1.jpg" alt="图片1"/>
                <img src="//img11.360buyimg.com/devfe/jfs/t19249/266/555939662/10324/447efd03/5a965eb2Nf83fd68c.jpg" alt="京东图片"/>
                <img src="https://example.com/2.png"/>
                <p>一些文本</p>
                <img src="relative/path/3.gif"/>
            </div>
            """;
        
        List<String> imageUrls = ResolveUtil.extractImageSources(html);
        
        System.out.println("输入HTML:");
        System.out.println(html);
        System.out.println("提取结果 (共" + imageUrls.size() + "个):");
        printJsonFormat(imageUrls);
        System.out.println();
    }
    
    private static void testYourHtmlFormat() {
        System.out.println("2. 您提供的HTML格式测试");
        System.out.println("========================");
        
        String yourHtml = """
            <div data-tab="item">
                <div class="module-title"> 商品详情 </div>
                <div class="goods-base">
                    <div class="item">
                        <div class="flex-center">
                            <div class="name">品牌</div>
                        </div>
                        <div class="adaptive">
                            <div class="text">
                                <a href="//list.jd.com/list.html?cat=670,686,689&tid=40161&ev=exbrand_90095">阿米洛（Varmilo）</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="img-text-warp">
                    <div id="img-text">
                        <div class="wrap-scale">
                            <div id="quality-life" class="quality-life">
                                <div class="q-logo">
                                    <img src="//img11.360buyimg.com/devfe/jfs/t19249/266/555939662/10324/447efd03/5a965eb2Nf83fd68c.jpg" alt="品质生活"/>
                                </div>
                            </div>
                        </div>
                        <div class="detail-content clearfix">
                            <div class="detail-content-wrap">
                                <div class="detail-content-item">
                                    <img src="//img1.360buyimg.com/product-detail/1.jpg"/>
                                    <img src="//img2.360buyimg.com/product-detail/2.png"/>
                                    <img src="//img3.360buyimg.com/product-detail/3.gif"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            """;
        
        List<String> imageUrls = ResolveUtil.extractImageSources(yourHtml);
        
        System.out.println("从您提供的HTML格式中提取的图片URL (共" + imageUrls.size() + "个):");
        printJsonFormat(imageUrls);
        System.out.println();
    }
    
    private static void testCssSelector() {
        System.out.println("3. CSS选择器测试");
        System.out.println("================");
        
        String html = """
            <div class="header">
                <img src="//header.360buyimg.com/logo.png"/>
            </div>
            <div class="detail-content">
                <img src="//detail1.360buyimg.com/image1.jpg"/>
                <img src="//detail2.360buyimg.com/image2.png"/>
            </div>
            <div class="footer">
                <img src="//footer.360buyimg.com/icon.gif"/>
            </div>
            """;
        
        // 提取所有图片
        List<String> allImages = ResolveUtil.extractImageSources(html);
        System.out.println("所有图片 (共" + allImages.size() + "个):");
        printJsonFormat(allImages);
        
        // 只提取详情区域的图片
        List<String> detailImages = ResolveUtil.extractImageSourcesBySelector(html, "div.detail-content img");
        System.out.println("详情区域图片 (共" + detailImages.size() + "个):");
        printJsonFormat(detailImages);
        System.out.println();
    }
    
    private static void testFiltering() {
        System.out.println("4. 过滤功能测试");
        System.out.println("==============");
        
        String html = """
            <div>
                <img src="http://example.com/1.jpg"/>
                <img src=""/>
                <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="/>
                <img src="https://example.com/2.jpg"/>
                <img/>
            </div>
            """;
        
        // 不过滤
        List<String> allResults = ResolveUtil.extractImageSources(html, false, false);
        System.out.println("不过滤 (共" + allResults.size() + "个):");
        printJsonFormat(allResults);
        
        // 过滤空值和data URL
        List<String> filteredResults = ResolveUtil.extractImageSources(html, true, true);
        System.out.println("过滤空值和data URL (共" + filteredResults.size() + "个):");
        printJsonFormat(filteredResults);
        System.out.println();
    }
    
    private static void testReadHtmlFile() {
        System.out.println("5. 读取HTML文件测试");
        System.out.println("==================");
        
        try {
            String filePath = "test_resolve_util.html";
            if (Files.exists(Paths.get(filePath))) {
                String htmlContent = Files.readString(Paths.get(filePath));
                List<String> imageUrls = ResolveUtil.extractImageSources(htmlContent);
                
                System.out.println("从文件 " + filePath + " 中提取的图片URL (共" + imageUrls.size() + "个):");
                printJsonFormat(imageUrls);
            } else {
                System.out.println("测试文件不存在: " + filePath);
            }
        } catch (IOException e) {
            System.out.println("读取文件时发生错误: " + e.getMessage());
        }
        System.out.println();
    }
    
    private static void printJsonFormat(List<String> urls) {
        System.out.println("[");
        for (int i = 0; i < urls.size(); i++) {
            String comma = i < urls.size() - 1 ? "," : "";
            System.out.println("  \"" + urls.get(i) + "\"" + comma);
        }
        System.out.println("]");
    }
} 