package com.yiyi.ai_train_playground.util;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.ArrayList;
import java.util.List;

/**
 * 简化版ResolveUtil演示程序
 * 不依赖Spring Boot，直接使用Jsoup
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
public class SimpleResolveUtilDemo {
    
    public static void main(String[] args) {
        System.out.println("=== ResolveUtil HTML解析工具演示 ===\n");
        
        // 测试1: 基本HTML解析
        testBasicHtmlParsing();
        
        // 测试2: 您提供的HTML格式
        testYourHtmlFormat();
        
        // 测试3: CSS选择器
        testCssSelector();
    }
    
    private static void testBasicHtmlParsing() {
        System.out.println("1. 基本HTML解析测试");
        System.out.println("==================");
        
        String html = """
            <div>
                <img src="http://example.com/1.jpg" alt="图片1"/>
                <img src="//img11.360buyimg.com/devfe/jfs/t19249/266/555939662/10324/447efd03/5a965eb2Nf83fd68c.jpg" alt="京东图片"/>
                <img src="https://example.com/2.png"/>
                <p>一些文本</p>
                <img src="relative/path/3.gif"/>
            </div>
            """;
        
        List<String> imageUrls = extractImageSources(html);
        
        System.out.println("输入HTML:");
        System.out.println(html);
        System.out.println("提取结果 (共" + imageUrls.size() + "个):");
        printJsonFormat(imageUrls);
        System.out.println();
    }
    
    private static void testYourHtmlFormat() {
        System.out.println("2. 您提供的HTML格式测试");
        System.out.println("========================");
        
        String yourHtml = """
            <div data-tab="item">
                <div class="module-title"> 商品详情 </div>
                <div class="goods-base">
                    <div class="item">
                        <div class="flex-center">
                            <div class="name">品牌</div>
                        </div>
                        <div class="adaptive">
                            <div class="text">
                                <a href="//list.jd.com/list.html?cat=670,686,689&tid=40161&ev=exbrand_90095">阿米洛（Varmilo）</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="img-text-warp">
                    <div id="img-text">
                        <div class="wrap-scale">
                            <div id="quality-life" class="quality-life">
                                <div class="q-logo">
                                    <img src="//img11.360buyimg.com/devfe/jfs/t19249/266/555939662/10324/447efd03/5a965eb2Nf83fd68c.jpg" alt="品质生活"/>
                                </div>
                            </div>
                        </div>
                        <div class="detail-content clearfix">
                            <div class="detail-content-wrap">
                                <div class="detail-content-item">
                                    <img src="//img1.360buyimg.com/product-detail/1.jpg"/>
                                    <img src="//img2.360buyimg.com/product-detail/2.png"/>
                                    <img src="//img3.360buyimg.com/product-detail/3.gif"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            """;
        
        List<String> imageUrls = extractImageSources(yourHtml);
        
        System.out.println("从您提供的HTML格式中提取的图片URL (共" + imageUrls.size() + "个):");
        printJsonFormat(imageUrls);
        System.out.println();
    }
    
    private static void testCssSelector() {
        System.out.println("3. CSS选择器测试");
        System.out.println("================");
        
        String html = """
            <div class="header">
                <img src="//header.360buyimg.com/logo.png"/>
            </div>
            <div class="detail-content">
                <img src="//detail1.360buyimg.com/image1.jpg"/>
                <img src="//detail2.360buyimg.com/image2.png"/>
            </div>
            <div class="footer">
                <img src="//footer.360buyimg.com/icon.gif"/>
            </div>
            """;
        
        // 提取所有图片
        List<String> allImages = extractImageSources(html);
        System.out.println("所有图片 (共" + allImages.size() + "个):");
        printJsonFormat(allImages);
        
        // 只提取详情区域的图片
        List<String> detailImages = extractImageSourcesBySelector(html, "div.detail-content img");
        System.out.println("详情区域图片 (共" + detailImages.size() + "个):");
        printJsonFormat(detailImages);
        System.out.println();
    }
    
    /**
     * 从HTML内容中提取所有img标签的src属性
     */
    public static List<String> extractImageSources(String htmlContent) {
        List<String> imageSources = new ArrayList<>();
        
        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            System.out.println("警告: HTML内容为空，返回空列表");
            return imageSources;
        }
        
        try {
            // 使用Jsoup解析HTML
            Document document = Jsoup.parse(htmlContent);
            
            // 选择所有img标签
            Elements imgElements = document.select("img");
            
            System.out.println("调试: 找到 " + imgElements.size() + " 个img标签");
            
            // 按顺序提取src属性
            for (Element img : imgElements) {
                String src = img.attr("src");
                
                if (src != null && !src.trim().isEmpty()) {
                    // 处理相对URL，添加协议
                    String processedSrc = processImageUrl(src);
                    imageSources.add(processedSrc);
                    System.out.println("调试: 提取到图片URL: " + processedSrc);
                } else {
                    System.out.println("调试: 跳过空的src属性的img标签");
                }
            }
            
            System.out.println("信息: 成功提取到 " + imageSources.size() + " 个图片URL");
            
        } catch (Exception e) {
            System.out.println("错误: 解析HTML时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
        
        return imageSources;
    }
    
    /**
     * 从HTML中提取指定CSS选择器的img标签src属性
     */
    public static List<String> extractImageSourcesBySelector(String htmlContent, String cssSelector) {
        List<String> imageSources = new ArrayList<>();
        
        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            System.out.println("警告: HTML内容为空，返回空列表");
            return imageSources;
        }
        
        if (cssSelector == null || cssSelector.trim().isEmpty()) {
            System.out.println("警告: CSS选择器为空，使用默认的img选择器");
            return extractImageSources(htmlContent);
        }
        
        try {
            Document document = Jsoup.parse(htmlContent);
            Elements imgElements = document.select(cssSelector);
            
            System.out.println("调试: 使用选择器 '" + cssSelector + "' 找到 " + imgElements.size() + " 个元素");
            
            for (Element img : imgElements) {
                String src = img.attr("src");
                if (src != null && !src.trim().isEmpty()) {
                    String processedSrc = processImageUrl(src);
                    imageSources.add(processedSrc);
                }
            }
            
            System.out.println("信息: 通过选择器成功提取到 " + imageSources.size() + " 个图片URL");
            
        } catch (Exception e) {
            System.out.println("错误: 使用CSS选择器解析HTML时发生异常: " + e.getMessage());
        }
        
        return imageSources;
    }
    
    /**
     * 处理图片URL，确保格式正确
     */
    private static String processImageUrl(String src) {
        if (src == null || src.trim().isEmpty()) {
            return src;
        }
        
        String trimmedSrc = src.trim();
        
        // 如果是相对URL且以//开头，添加https协议
        if (trimmedSrc.startsWith("//")) {
            return "https:" + trimmedSrc;
        }
        
        // 如果是相对路径，这里可以根据需要添加基础URL
        // 目前直接返回原值
        return trimmedSrc;
    }
    
    private static void printJsonFormat(List<String> urls) {
        System.out.println("[");
        for (int i = 0; i < urls.size(); i++) {
            String comma = i < urls.size() - 1 ? "," : "";
            System.out.println("  \"" + urls.get(i) + "\"" + comma);
        }
        System.out.println("]");
    }
} 