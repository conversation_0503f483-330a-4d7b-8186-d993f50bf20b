package com.yiyi.ai_train_playground.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MD5工具类测试
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Slf4j
class Md5UtilTest {
    
    @Test
    @DisplayName("测试生成MD5摘要")
    void testGenerateMd5() {
        // 测试正常字符串
        String input = "Hello World";
        String md5 = Md5Util.generateMd5(input);
        
        assertNotNull(md5);
        assertEquals(32, md5.length());
        assertEquals("b10a8db164e0754105b7a99be72e3fe5", md5);
        
        log.info("输入: {}, MD5: {}", input, md5);
        
        // 测试中文字符串
        String chineseInput = "你好世界";
        String chineseMd5 = Md5Util.generateMd5(chineseInput);
        
        assertNotNull(chineseMd5);
        assertEquals(32, chineseMd5.length());
        
        log.info("中文输入: {}, MD5: {}", chineseInput, chineseMd5);
        
        // 测试空字符串
        String emptyMd5 = Md5Util.generateMd5("");
        assertNull(emptyMd5);
        
        // 测试null
        String nullMd5 = Md5Util.generateMd5(null);
        assertNull(nullMd5);
    }
    
    @Test
    @DisplayName("测试验证MD5摘要")
    void testVerifyMd5() {
        String input = "Hello World";
        String correctMd5 = "b10a8db164e0754105b7a99be72e3fe5";
        String wrongMd5 = "wrong_md5_hash";
        
        // 测试正确的MD5
        assertTrue(Md5Util.verifyMd5(input, correctMd5));
        
        // 测试错误的MD5
        assertFalse(Md5Util.verifyMd5(input, wrongMd5));
        
        // 测试null值
        assertFalse(Md5Util.verifyMd5(null, correctMd5));
        assertFalse(Md5Util.verifyMd5(input, null));
        assertFalse(Md5Util.verifyMd5(null, null));
    }
    
    @Test
    @DisplayName("测试比较MD5摘要")
    void testCompareMd5() {
        String md5_1 = "b10a8db164e0754105b7a99be72e3fe5";
        String md5_2 = "B10A8DB164E0754105B7A99BE72E3FE5"; // 大写
        String md5_3 = "different_md5_hash";
        
        // 测试相同MD5（忽略大小写）
        assertTrue(Md5Util.compareMd5(md5_1, md5_2));
        
        // 测试不同MD5
        assertFalse(Md5Util.compareMd5(md5_1, md5_3));
        
        // 测试null值
        assertTrue(Md5Util.compareMd5(null, null));
        assertFalse(Md5Util.compareMd5(md5_1, null));
        assertFalse(Md5Util.compareMd5(null, md5_1));
    }
    
    @Test
    @DisplayName("测试HTML内容的MD5生成")
    void testHtmlContentMd5() {
        String htmlContent = "<div><h1>商品标题</h1><p>商品描述</p><img src='http://example.com/image.jpg'/></div>";
        String md5 = Md5Util.generateMd5(htmlContent);
        
        assertNotNull(md5);
        assertEquals(32, md5.length());
        
        log.info("HTML内容长度: {}, MD5: {}", htmlContent.length(), md5);
        
        // 测试相同内容生成相同MD5
        String md5_2 = Md5Util.generateMd5(htmlContent);
        assertEquals(md5, md5_2);
        
        // 测试不同内容生成不同MD5
        String differentHtml = htmlContent + " ";
        String differentMd5 = Md5Util.generateMd5(differentHtml);
        assertNotEquals(md5, differentMd5);
    }
}
