package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.dto.SmsCodeResponse;
import com.yiyi.ai_train_playground.dto.SmsLoginResponse;
import com.yiyi.ai_train_playground.entity.TrainTeam;
import com.yiyi.ai_train_playground.entity.User;
import com.yiyi.ai_train_playground.mapper.TrainTeamMapper;
import com.yiyi.ai_train_playground.mapper.UserMapper;
import com.yiyi.ai_train_playground.service.sms.impl.SmsServiceImpl;
import com.yiyi.ai_train_playground.util.JwtUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 短信验证码服务测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@ExtendWith(MockitoExtension.class)
class SmsServiceTest {

    @Mock
    private StringRedisTemplate stringRedisTemplate;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @Mock
    private UserMapper userMapper;

    @Mock
    private TrainTeamMapper trainTeamMapper;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private JwtUtil jwtUtil;

    @InjectMocks
    private SmsServiceImpl smsService;

    @BeforeEach
    void setUp() {
        lenient().when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    void testSendVerificationCode_Success() {
        // Given
        String phone = "13800138000";
        when(stringRedisTemplate.hasKey(anyString())).thenReturn(false);

        // When
        SmsCodeResponse response = smsService.sendVerificationCode(phone);

        // Then
        assertNotNull(response);
        assertNotNull(response.getVerificationKey());
        assertEquals(32, response.getVerificationKey().length());
        verify(valueOperations, times(2)).set(anyString(), anyString(), any(Duration.class));
    }

    @Test
    void testSendVerificationCode_RateLimited() {
        // Given
        String phone = "13800138000";
        when(stringRedisTemplate.hasKey(anyString())).thenReturn(true);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            smsService.sendVerificationCode(phone);
        });
        assertEquals("发送过于频繁，请1分钟后再试", exception.getMessage());
    }

    @Test
    void testVerifyCode_Success() {
        // Given
        String verificationKey = "test-key";
        String verificationCode = "1234";
        String phone = "13800138000";
        String storedValue = phone + ":" + verificationCode;
        
        when(valueOperations.get(anyString())).thenReturn(storedValue);

        // When
        boolean result = smsService.verifyCode(verificationKey, verificationCode);

        // Then
        assertTrue(result);
        verify(stringRedisTemplate).delete(anyString());
    }

    @Test
    void testVerifyCode_InvalidCode() {
        // Given
        String verificationKey = "test-key";
        String verificationCode = "1234";
        String wrongCode = "5678";
        String phone = "13800138000";
        String storedValue = phone + ":" + verificationCode;
        
        when(valueOperations.get(anyString())).thenReturn(storedValue);

        // When
        boolean result = smsService.verifyCode(verificationKey, wrongCode);

        // Then
        assertFalse(result);
        verify(stringRedisTemplate, never()).delete(anyString());
    }

    @Test
    void testVerifyCode_ExpiredCode() {
        // Given
        String verificationKey = "test-key";
        String verificationCode = "1234";
        
        when(valueOperations.get(anyString())).thenReturn(null);

        // When
        boolean result = smsService.verifyCode(verificationKey, verificationCode);

        // Then
        assertFalse(result);
    }

    @Test
    void testSmsLogin_ExistingUser() {
        // Given
        String verificationKey = "test-key";
        String verificationCode = "1234";
        String phone = "13800138000";
        String storedValue = phone + ":" + verificationCode;
        
        User existingUser = new User();
        existingUser.setId(1L);
        existingUser.setUsername("existing_user");
        existingUser.setMobile(phone);
        existingUser.setDisplayName("现有用户");
        existingUser.setTeamId(1L);
        
        when(valueOperations.get(anyString())).thenReturn(storedValue);
        when(userMapper.findByMobile(phone)).thenReturn(existingUser);
        when(jwtUtil.generateToken(anyLong(), anyString(), anyLong(), anyBoolean())).thenReturn("test-token");

        // When
        SmsLoginResponse response = smsService.smsLogin(verificationKey, verificationCode, null);

        // Then
        assertNotNull(response);
        assertEquals(existingUser.getId(), response.getUserId());
        assertEquals(existingUser.getUsername(), response.getUsername());
        assertEquals(existingUser.getDisplayName(), response.getDisplayName());
        assertEquals(existingUser.getMobile(), response.getMobile());
        assertEquals("test-token", response.getToken());
        
        verify(stringRedisTemplate).delete(anyString());
        verify(trainTeamMapper, never()).insert(any(TrainTeam.class));
        verify(userMapper, never()).insert(any(User.class));
    }

    @Test
    void testSmsLogin_NewUser() {
        // Given
        String verificationKey = "test-key";
        String verificationCode = "1234";
        String phone = "13800138000";
        String storedValue = phone + ":" + verificationCode;
        
        TrainTeam team = new TrainTeam();
        team.setId(1L);
        
        User newUser = new User();
        newUser.setId(1L);
        
        when(valueOperations.get(anyString())).thenReturn(storedValue);
        when(userMapper.findByMobile(phone)).thenReturn(null);
        when(userMapper.findByUsername(anyString())).thenReturn(null);
        when(trainTeamMapper.insert(any(TrainTeam.class))).thenAnswer(invocation -> {
            TrainTeam t = invocation.getArgument(0);
            t.setId(1L);
            return 1;
        });
        when(userMapper.insert(any(User.class))).thenAnswer(invocation -> {
            User u = invocation.getArgument(0);
            u.setId(1L);
            return 1;
        });
        when(passwordEncoder.encode("123456")).thenReturn("encoded-password");
        when(jwtUtil.generateToken(anyLong(), anyString(), anyLong(), anyBoolean())).thenReturn("test-token");

        // When
        SmsLoginResponse response = smsService.smsLogin(verificationKey, verificationCode, null);

        // Then
        assertNotNull(response);
        assertEquals(1L, response.getUserId());
        assertNotNull(response.getUsername());
        assertTrue(response.getUsername().startsWith("user_"));
        assertEquals("手机用户", response.getDisplayName());
        assertEquals(phone, response.getMobile());
        assertEquals("test-token", response.getToken());
        
        verify(stringRedisTemplate).delete(anyString());
        verify(trainTeamMapper).insert(any(TrainTeam.class));
        verify(userMapper).insert(any(User.class));
    }

    @Test
    void testSmsLogin_InvalidCode() {
        // Given
        String verificationKey = "test-key";
        String verificationCode = "1234";
        String wrongCode = "5678";
        String phone = "13800138000";
        String storedValue = phone + ":" + verificationCode;
        
        when(valueOperations.get(anyString())).thenReturn(storedValue);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            smsService.smsLogin(verificationKey, wrongCode, null);
        });
        assertEquals("验证码错误", exception.getMessage());
    }

    @Test
    void testSmsLogin_ExpiredCode() {
        // Given
        String verificationKey = "test-key";
        String verificationCode = "1234";
        
        when(valueOperations.get(anyString())).thenReturn(null);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            smsService.smsLogin(verificationKey, verificationCode, null);
        });
        assertEquals("验证码不存在或已过期", exception.getMessage());
    }
} 