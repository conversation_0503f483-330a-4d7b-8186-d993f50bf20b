package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.dto.ScriptUpdateRequest;
import com.yiyi.ai_train_playground.entity.TrainScript;
import com.yiyi.ai_train_playground.mapper.TrainScriptMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 剧本更新服务测试类
 *
 * <AUTHOR> Assistant
 * @since 2025-07-03
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TrainScriptUpdateServiceTest {

    @Autowired
    private TrainScriptService trainScriptService;

    @Autowired
    private TrainScriptMapper trainScriptMapper;

    private static final Long DEFAULT_TEAM_ID = 1L;
    private static final String DEFAULT_UPDATER = "test_updater";

    @Test
    public void testUpdateScriptWithRelatedData_Success() {
        // 1. 先创建一个剧本用于测试更新
        TrainScript originalScript = new TrainScript();
        originalScript.setName("原始剧本");
        originalScript.setGenerationType(0);
        originalScript.setGroupId(1L);
        originalScript.setIntentId(1L);
        originalScript.setEvaluationId(1L);
        originalScript.setBuyerRequirement("原始需求");
        originalScript.setOrderIsRemarked(0);
        originalScript.setOrderPriority(1);
        originalScript.setOrderRemark("原始备注");
        originalScript.setSimulationTool("原始工具");
        originalScript.setRetryBuyerRequirementCounts(0);
        originalScript.setRetryFlowNodeCounts(0);
        originalScript.setVersion(1L);
        originalScript.setIsOfficial(false);
        originalScript.setTeamId(DEFAULT_TEAM_ID);
        originalScript.setCreator(DEFAULT_UPDATER);
        originalScript.setUpdater(DEFAULT_UPDATER);

        int insertResult = trainScriptMapper.insert(originalScript);
        assertTrue(insertResult > 0, "创建原始剧本失败");
        assertNotNull(originalScript.getId(), "剧本ID应该不为空");

        Long scriptId = originalScript.getId();
        log.info("创建的剧本ID: {}", scriptId);

        // 2. 准备更新请求
        ScriptUpdateRequest updateRequest = new ScriptUpdateRequest();
        updateRequest.setId(scriptId);
        updateRequest.setName("更新后的剧本");
        updateRequest.setGenerationTypeCode(1);
        updateRequest.setGroupId(2L);
        updateRequest.setIntentId(2L);
        updateRequest.setEvaluationPlanId(2L);
        updateRequest.setBuyerRequirement("更新后的需求");
        updateRequest.setOrderIsRemarked(1);
        updateRequest.setOrderPriority(2);
        updateRequest.setOrderRemark("更新后的备注");
        updateRequest.setRetryBuyerRequirementCounts(0);
        updateRequest.setRetryFlowNodeCounts(0);
        updateRequest.setSimulationTool("更新后的工具");
        updateRequest.setVersion(1L);
        updateRequest.setIsOfficial(false);

        // 3. 准备商品列表
        List<ScriptUpdateRequest.ProductUpdateDTO> productList = new ArrayList<>();
        ScriptUpdateRequest.ProductUpdateDTO product = new ScriptUpdateRequest.ProductUpdateDTO();
        product.setId(1L);
        product.setExternalProductId("update_product_001");
        product.setExternalProductName("更新商品1");
        product.setExternalProductLink("http://update.example.com/product1");
        product.setExternalProductImage("http://update.example.com/image1.jpg");
        productList.add(product);
        updateRequest.setProductList(productList);

        // 4. 准备关联图片列表
        List<ScriptUpdateRequest.RelatedImageUpdateDTO> relateImgs = new ArrayList<>();
        ScriptUpdateRequest.RelatedImageUpdateDTO image = new ScriptUpdateRequest.RelatedImageUpdateDTO();
        image.setId(1L);
        image.setRecognizedText("更新图片识别文本");
        image.setUploadType(1);
        image.setMediaType(1);
        image.setUrl("http://update.example.com/image.jpg");
        relateImgs.add(image);
        updateRequest.setRelateImgs(relateImgs);

        // 5. 准备流程节点列表
        List<ScriptUpdateRequest.FlowNodeUpdateDTO> flowNodes = new ArrayList<>();
        ScriptUpdateRequest.FlowNodeUpdateDTO node = new ScriptUpdateRequest.FlowNodeUpdateDTO();
        node.setId(1L);
        node.setNodeName("更新节点1");
        node.setNodeBuyerRequirement("更新节点需求1");
        flowNodes.add(node);
        updateRequest.setFlowNodes(flowNodes);

        // 6. 执行更新
        boolean result = trainScriptService.updateScriptWithRelatedData(updateRequest, DEFAULT_TEAM_ID, DEFAULT_UPDATER);
        assertTrue(result, "更新剧本应该成功");

        // 7. 验证更新结果
        TrainScript updatedScript = trainScriptMapper.selectById(scriptId, DEFAULT_TEAM_ID);
        assertNotNull(updatedScript, "更新后的剧本应该存在");
        assertEquals("更新后的剧本", updatedScript.getName(), "剧本名称应该已更新");
        assertEquals(Integer.valueOf(1), updatedScript.getGenerationType(), "生成类型应该已更新");
        assertEquals(Long.valueOf(2), updatedScript.getGroupId(), "分组ID应该已更新");
        assertEquals("更新后的需求", updatedScript.getBuyerRequirement(), "买家需求应该已更新");

        log.info("剧本更新测试完成");
    }

    @Test
    public void testUpdateScriptWithRelatedData_NullProductList() {
        // 测试商品列表为null的情况
        ScriptUpdateRequest updateRequest = new ScriptUpdateRequest();
        updateRequest.setId(999L); // 使用不存在的ID
        updateRequest.setName("测试剧本");
        updateRequest.setGenerationTypeCode(0);
        updateRequest.setProductList(null);
        updateRequest.setRelateImgs(null);
        updateRequest.setFlowNodes(null);

        // 这个测试预期会失败，因为剧本ID不存在
        assertThrows(RuntimeException.class, () -> {
            trainScriptService.updateScriptWithRelatedData(updateRequest, DEFAULT_TEAM_ID, DEFAULT_UPDATER);
        }, "更新不存在的剧本应该抛出异常");
    }

    @Test
    public void testUpdateScriptWithRelatedData_EmptyLists() {
        // 1. 先创建一个剧本
        TrainScript originalScript = new TrainScript();
        originalScript.setName("空列表测试剧本");
        originalScript.setGenerationType(0);
        originalScript.setGroupId(1L);
        originalScript.setIntentId(1L);
        originalScript.setEvaluationId(1L);
        originalScript.setBuyerRequirement("测试需求");
        originalScript.setOrderIsRemarked(0);
        originalScript.setOrderPriority(1);
        originalScript.setOrderRemark("测试备注");
        originalScript.setSimulationTool("测试工具");
        originalScript.setRetryBuyerRequirementCounts(0);
        originalScript.setRetryFlowNodeCounts(0);
        originalScript.setVersion(1L);
        originalScript.setIsOfficial(false);
        originalScript.setTeamId(DEFAULT_TEAM_ID);
        originalScript.setCreator(DEFAULT_UPDATER);
        originalScript.setUpdater(DEFAULT_UPDATER);

        int insertResult = trainScriptMapper.insert(originalScript);
        assertTrue(insertResult > 0, "创建测试剧本失败");

        Long scriptId = originalScript.getId();

        // 2. 准备空列表的更新请求
        ScriptUpdateRequest updateRequest = new ScriptUpdateRequest();
        updateRequest.setId(scriptId);
        updateRequest.setName("更新后的空列表测试剧本");
        updateRequest.setGenerationTypeCode(1);
        updateRequest.setGroupId(2L);
        updateRequest.setIntentId(2L);
        updateRequest.setEvaluationPlanId(2L);
        updateRequest.setBuyerRequirement("更新后的测试需求");
        updateRequest.setOrderIsRemarked(1);
        updateRequest.setOrderPriority(2);
        updateRequest.setOrderRemark("更新后的测试备注");
        updateRequest.setRetryBuyerRequirementCounts(0);
        updateRequest.setRetryFlowNodeCounts(0);
        updateRequest.setSimulationTool("更新后的测试工具");
        updateRequest.setVersion(1L);
        updateRequest.setIsOfficial(false);
        updateRequest.setProductList(new ArrayList<>());
        updateRequest.setRelateImgs(new ArrayList<>());
        updateRequest.setFlowNodes(new ArrayList<>());

        // 3. 执行更新
        boolean result = trainScriptService.updateScriptWithRelatedData(updateRequest, DEFAULT_TEAM_ID, DEFAULT_UPDATER);
        assertTrue(result, "使用空列表更新剧本应该成功");

        // 4. 验证更新结果
        TrainScript updatedScript = trainScriptMapper.selectById(scriptId, DEFAULT_TEAM_ID);
        assertNotNull(updatedScript, "更新后的剧本应该存在");
        assertEquals("更新后的空列表测试剧本", updatedScript.getName(), "剧本名称应该已更新");

        log.info("空列表更新测试完成");
    }
}
