package com.yiyi.ai_train_playground.service.user.impl;

import com.yiyi.ai_train_playground.entity.User;
import com.yiyi.ai_train_playground.mapper.UserMapper;
import com.yiyi.ai_train_playground.service.user.UserService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * UserServiceImpl 单元测试
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-21
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
@Rollback(false)  // 禁用自动回滚，使测试数据能够提交到数据库
class UserServiceImplTest {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserMapper userMapper;
    
    private static final String TEST_USERNAME_PREFIX = "test_user_";
    private static final String TEST_MOBILE_PREFIX = "1380013";
    private static final String TEST_DISPLAY_NAME_PREFIX = "测试用户";
    private static final Long TEST_TEAM_ID = 1L;

    private Long testUserId; // 动态生成的测试用户ID
    private String testUsername; // 每次测试生成唯一的用户名

    @BeforeEach
    void setUp() {
        log.info("=== 开始设置测试数据 ===");

        // 生成唯一的测试用户名（使用时间戳）
        long timestamp = System.currentTimeMillis();
        testUsername = TEST_USERNAME_PREFIX + timestamp;

        // 创建测试用户
        User testUser = createTestUser(testUsername);

        int insertResult = userMapper.insert(testUser);
        assertThat(insertResult).isEqualTo(1);

        // 获取自动生成的用户ID
        testUserId = testUser.getId();
        assertThat(testUserId).isNotNull();

        log.info("创建测试用户成功: userId={}, username={}", testUserId, testUser.getUsername());
    }
    
    @Test
    @DisplayName("测试根据用户ID查询用户信息 - 成功场景")
    void testFindByUserId_Success() {
        log.info("=== 测试根据用户ID查询用户信息 - 成功场景 ===");

        // 执行查询
        User result = userService.findByUserId(testUserId);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(testUserId);
        assertThat(result.getUsername()).isEqualTo(testUsername);
        assertThat(result.getMobile()).isEqualTo(TEST_MOBILE_PREFIX + testUsername.substring(testUsername.lastIndexOf('_') + 1));
        assertThat(result.getDisplayName()).isEqualTo(TEST_DISPLAY_NAME_PREFIX + testUsername.substring(testUsername.lastIndexOf('_') + 1));
        assertThat(result.getTeamId()).isEqualTo(TEST_TEAM_ID);

        // 验证密码字段已被清除（安全考虑）
        assertThat(result.getPasswordHash()).isNull();

        log.info("查询用户成功: userId={}, username={}", result.getId(), result.getUsername());
    }
    
    @Test
    @DisplayName("测试根据用户ID查询用户信息 - 用户不存在")
    void testFindByUserId_UserNotFound() {
        log.info("=== 测试根据用户ID查询用户信息 - 用户不存在 ===");
        
        Long nonExistentUserId = 99999L;
        
        // 执行查询
        User result = userService.findByUserId(nonExistentUserId);
        
        // 验证结果
        assertThat(result).isNull();
        
        log.info("用户不存在测试通过: userId={}", nonExistentUserId);
    }
    
    @Test
    @DisplayName("测试根据用户ID查询用户信息 - 参数为null")
    void testFindByUserId_NullParameter() {
        log.info("=== 测试根据用户ID查询用户信息 - 参数为null ===");
        
        // 执行查询
        User result = userService.findByUserId(null);
        
        // 验证结果
        assertThat(result).isNull();
        
        log.info("null参数测试通过");
    }
    
    @Test
    @DisplayName("测试根据用户ID查询用户信息 - 数据库异常")
    void testFindByUserId_DatabaseException() {
        log.info("=== 测试根据用户ID查询用户信息 - 数据库异常 ===");
        
        // 使用一个可能导致数据库异常的用户ID（负数）
        Long invalidUserId = -1L;
        
        // 这里我们期望服务能正常处理，返回null或抛出合理的异常
        // 具体行为取决于数据库和MyBatis的配置
        try {
            User result = userService.findByUserId(invalidUserId);
            // 如果没有抛出异常，验证返回null
            assertThat(result).isNull();
            log.info("数据库异常处理测试通过: 返回null");
        } catch (RuntimeException e) {
            // 如果抛出了RuntimeException，验证异常信息
            assertThat(e.getMessage()).contains("查询用户失败");
            log.info("数据库异常处理测试通过: 抛出异常 - {}", e.getMessage());
        }
    }
    
    /**
     * 创建测试用的User对象
     */
    private User createTestUser(String username) {
        String timestamp = username.substring(username.lastIndexOf('_') + 1);
        User user = new User();
        user.setUsername(username);
        user.setPasswordHash("$2a$10$test.password.hash"); // 测试密码哈希
        user.setMobile(TEST_MOBILE_PREFIX + timestamp);
        user.setDisplayName(TEST_DISPLAY_NAME_PREFIX + timestamp);
        user.setTeamId(TEST_TEAM_ID);
        user.setFailedAttempts(0);
        user.setIsLocked(false);
        user.setCreator("test");
        user.setUpdater("test");
        return user;
    }
}
