package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.entity.TrainJdAccessToken;
import com.yiyi.ai_train_playground.service.jd.TrainJdAccessTokenService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * TrainJdAccessTokenService 测试类
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TrainJdAccessTokenServiceImplTest {

    @Autowired
    private TrainJdAccessTokenService trainJdAccessTokenService;

    private static final Long TEST_USER_ID = 1L;
    private static final Long TEST_TEAM_ID = 1L;
    private static final Long TEST_SHOP_ID = 12345L;
    private static final String TEST_XID = "test-xid-12345";

    @Test
    @DisplayName("测试根据店铺ID查询访问令牌")
    void testFindByShopId() {
        log.info("=== 测试根据店铺ID查询访问令牌 ===");

        // 1. 创建测试数据
        TrainJdAccessToken testToken = createTestToken();
        testToken.setShopId(TEST_SHOP_ID);
        
        // 2. 插入测试数据
        boolean insertResult = trainJdAccessTokenService.insert(testToken);
        assertThat(insertResult).isTrue();
        log.info("插入测试数据成功");

        // 3. 根据店铺ID查询
        TrainJdAccessToken foundToken = trainJdAccessTokenService.findByShopId(TEST_SHOP_ID);
        
        // 4. 验证结果
        assertThat(foundToken).isNotNull();
        assertThat(foundToken.getShopId()).isEqualTo(TEST_SHOP_ID);
        assertThat(foundToken.getXid()).isEqualTo(TEST_XID);
        assertThat(foundToken.getUserId()).isEqualTo(TEST_USER_ID);
        assertThat(foundToken.getTeamId()).isEqualTo(TEST_TEAM_ID);
        
        log.info("根据店铺ID查询成功: shopId={}, xid={}", foundToken.getShopId(), foundToken.getXid());
    }

    @Test
    @DisplayName("测试根据不存在的店铺ID查询")
    void testFindByShopIdNotFound() {
        log.info("=== 测试根据不存在的店铺ID查询 ===");

        Long nonExistentShopId = 99999L;
        TrainJdAccessToken foundToken = trainJdAccessTokenService.findByShopId(nonExistentShopId);
        
        assertThat(foundToken).isNull();
        log.info("根据不存在的店铺ID查询，正确返回null");
    }

    @Test
    @DisplayName("测试店铺ID为null的情况")
    void testFindByShopIdWithNull() {
        log.info("=== 测试店铺ID为null的情况 ===");

        TrainJdAccessToken foundToken = trainJdAccessTokenService.findByShopId(null);
        
        assertThat(foundToken).isNull();
        log.info("店铺ID为null时，正确返回null");
    }

    @Test
    @DisplayName("测试完整的CRUD操作")
    void testCompleteOperations() {
        log.info("=== 测试完整的CRUD操作 ===");

        // 1. 创建和插入
        TrainJdAccessToken testToken = createTestToken();
        testToken.setShopId(TEST_SHOP_ID);
        
        boolean insertResult = trainJdAccessTokenService.insert(testToken);
        assertThat(insertResult).isTrue();
        log.info("插入操作成功");

        // 2. 根据xid查询
        TrainJdAccessToken foundByXid = trainJdAccessTokenService.findByXid(TEST_XID);
        assertThat(foundByXid).isNotNull();
        assertThat(foundByXid.getShopId()).isEqualTo(TEST_SHOP_ID);
        log.info("根据xid查询成功");

        // 3. 根据店铺ID查询
        TrainJdAccessToken foundByShopId = trainJdAccessTokenService.findByShopId(TEST_SHOP_ID);
        assertThat(foundByShopId).isNotNull();
        assertThat(foundByShopId.getXid()).isEqualTo(TEST_XID);
        log.info("根据店铺ID查询成功");

        // 4. 根据用户ID和团队ID查询
        TrainJdAccessToken foundByUserAndTeam = trainJdAccessTokenService.findByUserIdAndTeamId(TEST_USER_ID, TEST_TEAM_ID);
        assertThat(foundByUserAndTeam).isNotNull();
        assertThat(foundByUserAndTeam.getShopId()).isEqualTo(TEST_SHOP_ID);
        log.info("根据用户ID和团队ID查询成功");

        // 5. 更新操作
        foundByXid.setAccessToken("updated-access-token");
        foundByXid.setUpdater("test-updater");
        boolean updateResult = trainJdAccessTokenService.updateByXid(foundByXid);
        assertThat(updateResult).isTrue();
        log.info("更新操作成功");

        // 6. 验证更新结果
        TrainJdAccessToken updatedToken = trainJdAccessTokenService.findByShopId(TEST_SHOP_ID);
        assertThat(updatedToken).isNotNull();
        assertThat(updatedToken.getAccessToken()).isEqualTo("updated-access-token");
        assertThat(updatedToken.getUpdater()).isEqualTo("test-updater");
        log.info("更新结果验证成功");
    }

    @Test
    @DisplayName("测试saveOrUpdate方法")
    void testSaveOrUpdate() {
        log.info("=== 测试saveOrUpdate方法 ===");

        // 1. 测试插入新记录
        TrainJdAccessToken newToken = createTestToken();
        newToken.setShopId(TEST_SHOP_ID);
        
        boolean saveResult = trainJdAccessTokenService.saveOrUpdate(newToken);
        assertThat(saveResult).isTrue();
        log.info("saveOrUpdate插入新记录成功");

        // 2. 验证记录已存在
        TrainJdAccessToken foundToken = trainJdAccessTokenService.findByShopId(TEST_SHOP_ID);
        assertThat(foundToken).isNotNull();
        assertThat(foundToken.getAccessToken()).isEqualTo("test-access-token");

        // 3. 测试更新现有记录
        newToken.setAccessToken("updated-by-save-or-update");
        newToken.setUpdater("save-or-update-test");
        
        boolean updateResult = trainJdAccessTokenService.saveOrUpdate(newToken);
        assertThat(updateResult).isTrue();
        log.info("saveOrUpdate更新现有记录成功");

        // 4. 验证更新结果
        TrainJdAccessToken updatedToken = trainJdAccessTokenService.findByShopId(TEST_SHOP_ID);
        assertThat(updatedToken).isNotNull();
        assertThat(updatedToken.getAccessToken()).isEqualTo("updated-by-save-or-update");
        log.info("saveOrUpdate更新结果验证成功");
    }

    /**
     * 创建测试用的TrainJdAccessToken对象
     */
    private TrainJdAccessToken createTestToken() {
        TrainJdAccessToken token = new TrainJdAccessToken();
        token.setUserId(TEST_USER_ID);
        token.setTeamId(TEST_TEAM_ID);
        token.setAccessToken("test-access-token");
        token.setExpiresTime(LocalDateTime.now().plusHours(2));
        token.setRefreshToken("test-refresh-token");
        token.setScope("read,write");
        token.setXid(TEST_XID);
        token.setIsAuthorize(1);
        token.setIsSyncComplete(0);
        token.setCreator("test-creator");
        token.setUpdater("test-updater");
        return token;
    }
}
