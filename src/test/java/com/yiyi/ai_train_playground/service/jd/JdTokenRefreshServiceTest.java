package com.yiyi.ai_train_playground.service.jd;

import com.yiyi.ai_train_playground.config.JdConfig;
import com.yiyi.ai_train_playground.entity.TrainJdAccessToken;
import com.yiyi.ai_train_playground.mapper.TrainJdAccessTokenMapper;
import com.yiyi.ai_train_playground.service.jd.impl.JdTokenRefreshServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * 京东Token刷新服务测试
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@ExtendWith(MockitoExtension.class)
@SuppressWarnings({"unchecked", "rawtypes"})
public class JdTokenRefreshServiceTest {
    
    @Mock
    private TrainJdAccessTokenMapper trainJdAccessTokenMapper;
    
    @Mock
    private WebClient.Builder webClientBuilder;
    
    @Mock
    private WebClient webClient;
    
    @Mock
    private WebClient.RequestHeadersUriSpec requestHeadersUriSpec;

    @Mock
    private WebClient.ResponseSpec responseSpec;
    
    @Mock
    private JdConfig jdConfig;
    
    @InjectMocks
    private JdTokenRefreshServiceImpl jdTokenRefreshService;
    
    @BeforeEach
    void setUp() {
        // 这些配置在所有测试中都会用到
        lenient().when(jdConfig.getAppKey()).thenReturn("test-app-key");
        lenient().when(jdConfig.getAppSecret()).thenReturn("test-app-secret");
        lenient().when(jdConfig.getRefreshTokenUrl()).thenReturn("https://open-oauth.jd.com/oauth2/refresh_token");
    }
    
    @Test
    void testRefreshAllTokens_EmptyList() {
        // Given
        when(trainJdAccessTokenMapper.findAll()).thenReturn(Arrays.asList());
        
        // When
        int result = jdTokenRefreshService.refreshAllTokens();
        
        // Then
        assertEquals(0, result);
        verify(trainJdAccessTokenMapper).findAll();
        verifyNoMoreInteractions(trainJdAccessTokenMapper);
    }
    
    @Test
    void testRefreshAllTokens_Success() {
        // Given
        TrainJdAccessToken token1 = createTestToken(1L, "refresh-token-1");
        TrainJdAccessToken token2 = createTestToken(2L, "refresh-token-2");
        List<TrainJdAccessToken> tokens = Arrays.asList(token1, token2);
        
        when(trainJdAccessTokenMapper.findAll()).thenReturn(tokens);
        
        // Mock WebClient chain
        when(webClientBuilder.build()).thenReturn(webClient);
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        
        // Mock successful API response
        Map<String, Object> successResponse = new HashMap<>();
        successResponse.put("access_token", "new-access-token");
        successResponse.put("expires_in", 7200);
        successResponse.put("refresh_token", "new-refresh-token");
        successResponse.put("scope", "read");
        
        when(responseSpec.bodyToMono(any(ParameterizedTypeReference.class))).thenReturn(Mono.just(successResponse));
        when(trainJdAccessTokenMapper.updateTokenInfo(any(TrainJdAccessToken.class))).thenReturn(1);
        
        // When
        int result = jdTokenRefreshService.refreshAllTokens();
        
        // Then
        assertEquals(2, result);
        verify(trainJdAccessTokenMapper).findAll();
        verify(trainJdAccessTokenMapper, times(2)).updateTokenInfo(any(TrainJdAccessToken.class));
    }
    
    @Test
    void testRefreshAllTokens_PartialFailure() {
        // Given
        TrainJdAccessToken token1 = createTestToken(1L, "refresh-token-1");
        TrainJdAccessToken token2 = createTestToken(2L, "refresh-token-2");
        List<TrainJdAccessToken> tokens = Arrays.asList(token1, token2);
        
        when(trainJdAccessTokenMapper.findAll()).thenReturn(tokens);
        
        // Mock WebClient chain
        when(webClientBuilder.build()).thenReturn(webClient);
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.retrieve()).thenReturn(responseSpec);
        
        // Mock first call success, second call failure
        Map<String, Object> successResponse = new HashMap<>();
        successResponse.put("access_token", "new-access-token");
        successResponse.put("expires_in", 7200);
        successResponse.put("refresh_token", "new-refresh-token");
        successResponse.put("scope", "read");
        
        when(responseSpec.bodyToMono(any(ParameterizedTypeReference.class)))
                .thenReturn(Mono.just(successResponse))  // First call succeeds
                .thenReturn(Mono.empty());               // Second call returns empty
        
        when(trainJdAccessTokenMapper.updateTokenInfo(any(TrainJdAccessToken.class))).thenReturn(1);
        
        // When
        int result = jdTokenRefreshService.refreshAllTokens();
        
        // Then
        assertEquals(1, result); // Only one token should be successfully refreshed
        verify(trainJdAccessTokenMapper).findAll();
        verify(trainJdAccessTokenMapper, times(1)).updateTokenInfo(any(TrainJdAccessToken.class));
    }
    
    private TrainJdAccessToken createTestToken(Long id, String refreshToken) {
        TrainJdAccessToken token = new TrainJdAccessToken();
        token.setId(id);
        token.setUserId(1L);
        token.setTeamId(1L);
        token.setAccessToken("old-access-token");
        token.setRefreshToken(refreshToken);
        token.setExpiresTime(LocalDateTime.now().plusHours(1));
        token.setScope("read");
        token.setXid("test-xid-" + id);
        token.setShopId(1L);
        token.setIsAuthorize(1);
        token.setIsSyncComplete(1);
        token.setCreator("test");
        token.setUpdater("test");
        return token;
    }
}
