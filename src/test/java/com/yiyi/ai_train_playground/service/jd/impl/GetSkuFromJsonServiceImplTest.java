package com.yiyi.ai_train_playground.service.jd.impl;

import com.jd.open.api.sdk.domain.ware.SkuReadService.response.searchSkuList.Sku;
import com.yiyi.ai_train_playground.service.jd.GetSkuFromJsonService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * GetSkuFromJsonService的单元测试
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@SpringBootTest(properties = {
    "jd.sku.json.file.path=src/test/resources/test_bot_product_sku.json"
})
@ActiveProfiles("test")
class GetSkuFromJsonServiceImplTest {
    
    @Autowired
    private GetSkuFromJsonService getSkuFromJsonService;
    
    @Test
    @DisplayName("测试从JSON文件读取并转换为Sku列表")
    void testGetSkuListFromJson() {
        // 执行测试
        List<Sku> result = getSkuFromJsonService.getSkuListFromJson();
        
        // 验证结果
        assertThat(result).isNotNull();
        log.info("成功读取并转换了{}条Sku记录", result.size());
        
        // 如果有数据，验证第一条记录的基本字段
        if (!result.isEmpty()) {
            Sku firstSku = result.get(0);
            assertThat(firstSku).isNotNull();
            log.info("第一条记录信息 - skuId: {}, skuName: {}, wareTitle: {}", 
                    firstSku.getSkuId(), firstSku.getSkuName(), firstSku.getWareTitle());
            
            // 打印所有SKU信息
            for(Sku sku : result){
                log.info("SKU信息: {}", sku);
            }
        }
    }

    @Test
    @DisplayName("测试根据wareId筛选从JSON文件读取Sku列表")
    void testGetSkuListFromJsonWithWareId() {
        // 测试筛选存在的wareId
        Long existingWareId = 100012345678L;
        List<Sku> filteredResult = getSkuFromJsonService.getSkuListFromJson(existingWareId);

        // 验证结果
        assertThat(filteredResult).isNotNull();
        assertThat(filteredResult).hasSize(1);
        log.info("根据wareId: {} 筛选后，成功获取{}条Sku记录", existingWareId, filteredResult.size());

        if (!filteredResult.isEmpty()) {
            Sku firstSku = filteredResult.get(0);
            assertThat(firstSku.getWareId()).isEqualTo(existingWareId);
            log.info("筛选结果验证通过 - skuId: {}, wareId: {}", firstSku.getSkuId(), firstSku.getWareId());
        }

        // 测试筛选不存在的wareId
        Long nonExistingWareId = 999999999L;
        List<Sku> emptyResult = getSkuFromJsonService.getSkuListFromJson(nonExistingWareId);
        assertThat(emptyResult).isNotNull();
        assertThat(emptyResult).isEmpty();
        log.info("根据不存在的wareId: {} 筛选后，正确返回空列表", nonExistingWareId);

        // 测试wareId为null的情况
        List<Sku> allResult = getSkuFromJsonService.getSkuListFromJson(null);
        assertThat(allResult).isNotNull();
        assertThat(allResult).hasSize(2);
        log.info("wareId为null时，正确返回所有{}条记录", allResult.size());
    }
}
