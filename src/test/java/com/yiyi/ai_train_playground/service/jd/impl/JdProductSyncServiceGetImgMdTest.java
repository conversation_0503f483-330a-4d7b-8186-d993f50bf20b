package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.service.ConverterServiceByLLM;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * JdProductSyncServiceImpl getImgMd 方法测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class JdProductSyncServiceGetImgMdTest {
    
    @Autowired
    private JdProductSyncServiceUtil jdProductSyncService;
    
    @Autowired
    private ConverterServiceByLLM converterService;
    
    @Test
    @Order(1)
    @DisplayName("测试getImgMd方法 - 正常图像URL")
    public void testGetImgMd_NormalImageUrl() {
        System.out.println("=== 测试getImgMd方法 - 正常图像URL ===");
        
        String imageUrl = "https://example.com/product-image.jpg";
        
        try {
            // 使用反射调用私有方法
            String result = (String) ReflectionTestUtils.invokeMethod(
                    jdProductSyncService, "getImgMd", imageUrl);
            
            Assertions.assertNotNull(result, "转换结果不应为null");
            Assertions.assertTrue(result.length() > 0, "转换结果不应为空");
            
            System.out.println("输入图像URL: " + imageUrl);
            System.out.println("转换结果长度: " + result.length());
            System.out.println("转换结果预览: " + result.substring(0, Math.min(200, result.length())) + "...");
            System.out.println("✅ 正常图像URL测试通过");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("未找到提示词模板")) {
                System.out.println("⚠️ 跳过测试：需要配置有效的API密钥或提示词模板");
                Assumptions.assumeTrue(false, "需要配置有效的API密钥或提示词模板");
            } else {
                throw e;
            }
        }
    }
    
    @Test
    @Order(2)
    @DisplayName("测试getImgMd方法 - 复杂图像URL")
    public void testGetImgMd_ComplexImageUrl() {
        System.out.println("=== 测试getImgMd方法 - 复杂图像URL ===");
        
        String complexImageUrl = "https://img.jd.com/product/12345/main/image_1920x1080.png?x-oss-process=image/resize,w_800,h_600";
        
        try {
            String result = (String) ReflectionTestUtils.invokeMethod(
                    jdProductSyncService, "getImgMd", complexImageUrl);
            
            Assertions.assertNotNull(result, "转换结果不应为null");
            Assertions.assertTrue(result.length() > 0, "转换结果不应为空");
            
            System.out.println("输入图像URL: " + complexImageUrl);
            System.out.println("转换结果长度: " + result.length());
            System.out.println("转换结果预览: " + result.substring(0, Math.min(300, result.length())) + "...");
            System.out.println("✅ 复杂图像URL测试通过");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("未找到提示词模板")) {
                System.out.println("⚠️ 跳过测试：需要配置有效的API密钥或提示词模板");
                Assumptions.assumeTrue(false, "需要配置有效的API密钥或提示词模板");
            } else {
                throw e;
            }
        }
    }
    
    @Test
    @Order(3)
    @DisplayName("测试getImgMd方法 - 空URL")
    public void testGetImgMd_EmptyUrl() {
        System.out.println("=== 测试getImgMd方法 - 空URL ===");
        
        try {
            String result = (String) ReflectionTestUtils.invokeMethod(
                    jdProductSyncService, "getImgMd", "");
            
            // 空URL可能会被处理或抛出异常，这取决于ConverterServiceByLLM的实现
            System.out.println("空URL转换结果: " + result);
            System.out.println("✅ 空URL测试通过");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("未找到提示词模板") ||
                e.getMessage().contains("不能为空")) {
                System.out.println("⚠️ 跳过测试：需要配置有效的API密钥或提示词模板，或空URL被正确拒绝");
                Assumptions.assumeTrue(false, "需要配置有效的API密钥或提示词模板，或空URL被正确拒绝");
            } else {
                throw e;
            }
        }
    }
    
    @Test
    @Order(4)
    @DisplayName("测试getImgMd方法 - null URL")
    public void testGetImgMd_NullUrl() {
        System.out.println("=== 测试getImgMd方法 - null URL ===");
        
        try {
            String result = (String) ReflectionTestUtils.invokeMethod(
                    jdProductSyncService, "getImgMd", (String) null);
            
            // null URL可能会被处理或抛出异常，这取决于ConverterServiceByLLM的实现
            System.out.println("null URL转换结果: " + result);
            System.out.println("✅ null URL测试通过");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("未找到提示词模板") ||
                e.getMessage().contains("不能为空") || e.getMessage().contains("不能为null")) {
                System.out.println("⚠️ 跳过测试：需要配置有效的API密钥或提示词模板，或null URL被正确拒绝");
                Assumptions.assumeTrue(false, "需要配置有效的API密钥或提示词模板，或null URL被正确拒绝");
            } else {
                throw e;
            }
        }
    }
    
    @Test
    @Order(5)
    @DisplayName("测试getImgMd方法 - 验证调用参数")
    public void testGetImgMd_VerifyParameters() {
        System.out.println("=== 测试getImgMd方法 - 验证调用参数 ===");
        
        // 这个测试主要验证方法能正常调用，参数传递正确
        String testImageUrl = "https://test.example.com/image.jpg";
        
        try {
            String result = (String) ReflectionTestUtils.invokeMethod(
                    jdProductSyncService, "getImgMd", testImageUrl);
            
            // 验证方法能正常执行
            Assertions.assertNotNull(result, "方法应该返回结果");
            
            System.out.println("方法调用成功，返回结果长度: " + result.length());
            System.out.println("✅ 参数验证测试通过");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("未找到提示词模板")) {
                System.out.println("⚠️ 跳过测试：需要配置有效的API密钥或提示词模板");
                Assumptions.assumeTrue(false, "需要配置有效的API密钥或提示词模板");
            } else {
                throw e;
            }
        }
    }
}
