package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.entity.TrainJdSku;
import com.yiyi.ai_train_playground.service.jd.YiYiJdService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * YiYiJdService TrainJdSku相关方法测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
class YiYiJdServiceImplTrainJdSkuTest {
    
    @Autowired
    private YiYiJdService yiYiJdService;
    
    @Test
    @DisplayName("测试getTrainJdSkuList方法")
    void testGetTrainJdSkuList() {
        // 测试参数
        String accessToken = "test-access-token";
        Long wareId = 12345L;
        Integer page = 1;
        Integer pageSize = 10;
        
        try {
            log.info("开始测试getTrainJdSkuList方法");
            
            // 调用方法
            List<TrainJdSku> result = yiYiJdService.getTrainJdSkuList(accessToken, wareId, page, pageSize);
            
            // 验证结果
            assertNotNull(result);
            log.info("getTrainJdSkuList方法调用成功，返回 {} 条记录", result.size());
            
            // 如果有数据，验证数据结构
            if (!result.isEmpty()) {
                TrainJdSku firstSku = result.get(0);
                assertNotNull(firstSku);
                log.info("第一条SKU数据: skuId={}, wareId={}, skuName={}", 
                        firstSku.getSkuId(), firstSku.getWareId(), firstSku.getSkuName());
            }
            
        } catch (Exception e) {
            log.error("测试getTrainJdSkuList方法失败", e);
            // 在测试环境中，由于可能没有有效的accessToken，方法可能返回空列表而不是抛异常
            // 这是正常的行为
        }
    }
    
    @Test
    @DisplayName("测试getTrainJdSkuList方法参数验证")
    void testGetTrainJdSkuListParameterValidation() {
        try {
            log.info("开始测试getTrainJdSkuList方法参数验证");
            
            // 测试空accessToken
            List<TrainJdSku> result1 = yiYiJdService.getTrainJdSkuList(null, 12345L, 1, 10);
            assertNotNull(result1);
            assertTrue(result1.isEmpty());
            log.info("空accessToken测试通过");
            
            // 测试空wareId
            List<TrainJdSku> result2 = yiYiJdService.getTrainJdSkuList("test-token", null, 1, 10);
            assertNotNull(result2);
            assertTrue(result2.isEmpty());
            log.info("空wareId测试通过");
            
            // 测试默认分页参数
            List<TrainJdSku> result3 = yiYiJdService.getTrainJdSkuList("test-token", 12345L, null, null);
            assertNotNull(result3);
            log.info("默认分页参数测试通过");
            
        } catch (Exception e) {
            log.error("测试getTrainJdSkuList方法参数验证失败", e);
            fail("参数验证测试不应该抛出异常");
        }
    }
}
