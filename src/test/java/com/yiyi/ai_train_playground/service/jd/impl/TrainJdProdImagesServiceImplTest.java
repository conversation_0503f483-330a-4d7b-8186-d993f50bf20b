package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.entity.TrainJdProdImages;
import com.yiyi.ai_train_playground.service.jd.TrainJdProdImagesService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 京东商品图片信息服务测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class TrainJdProdImagesServiceImplTest {
    
    @Autowired
    private TrainJdProdImagesService trainJdProdImagesService;
    
    private static final Long TEST_TEAM_ID = 1001L;
    private static final Long TEST_JD_PROD_ID = 2001L;
    private static final String TEST_IMG_URL = "https://img.jd.com/test/image1.jpg";
    private static final String TEST_IMG_RECO_TEXT = "这是一个测试商品图片的LLM识别结果";
    private static final String TEST_CREATOR = "test_user";
    
    @Test
    @Order(1)
    @DisplayName("测试插入商品图片信息")
    @Transactional
    void testInsert() {
        System.out.println("=== 测试插入商品图片信息 ===");
        
        TrainJdProdImages image = createTestImage();
        
        int result = trainJdProdImagesService.insert(image);
        
        assertThat(result).isEqualTo(1);
        assertThat(image.getId()).isNotNull();
        
        System.out.println("插入成功，生成ID: " + image.getId());
        System.out.println("✅ 插入测试通过");
    }
    
    @Test
    @Order(2)
    @DisplayName("测试批量插入商品图片信息")
    @Transactional
    void testBatchInsert() {
        System.out.println("=== 测试批量插入商品图片信息 ===");
        
        List<TrainJdProdImages> imageList = new ArrayList<>();
        
        for (int i = 1; i <= 3; i++) {
            TrainJdProdImages image = createTestImage();
            image.setImgUrl("https://img.jd.com/test/batch_image" + i + ".jpg");
            image.setImgRecoText("批量测试图片" + i + "的LLM识别结果");
            imageList.add(image);
        }
        
        int result = trainJdProdImagesService.batchInsert(imageList);
        
        assertThat(result).isEqualTo(3);
        
        System.out.println("批量插入成功，插入数量: " + result);
        System.out.println("✅ 批量插入测试通过");
    }
    
    @Test
    @Order(3)
    @DisplayName("测试根据teamId和jdProdId查询商品图片列表")
    @Transactional
    void testFindByTeamIdAndJdProdId() {
        System.out.println("=== 测试根据teamId和jdProdId查询商品图片列表 ===");
        
        // 先插入测试数据
        TrainJdProdImages image = createTestImage();
        trainJdProdImagesService.insert(image);
        
        // 查询
        List<TrainJdProdImages> result = trainJdProdImagesService.findByTeamIdAndJdProdId(TEST_TEAM_ID, TEST_JD_PROD_ID);
        
        assertThat(result).isNotEmpty();
        assertThat(result.get(0).getTeamId()).isEqualTo(TEST_TEAM_ID);
        assertThat(result.get(0).getJdProdId()).isEqualTo(TEST_JD_PROD_ID);
        
        System.out.println("查询结果数量: " + result.size());
        System.out.println("第一条记录URL: " + result.get(0).getImgUrl());
        System.out.println("✅ 查询测试通过");
    }
    
    @Test
    @Order(4)
    @DisplayName("测试更新商品图片信息")
    @Transactional
    void testUpdateById() {
        System.out.println("=== 测试更新商品图片信息 ===");
        
        // 先插入测试数据
        TrainJdProdImages image = createTestImage();
        trainJdProdImagesService.insert(image);
        
        // 更新数据
        String newRecoText = "更新后的LLM识别结果";
        image.setImgRecoText(newRecoText);
        image.setSyncStatus(2); // 设置为已完成
        image.setUpdater("updated_user");
        
        int result = trainJdProdImagesService.updateById(image);
        
        assertThat(result).isEqualTo(1);
        
        // 验证更新结果
        TrainJdProdImages updatedImage = trainJdProdImagesService.findById(image.getId());
        assertThat(updatedImage.getImgRecoText()).isEqualTo(newRecoText);
        assertThat(updatedImage.getSyncStatus()).isEqualTo(2);
        assertThat(updatedImage.getUpdater()).isEqualTo("updated_user");
        
        System.out.println("更新成功，新的识别文本: " + updatedImage.getImgRecoText());
        System.out.println("✅ 更新测试通过");
    }
    
    @Test
    @Order(5)
    @DisplayName("测试保存或更新商品图片信息")
    @Transactional
    void testSaveOrUpdate() {
        System.out.println("=== 测试保存或更新商品图片信息 ===");
        
        TrainJdProdImages image = createTestImage();
        
        // 第一次调用 - 应该插入
        int result1 = trainJdProdImagesService.saveOrUpdate(image);
        assertThat(result1).isEqualTo(1);
        assertThat(image.getId()).isNotNull();
        
        Long firstId = image.getId();
        System.out.println("首次保存，生成ID: " + firstId);
        
        // 第二次调用相同URL - 应该更新
        image.setImgRecoText("更新后的识别结果");
        image.setSyncStatus(2);
        int result2 = trainJdProdImagesService.saveOrUpdate(image);
        assertThat(result2).isEqualTo(1);
        assertThat(image.getId()).isEqualTo(firstId); // ID应该保持不变
        
        // 验证更新结果
        TrainJdProdImages updatedImage = trainJdProdImagesService.findById(firstId);
        assertThat(updatedImage.getImgRecoText()).isEqualTo("更新后的识别结果");
        assertThat(updatedImage.getSyncStatus()).isEqualTo(2);
        
        System.out.println("更新成功，ID保持不变: " + updatedImage.getId());
        System.out.println("✅ 保存或更新测试通过");
    }
    
    @Test
    @Order(6)
    @DisplayName("测试统计功能")
    @Transactional
    void testCount() {
        System.out.println("=== 测试统计功能 ===");
        
        // 插入测试数据
        TrainJdProdImages image1 = createTestImage();
        image1.setImgUrl("https://img.jd.com/test/count1.jpg");
        trainJdProdImagesService.insert(image1);
        
        TrainJdProdImages image2 = createTestImage();
        image2.setImgUrl("https://img.jd.com/test/count2.jpg");
        trainJdProdImagesService.insert(image2);
        
        // 测试统计团队图片数量
        Long teamCount = trainJdProdImagesService.countByTeamId(TEST_TEAM_ID);
        assertThat(teamCount).isGreaterThanOrEqualTo(2);
        
        // 测试统计指定商品图片数量
        Long prodCount = trainJdProdImagesService.countByTeamIdAndJdProdId(TEST_TEAM_ID, TEST_JD_PROD_ID);
        assertThat(prodCount).isGreaterThanOrEqualTo(2);
        
        System.out.println("团队图片总数: " + teamCount);
        System.out.println("指定商品图片数: " + prodCount);
        System.out.println("✅ 统计测试通过");
    }
    
    @Test
    @Order(7)
    @DisplayName("测试根据同步状态查询")
    @Transactional
    void testFindByTeamIdAndSyncStatus() {
        System.out.println("=== 测试根据同步状态查询 ===");
        
        // 插入不同同步状态的测试数据
        TrainJdProdImages image1 = createTestImage();
        image1.setImgUrl("https://img.jd.com/test/status0.jpg");
        image1.setSyncStatus(0); // 未完成
        trainJdProdImagesService.insert(image1);
        
        TrainJdProdImages image2 = createTestImage();
        image2.setImgUrl("https://img.jd.com/test/status2.jpg");
        image2.setSyncStatus(2); // 已完成
        trainJdProdImagesService.insert(image2);
        
        // 查询未完成的图片
        List<TrainJdProdImages> unfinishedImages = trainJdProdImagesService.findByTeamIdAndSyncStatus(TEST_TEAM_ID, 0);
        assertThat(unfinishedImages).isNotEmpty();
        assertThat(unfinishedImages.get(0).getSyncStatus()).isEqualTo(0);
        
        // 查询已完成的图片
        List<TrainJdProdImages> finishedImages = trainJdProdImagesService.findByTeamIdAndSyncStatus(TEST_TEAM_ID, 2);
        assertThat(finishedImages).isNotEmpty();
        assertThat(finishedImages.get(0).getSyncStatus()).isEqualTo(2);
        
        System.out.println("未完成图片数量: " + unfinishedImages.size());
        System.out.println("已完成图片数量: " + finishedImages.size());
        System.out.println("✅ 同步状态查询测试通过");
    }

    @Test
    @Order(8)
    @DisplayName("测试根据team_id、sync_status分页查询")
    @Transactional
    void testFindByTeamIdAndSyncStatusWithPagination() {
        System.out.println("=== 测试根据team_id、sync_status分页查询 ===");

        // 插入多条不同同步状态的测试数据
        List<TrainJdProdImages> testImages = new ArrayList<>();
        for (int i = 1; i <= 15; i++) {
            TrainJdProdImages image = createTestImage();
            image.setImgUrl("https://img.jd.com/test/pagination" + i + ".jpg");
            image.setImgRecoText("分页测试图片" + i + "的LLM识别结果");
            image.setSyncStatus(i % 3); // 0, 1, 2 循环
            testImages.add(image);
        }
        trainJdProdImagesService.batchInsert(testImages);

        // 测试分页查询 - 查询所有状态
        List<TrainJdProdImages> page1All = trainJdProdImagesService.findByTeamIdAndSyncStatusWithPagination(
                TEST_TEAM_ID, null, null, 0, 5);
        assertThat(page1All).hasSize(5);

        List<TrainJdProdImages> page2All = trainJdProdImagesService.findByTeamIdAndSyncStatusWithPagination(
                TEST_TEAM_ID, null, null, 5, 5);
        assertThat(page2All).hasSize(5);

        // 测试分页查询 - 查询特定状态
        List<TrainJdProdImages> page1Status0 = trainJdProdImagesService.findByTeamIdAndSyncStatusWithPagination(
                TEST_TEAM_ID, 0, null, 0, 3);
        assertThat(page1Status0).hasSizeLessThanOrEqualTo(3);
        // 验证返回的都是指定状态
        page1Status0.forEach(image -> assertThat(image.getSyncStatus()).isEqualTo(0));

        // 测试边界情况
        List<TrainJdProdImages> emptyResult = trainJdProdImagesService.findByTeamIdAndSyncStatusWithPagination(
                TEST_TEAM_ID, 0, null, 100, 5);
        assertThat(emptyResult).isEmpty();

        System.out.println("第1页全部状态数量: " + page1All.size());
        System.out.println("第2页全部状态数量: " + page2All.size());
        System.out.println("第1页状态0数量: " + page1Status0.size());
        System.out.println("超出范围查询结果: " + emptyResult.size());
        System.out.println("✅ 分页查询测试通过");
    }

    @Test
    @Order(9)
    @DisplayName("测试分页查询参数验证")
    @Transactional
    void testFindByTeamIdAndSyncStatusWithPaginationValidation() {
        System.out.println("=== 测试分页查询参数验证 ===");

        // 测试teamId为null
        List<TrainJdProdImages> result1 = trainJdProdImagesService.findByTeamIdAndSyncStatusWithPagination(
                null, 0, null, 0, 5);
        assertThat(result1).isEmpty();

        // 测试offset为负数
        List<TrainJdProdImages> result2 = trainJdProdImagesService.findByTeamIdAndSyncStatusWithPagination(
                TEST_TEAM_ID, 0, null, -1, 5);
        assertThat(result2).isEmpty();

        // 测试pageSize为0
        List<TrainJdProdImages> result3 = trainJdProdImagesService.findByTeamIdAndSyncStatusWithPagination(
                TEST_TEAM_ID, 0, null, 0, 0);
        assertThat(result3).isEmpty();

        // 测试pageSize为负数
        List<TrainJdProdImages> result4 = trainJdProdImagesService.findByTeamIdAndSyncStatusWithPagination(
                TEST_TEAM_ID, 0, null, 0, -5);
        assertThat(result4).isEmpty();

        System.out.println("所有参数验证测试均返回空列表，符合预期");
        System.out.println("✅ 参数验证测试通过");
    }

    /**
     * 创建测试用的商品图片对象
     */
    private TrainJdProdImages createTestImage() {
        TrainJdProdImages image = new TrainJdProdImages();
        image.setTeamId(TEST_TEAM_ID);
        image.setJdProdId(TEST_JD_PROD_ID);
        image.setImgUrl(TEST_IMG_URL);
        image.setImgRecoText(TEST_IMG_RECO_TEXT);
        image.setSyncStatus(0); // 默认未完成
        image.setCreator(TEST_CREATOR);
        image.setUpdater(TEST_CREATOR);
        return image;
    }
}
