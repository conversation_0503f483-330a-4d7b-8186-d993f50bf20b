package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.service.jd.JdNewSyncService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * JdNewSyncService测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
class JdNewSyncServiceImplTest {
    
    @Autowired
    private JdNewSyncService jdNewSyncService;
    
    @Test
    @DisplayName("测试异步同步方法")
    void testSync() {
        // 测试参数
        String xid = "test-xid-001";
        String accessToken = "test-access-token";
        Long teamId = 1L;
        String creator = "test-user";
        
        try {
            log.info("开始测试异步同步方法");
            
            // 调用同步方法
            jdNewSyncService.sync(xid, accessToken, teamId, creator);
            
            log.info("异步同步方法调用成功");
            
            // 由于是异步方法，这里只能测试方法调用是否成功
            // 实际的同步结果需要通过日志或数据库查看
            
        } catch (Exception e) {
            log.error("测试异步同步方法失败", e);
            throw e;
        }
    }
}
