package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.service.ConverterServiceByLLM;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * JdProductSyncServiceImpl getProdMd 方法测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class JdProductSyncServiceGetProdMdTest {
    
    @Autowired
    private JdProductSyncServiceUtil jdProductSyncService;
    
    @Autowired
    private ConverterServiceByLLM converterService;
    
    @Test
    @Order(1)
    @DisplayName("测试getProdMd方法 - 正常HTML内容")
    public void testGetProdMd_NormalHtml() {
        System.out.println("=== 测试getProdMd方法 - 正常HTML内容 ===");
        
        String htmlContent = "<div><h1>商品标题</h1><p>这是商品描述</p><ul><li>特点1</li><li>特点2</li></ul></div>";
        
        try {
            // 使用反射调用私有方法
            String result = (String) ReflectionTestUtils.invokeMethod(
                    jdProductSyncService, "getProdMd", htmlContent);
            
            Assertions.assertNotNull(result, "转换结果不应为null");
            Assertions.assertTrue(result.length() > 0, "转换结果不应为空");
            
            System.out.println("输入HTML: " + htmlContent);
            System.out.println("转换结果长度: " + result.length());
            System.out.println("转换结果预览: " + result.substring(0, Math.min(200, result.length())) + "...");
            System.out.println("✅ 正常HTML内容测试通过");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("未找到提示词模板")) {
                System.out.println("⚠️ 跳过测试：需要配置有效的API密钥或提示词模板");
                Assumptions.assumeTrue(false, "需要配置有效的API密钥或提示词模板");
            } else {
                throw e;
            }
        }
    }
    
    @Test
    @Order(2)
    @DisplayName("测试getProdMd方法 - 复杂HTML内容")
    public void testGetProdMd_ComplexHtml() {
        System.out.println("=== 测试getProdMd方法 - 复杂HTML内容 ===");
        
        String complexHtml = "<div class='product'>" +
                "<h1>高端智能手机</h1>" +
                "<div class='description'>" +
                "<p>这是一款<strong>高性能</strong>的智能手机，具有以下特点：</p>" +
                "<ul>" +
                "<li>6.7英寸OLED显示屏</li>" +
                "<li>128GB存储空间</li>" +
                "<li>5000mAh大容量电池</li>" +
                "<li>支持5G网络</li>" +
                "</ul>" +
                "</div>" +
                "<div class='specs'>" +
                "<table>" +
                "<tr><td>处理器</td><td>骁龙888</td></tr>" +
                "<tr><td>内存</td><td>8GB</td></tr>" +
                "<tr><td>摄像头</td><td>1200万像素</td></tr>" +
                "</table>" +
                "</div>" +
                "</div>";
        
        try {
            String result = (String) ReflectionTestUtils.invokeMethod(
                    jdProductSyncService, "getProdMd", complexHtml);
            
            Assertions.assertNotNull(result, "转换结果不应为null");
            Assertions.assertTrue(result.length() > 0, "转换结果不应为空");
            
            System.out.println("输入HTML长度: " + complexHtml.length());
            System.out.println("转换结果长度: " + result.length());
            System.out.println("转换结果预览: " + result.substring(0, Math.min(300, result.length())) + "...");
            System.out.println("✅ 复杂HTML内容测试通过");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("未找到提示词模板")) {
                System.out.println("⚠️ 跳过测试：需要配置有效的API密钥或提示词模板");
                Assumptions.assumeTrue(false, "需要配置有效的API密钥或提示词模板");
            } else {
                throw e;
            }
        }
    }
    
    @Test
    @Order(3)
    @DisplayName("测试getProdMd方法 - 空内容")
    public void testGetProdMd_EmptyContent() {
        System.out.println("=== 测试getProdMd方法 - 空内容 ===");
        
        try {
            String result = (String) ReflectionTestUtils.invokeMethod(
                    jdProductSyncService, "getProdMd", "");
            
            Assertions.assertNotNull(result, "转换结果不应为null");
            System.out.println("空内容转换结果: " + result);
            System.out.println("✅ 空内容测试通过");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("未找到提示词模板")) {
                System.out.println("⚠️ 跳过测试：需要配置有效的API密钥或提示词模板");
                Assumptions.assumeTrue(false, "需要配置有效的API密钥或提示词模板");
            } else {
                throw e;
            }
        }
    }
    
    @Test
    @Order(4)
    @DisplayName("测试getProdMd方法 - null内容")
    public void testGetProdMd_NullContent() {
        System.out.println("=== 测试getProdMd方法 - null内容 ===");
        
        try {
            String result = (String) ReflectionTestUtils.invokeMethod(
                    jdProductSyncService, "getProdMd", (String) null);
            
            // null内容可能会被处理或抛出异常，这取决于ConverterServiceByLLM的实现
            System.out.println("null内容转换结果: " + result);
            System.out.println("✅ null内容测试通过");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("未找到提示词模板") ||
                e.getMessage().contains("不能为null")) {
                System.out.println("⚠️ 跳过测试：需要配置有效的API密钥或提示词模板，或null参数被正确拒绝");
                Assumptions.assumeTrue(false, "需要配置有效的API密钥或提示词模板，或null参数被正确拒绝");
            } else {
                throw e;
            }
        }
    }
    
    @Test
    @Order(5)
    @DisplayName("测试getProdMd方法 - 验证调用参数")
    public void testGetProdMd_VerifyParameters() {
        System.out.println("=== 测试getProdMd方法 - 验证调用参数 ===");
        
        // 这个测试主要验证方法能正常调用，参数传递正确
        String testHtml = "<p>测试内容</p>";
        
        try {
            String result = (String) ReflectionTestUtils.invokeMethod(
                    jdProductSyncService, "getProdMd", testHtml);
            
            // 验证方法能正常执行
            Assertions.assertNotNull(result, "方法应该返回结果");
            
            System.out.println("方法调用成功，返回结果长度: " + result.length());
            System.out.println("✅ 参数验证测试通过");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("未找到提示词模板")) {
                System.out.println("⚠️ 跳过测试：需要配置有效的API密钥或提示词模板");
                Assumptions.assumeTrue(false, "需要配置有效的API密钥或提示词模板");
            } else {
                throw e;
            }
        }
    }
}
