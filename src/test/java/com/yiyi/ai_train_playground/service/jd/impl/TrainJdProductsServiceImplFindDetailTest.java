package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.dto.JdPrdDtlResponse;
import com.yiyi.ai_train_playground.service.jd.TrainJdProductsService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TrainJdProductsServiceImpl.findJdProductDetail 方法测试
 * 验证修改后的 join 查询逻辑是否正确
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-20
 */
@Slf4j
@SpringBootTest
public class TrainJdProductsServiceImplFindDetailTest {

    @Autowired
    private TrainJdProductsService trainJdProductsService;

    @Test
    @DisplayName("测试 findJdProductDetail 方法的 join 查询逻辑")
    public void testFindJdProductDetailWithJoinQuery() {
        log.info("=== 开始测试 findJdProductDetail 方法 ===");

        // 使用测试数据（需要根据实际数据库中的数据调整）
        Long testTeamId = 1L;
        Long testWareId = 100012043978L; // 请根据实际数据调整

        try {
            // 调用被测试的方法
            JdPrdDtlResponse response = trainJdProductsService.findJdProductDetail(testTeamId, testWareId);

            if (response != null) {
                log.info("✅ 查询成功");
                log.info("ID: {}", response.getId());
                log.info("WareId: {}", response.getWareId());
                log.info("JdProdDtl 长度: {}", response.getJdProdDtl() != null ? response.getJdProdDtl().length() : 0);
                log.info("JdProdImgList: {}", response.getJdProdImgList());

                // 验证基本字段
                assertNotNull(response.getId(), "ID 不应为空");
                assertNotNull(response.getWareId(), "WareId 不应为空");
                assertEquals(testWareId, response.getWareId(), "WareId 应该匹配");

                // 验证图片列表格式（应该是逗号分隔的URL）
                if (response.getJdProdImgList() != null && !response.getJdProdImgList().isEmpty()) {
                    String[] imgUrls = response.getJdProdImgList().split(",");
                    log.info("图片URL数量: {}", imgUrls.length);
                    for (int i = 0; i < imgUrls.length; i++) {
                        log.info("图片URL[{}]: {}", i, imgUrls[i].trim());
                    }
                    assertTrue(imgUrls.length > 0, "应该至少有一个图片URL");
                }

                log.info("✅ 所有验证通过");
            } else {
                log.warn("⚠️ 查询结果为空，可能是测试数据不存在");
                log.warn("请检查数据库中是否存在 teamId={}, wareId={} 的数据", testTeamId, testWareId);
            }

        } catch (Exception e) {
            log.error("❌ 测试失败", e);
            fail("测试执行失败: " + e.getMessage());
        }

        log.info("=== findJdProductDetail 方法测试完成 ===");
    }

    @Test
    @DisplayName("测试 findJdProductDetail 方法的参数验证")
    public void testFindJdProductDetailParameterValidation() {
        log.info("=== 开始测试参数验证 ===");

        // 测试 teamId 为 null
        JdPrdDtlResponse response1 = trainJdProductsService.findJdProductDetail(null, 123L);
        assertNull(response1, "teamId 为 null 时应返回 null");
        log.info("✅ teamId 为 null 的验证通过");

        // 测试 wareId 为 null
        JdPrdDtlResponse response2 = trainJdProductsService.findJdProductDetail(1L, null);
        assertNull(response2, "wareId 为 null 时应返回 null");
        log.info("✅ wareId 为 null 的验证通过");

        // 测试不存在的数据
        JdPrdDtlResponse response3 = trainJdProductsService.findJdProductDetail(999999L, 999999L);
        assertNull(response3, "不存在的数据应返回 null");
        log.info("✅ 不存在数据的验证通过");

        log.info("=== 参数验证测试完成 ===");
    }
}
