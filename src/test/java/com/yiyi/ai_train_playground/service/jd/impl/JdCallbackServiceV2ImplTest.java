package com.yiyi.ai_train_playground.service.jd.impl;

import com.yiyi.ai_train_playground.config.JdConfig;
import com.yiyi.ai_train_playground.dto.JdCallbackRequest;
import com.yiyi.ai_train_playground.dto.JdCallbackResult;
import com.yiyi.ai_train_playground.entity.TrainJdAccessToken;
import com.yiyi.ai_train_playground.mapper.TrainJdAccessTokenMapper;
import com.yiyi.ai_train_playground.service.jd.JdNewSyncService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * 京东回调服务V2实现单元测试
 *
 * <AUTHOR> Assistant
 * @since 2025-01-21
 */
@ExtendWith(MockitoExtension.class)
class JdCallbackServiceV2ImplTest {

    @Mock
    private TrainJdAccessTokenMapper trainJdAccessTokenMapper;

    @Mock
    private WebClient.Builder webClientBuilder;

    @Mock
    private WebClient webClient;

    @Mock
    private WebClient.RequestHeadersUriSpec requestHeadersUriSpec;

    @Mock
    private WebClient.RequestHeadersSpec requestHeadersSpec;

    @Mock
    private WebClient.ResponseSpec responseSpec;

    @Mock
    private JdConfig jdConfig;

    @Mock
    private JdNewSyncService jdNewSyncService;

    @InjectMocks
    private JdCallbackServiceV2Impl jdCallbackServiceV2Impl;

    @BeforeEach
    void setUp() {
        // Mock JdConfig - 使用lenient模式避免不必要的stubbing错误
        lenient().when(jdConfig.getExpectedState()).thenReturn("YyJdPlayground2025");
        lenient().when(jdConfig.getTokenUrl()).thenReturn("https://api.jd.com/oauth2/token");
        lenient().when(jdConfig.getAppKey()).thenReturn("test_app_key");
        lenient().when(jdConfig.getAppSecret()).thenReturn("test_app_secret");

        // Mock JdConfig.Sync
        JdConfig.Sync syncConfig = new JdConfig.Sync();
        syncConfig.setIsMockSwitch(false); // 默认不使用Mock模式
        lenient().when(jdConfig.getSync()).thenReturn(syncConfig);

        // Mock WebClient chain - 使用lenient模式
        lenient().when(webClientBuilder.build()).thenReturn(webClient);
        lenient().when(webClient.get()).thenReturn(requestHeadersUriSpec);
        lenient().when(requestHeadersUriSpec.uri(anyString())).thenReturn(requestHeadersSpec);
        lenient().when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
    }

    @Test
    void testHandleCallback_Success() {
        // 准备测试数据
        JdCallbackRequest request = new JdCallbackRequest();
        request.setState("YyJdPlayground2025");
        request.setCode("test_code");
        request.setTeamId(1L);
        request.setUserId(100L);
        request.setUsername("testuser");

        // Mock 京东API响应
        Map<String, Object> tokenResponse = new HashMap<>();
        tokenResponse.put("access_token", "test_access_token");
        tokenResponse.put("expires_in", 7200);
        tokenResponse.put("refresh_token", "test_refresh_token");
        tokenResponse.put("scope", "read,write");
        tokenResponse.put("xid", "test_xid");

        when(responseSpec.bodyToMono(Map.class)).thenReturn(Mono.just(tokenResponse));
        when(trainJdAccessTokenMapper.findByXid("test_xid")).thenReturn(null);
        when(trainJdAccessTokenMapper.insert(any(TrainJdAccessToken.class))).thenReturn(1);

        // 执行测试
        JdCallbackResult result = jdCallbackServiceV2Impl.handleCallback(request);

        // 验证结果
        assertTrue(result.isSuccess());
        assertTrue(result.isAuthorize());
        assertTrue(result.isSyncComplete());
        assertEquals("test_access_token", result.getAccessToken());
        assertFalse(result.isJdUserExist()); // 新用户，不存在
        assertNull(result.getUserId()); // 新用户，userId为null
        assertEquals("test_xid", result.getXid()); // 验证xid

        // 验证mapper调用
        verify(trainJdAccessTokenMapper).insert(any(TrainJdAccessToken.class));
        verify(jdNewSyncService).sync(eq("test_xid"), eq("test_access_token"), eq(1L), eq("testuser"));
    }

    @Test
    void testHandleCallback_InvalidState() {
        // 准备测试数据
        JdCallbackRequest request = new JdCallbackRequest();
        request.setState("invalid_state");
        request.setCode("test_code");
        request.setTeamId(1L);
        request.setUserId(100L);
        request.setUsername("testuser");

        // 执行测试
        JdCallbackResult result = jdCallbackServiceV2Impl.handleCallback(request);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("非法的state参数", result.getErrorMessage());

        // 验证没有调用mapper
        verify(trainJdAccessTokenMapper, never()).insert(any(TrainJdAccessToken.class));
        verify(trainJdAccessTokenMapper, never()).updateByXid(any(TrainJdAccessToken.class));
    }

    @Test
    void testHandleCallback_EmptyApiResponse() {
        // 准备测试数据
        JdCallbackRequest request = new JdCallbackRequest();
        request.setState("YyJdPlayground2025");
        request.setCode("test_code");
        request.setTeamId(1L);
        request.setUserId(100L);
        request.setUsername("testuser");

        // Mock 空响应
        when(responseSpec.bodyToMono(Map.class)).thenReturn(Mono.empty());

        // 执行测试
        JdCallbackResult result = jdCallbackServiceV2Impl.handleCallback(request);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("京东API返回空响应", result.getErrorMessage());

        // 验证没有调用mapper
        verify(trainJdAccessTokenMapper, never()).insert(any(TrainJdAccessToken.class));
    }

    @Test
    void testHandleCallback_MissingRequiredFields() {
        // 准备测试数据
        JdCallbackRequest request = new JdCallbackRequest();
        request.setState("YyJdPlayground2025");
        request.setCode("test_code");
        request.setTeamId(1L);
        request.setUserId(100L);
        request.setUsername("testuser");

        // Mock 缺少必要字段的响应
        Map<String, Object> tokenResponse = new HashMap<>();
        tokenResponse.put("expires_in", 7200);
        // 缺少 access_token 和 xid

        when(responseSpec.bodyToMono(Map.class)).thenReturn(Mono.just(tokenResponse));

        // 执行测试
        JdCallbackResult result = jdCallbackServiceV2Impl.handleCallback(request);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("京东API响应缺少必要字段", result.getErrorMessage());

        // 验证没有调用mapper
        verify(trainJdAccessTokenMapper, never()).insert(any(TrainJdAccessToken.class));
    }

    @Test
    void testHandleCallback_UpdateExistingToken() {
        // 准备测试数据
        JdCallbackRequest request = new JdCallbackRequest();
        request.setState("YyJdPlayground2025");
        request.setCode("test_code");
        request.setTeamId(1L);
        request.setUserId(100L);
        request.setUsername("testuser");

        // Mock 京东API响应
        Map<String, Object> tokenResponse = new HashMap<>();
        tokenResponse.put("access_token", "test_access_token");
        tokenResponse.put("expires_in", 7200);
        tokenResponse.put("refresh_token", "test_refresh_token");
        tokenResponse.put("scope", "read,write");
        tokenResponse.put("xid", "test_xid");

        when(responseSpec.bodyToMono(Map.class)).thenReturn(Mono.just(tokenResponse));
        
        // Mock 已存在的token
        TrainJdAccessToken existingToken = new TrainJdAccessToken();
        existingToken.setXid("test_xid");
        existingToken.setUserId(999L); // 设置已存在的userId
        when(trainJdAccessTokenMapper.findByXid("test_xid")).thenReturn(existingToken);
        when(trainJdAccessTokenMapper.updateByXid(any(TrainJdAccessToken.class))).thenReturn(1);

        // 执行测试
        JdCallbackResult result = jdCallbackServiceV2Impl.handleCallback(request);

        // 验证结果
        assertTrue(result.isSuccess());
        assertTrue(result.isAuthorize());
        assertTrue(result.isSyncComplete());
        assertEquals("test_access_token", result.getAccessToken());
        assertTrue(result.isJdUserExist()); // 已存在的用户
        assertEquals(999L, result.getUserId()); // 验证userId来自existingToken
        assertEquals("test_xid", result.getXid()); // 验证xid

        // 验证调用了update而不是insert
        verify(trainJdAccessTokenMapper).updateByXid(any(TrainJdAccessToken.class));
        verify(trainJdAccessTokenMapper, never()).insert(any(TrainJdAccessToken.class));
    }

    @Test
    void testHandleCallback_IsJdUserExist() {
        // 测试新增的isJdUserExist字段
        JdCallbackRequest request = new JdCallbackRequest();
        request.setState("YyJdPlayground2025");
        request.setCode("test_code");
        request.setTeamId(1L);
        request.setUserId(100L);
        request.setUsername("testuser");

        // Mock 京东API响应
        Map<String, Object> tokenResponse = new HashMap<>();
        tokenResponse.put("access_token", "test_access_token");
        tokenResponse.put("expires_in", 7200);
        tokenResponse.put("refresh_token", "test_refresh_token");
        tokenResponse.put("scope", "read,write");
        tokenResponse.put("xid", "test_xid");

        when(responseSpec.bodyToMono(Map.class)).thenReturn(Mono.just(tokenResponse));
        when(trainJdAccessTokenMapper.findByXid("test_xid")).thenReturn(null);
        when(trainJdAccessTokenMapper.insert(any(TrainJdAccessToken.class))).thenReturn(1);

        // 执行测试
        JdCallbackResult result = jdCallbackServiceV2Impl.handleCallback(request);

        // 验证isJdUserExist字段和userId
        assertFalse(result.isJdUserExist()); // 新用户，应该为false
        assertNull(result.getUserId()); // 新用户，userId应该为null
        assertEquals("test_xid", result.getXid()); // 验证xid

        // 测试已存在用户的情况
        TrainJdAccessToken existingToken = new TrainJdAccessToken();
        existingToken.setXid("test_xid");
        existingToken.setUserId(888L); // 设置已存在的userId
        when(trainJdAccessTokenMapper.findByXid("test_xid")).thenReturn(existingToken);
        when(trainJdAccessTokenMapper.updateByXid(any(TrainJdAccessToken.class))).thenReturn(1);

        // 再次执行测试
        JdCallbackResult result2 = jdCallbackServiceV2Impl.handleCallback(request);

        // 验证isJdUserExist字段和userId
        assertTrue(result2.isJdUserExist()); // 已存在用户，应该为true
        assertEquals(888L, result2.getUserId()); // 验证userId来自existingToken
        assertEquals("test_xid", result2.getXid()); // 验证xid
    }

    @Test
    void testHandleCallback_MockMode() {
        // 准备测试数据
        JdCallbackRequest request = new JdCallbackRequest();
        request.setState("YyJdPlayground2025");
        request.setCode("test_code");
        request.setTeamId(1L);
        request.setUserId(100L);
        request.setUsername("testuser");

        // Mock JdConfig for Mock mode
        JdConfig.Sync syncConfig = new JdConfig.Sync();
        syncConfig.setIsMockSwitch(true);
        when(jdConfig.getSync()).thenReturn(syncConfig);

        // Mock existing token for Mock mode
        TrainJdAccessToken existingToken = new TrainJdAccessToken();
        existingToken.setXid("o*AASsPGRxBX6sjhq5v-zzb_U1YjI5ZfqeRIRBiYKyLkaMCc4Rqxs");
        existingToken.setAccessToken("existing_access_token");
        existingToken.setRefreshToken("existing_refresh_token");
        existingToken.setScope("read,write");
        existingToken.setUserId(777L); // 设置Mock模式下的userId
        when(trainJdAccessTokenMapper.findByXid("o*AASsPGRxBX6sjhq5v-zzb_U1YjI5ZfqeRIRBiYKyLkaMCc4Rqxs")).thenReturn(existingToken);
        when(trainJdAccessTokenMapper.updateByXid(any(TrainJdAccessToken.class))).thenReturn(1);

        // 执行测试
        JdCallbackResult result = jdCallbackServiceV2Impl.handleCallback(request);

        // 验证结果
        assertTrue(result.isSuccess());
        assertTrue(result.isAuthorize());
        assertTrue(result.isSyncComplete());
        assertEquals("existing_access_token", result.getAccessToken());
        assertTrue(result.isJdUserExist()); // Mock模式下用户已存在
        assertEquals(777L, result.getUserId()); // 验证Mock模式下的userId
        assertEquals("o*AASsPGRxBX6sjhq5v-zzb_U1YjI5ZfqeRIRBiYKyLkaMCc4Rqxs", result.getXid()); // 验证Mock模式下的xid

        // 验证调用了update
        verify(trainJdAccessTokenMapper).updateByXid(any(TrainJdAccessToken.class));
    }
}
