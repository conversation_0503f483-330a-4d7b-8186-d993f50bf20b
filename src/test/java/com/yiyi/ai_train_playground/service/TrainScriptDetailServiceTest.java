package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.dto.ScriptDetailDTO;
import com.yiyi.ai_train_playground.service.impl.TrainScriptServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 剧本详情查询服务测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-02
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class TrainScriptDetailServiceTest {

    @Autowired
    private TrainScriptServiceImpl trainScriptService;

    @Test
    public void testGetScriptDetailById_Success() {
        // 测试成功查询剧本详情
        Long scriptId = 1L;
        Long teamId = 1L;
        
        ScriptDetailDTO result = trainScriptService.getScriptDetail(scriptId, teamId);
        
        assertNotNull(result, "剧本详情不应为空");
        assertEquals(scriptId, result.getId(), "剧本ID应该匹配");
        assertEquals("商品知识训练剧本", result.getName(), "剧本名称应该匹配");
        assertEquals(Integer.valueOf(0), result.getGenerationTypeCode(), "生成类型代码应该匹配");
        assertEquals("商品知识训练", result.getGenerationType(), "生成类型描述应该匹配");
        assertEquals(Long.valueOf(1), result.getGroupId(), "分组ID应该匹配");
        assertEquals(Long.valueOf(1), result.getIntentId(), "意图ID应该匹配");
        assertEquals("销售意图", result.getIntentName(), "意图名称应该匹配");
        assertNull(result.getParentIntentId(), "父意图ID应该为空");
        assertNull(result.getParentIntentName(), "父意图名称应该为空");
        assertEquals(Long.valueOf(1), result.getEvaluationPlanId(), "评价方案ID应该匹配");
        assertEquals("基础评价方案", result.getEvaluationPlanName(), "评价方案名称应该匹配");
        assertEquals("根分组", result.getEvaluationPlanGroupName(), "评价方案分组名称应该匹配");
        assertEquals("需要了解产品特性", result.getBuyerRequirement(), "买家需求应该匹配");
        assertEquals(Integer.valueOf(1), result.getOrderPriority(), "订单优先级应该匹配");
        assertEquals("高优先级", result.getOrderRemark(), "订单备注应该匹配");
        
        // 验证商品列表
        assertNotNull(result.getProductList(), "商品列表不应为空");
        assertEquals(2, result.getProductList().size(), "应该有2个商品");
        assertEquals("智能手机", result.getProductList().get(0).getExternalProductName(), "第一个商品名称应该匹配");
        assertEquals("蓝牙耳机", result.getProductList().get(1).getExternalProductName(), "第二个商品名称应该匹配");
        
        // 验证关联图片列表
        assertNotNull(result.getRelateImgs(), "关联图片列表不应为空");
        assertEquals(2, result.getRelateImgs().size(), "应该有2个关联图片");
        assertEquals(Integer.valueOf(1), result.getRelateImgs().get(0).getMediaType(), "第一个媒体类型应该是图片");
        assertEquals(Integer.valueOf(2), result.getRelateImgs().get(1).getMediaType(), "第二个媒体类型应该是视频");
        
        // 验证流程节点列表
        assertNotNull(result.getFlowNodes(), "流程节点列表不应为空");
        assertEquals(2, result.getFlowNodes().size(), "应该有2个流程节点");
        assertEquals("产品参数", result.getFlowNodes().get(0).getNodeName(), "第一个节点名称应该匹配");
        assertEquals("价格咨询", result.getFlowNodes().get(1).getNodeName(), "第二个节点名称应该匹配");
        
        log.info("剧本详情查询测试成功：{}", result);
    }

    @Test
    public void testGetScriptDetailById_WithParentIntent() {
        // 测试查询有父意图的剧本详情
        Long scriptId = 3L;
        Long teamId = 1L;
        
        ScriptDetailDTO result = trainScriptService.getScriptDetail(scriptId, teamId);
        
        assertNotNull(result, "剧本详情不应为空");
        assertEquals(scriptId, result.getId(), "剧本ID应该匹配");
        assertEquals("自定义内容剧本", result.getName(), "剧本名称应该匹配");
        assertEquals(Long.valueOf(3), result.getIntentId(), "意图ID应该匹配");
        assertEquals("产品咨询", result.getIntentName(), "意图名称应该匹配");
        assertEquals(Long.valueOf(1), result.getParentIntentId(), "父意图ID应该匹配");
        assertEquals("销售意图", result.getParentIntentName(), "父意图名称应该匹配");
        
        log.info("带父意图的剧本详情查询测试成功：{}", result);
    }

    @Test
    public void testGetScriptDetailById_NotFound() {
        // 测试查询不存在的剧本
        Long scriptId = 999L;
        Long teamId = 1L;
        
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.getScriptDetail(scriptId, teamId);
        });
        
        assertEquals("剧本不存在或无权限访问", exception.getMessage(), "异常消息应该匹配");
        
        log.info("不存在剧本查询测试成功：{}", exception.getMessage());
    }

    @Test
    public void testGetScriptDetailById_WrongTeam() {
        // 测试查询其他团队的剧本
        Long scriptId = 1L;
        Long teamId = 2L;
        
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.getScriptDetail(scriptId, teamId);
        });
        
        assertEquals("剧本不存在或无权限访问", exception.getMessage(), "异常消息应该匹配");
        
        log.info("跨团队剧本查询测试成功：{}", exception.getMessage());
    }

    @Test
    public void testGetScriptDetailById_NullId() {
        // 测试空ID参数
        Long teamId = 1L;
        
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.getScriptDetail(null, teamId);
        });
        
        assertEquals("剧本ID不能为空", exception.getMessage(), "异常消息应该匹配");
        
        log.info("空ID参数测试成功：{}", exception.getMessage());
    }

    @Test
    public void testGetScriptDetailById_NullTeamId() {
        // 测试空团队ID参数
        Long scriptId = 1L;
        
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.getScriptDetail(scriptId, null);
        });
        
        assertEquals("团队ID不能为空", exception.getMessage(), "异常消息应该匹配");
        
        log.info("空团队ID参数测试成功：{}", exception.getMessage());
    }
}
