package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.service.jd.AsyncProductProcessingService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
class AsyncProductProcessingServiceTest {
    
    @Autowired
    private AsyncProductProcessingService asyncProductProcessingService;
    
    @Test
    void testProcessProductDetailAsyncWithDuplicateCheck() {
        // 准备测试数据
        String externalProductId = "TEST_PRODUCT_" + System.currentTimeMillis();
        String teamId = "1";
        String productDetail = "<p><strong>品牌</strong>：测试品牌</p><p><strong>型号</strong>：测试型号</p><p><strong>描述</strong>：这是一个测试商品的详细描述，用于验证异步处理功能。包含向量化处理、重复检查和删除功能。</p>";
        
        try {
            // 第一次处理 - 插入新向量
            System.out.println("=== 第一次处理商品详情 ===");
            asyncProductProcessingService.processProductDetailAsync(externalProductId, teamId, productDetail);
            
            // 等待异步处理完成
            Thread.sleep(10000);
            
            // 第二次处理相同商品 - 应该删除旧向量并插入新向量
            System.out.println("=== 第二次处理相同商品详情 ===");
            String updatedProductDetail = "<p><strong>品牌</strong>：测试品牌（更新版）</p><p><strong>型号</strong>：测试型号（更新版）</p><p><strong>描述</strong>：这是一个更新后的测试商品详细描述，用于验证异步处理的重复检查和删除功能。</p>";
            
            asyncProductProcessingService.processProductDetailAsync(externalProductId, teamId, updatedProductDetail);
            
            // 等待异步处理完成
            Thread.sleep(10000);
            
            System.out.println("=== 异步处理测试完成 ===");
            System.out.println("测试商品ID: " + externalProductId);
            System.out.println("团队ID: " + teamId);
            System.out.println("测试验证了向量重复检查和删除功能");
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            fail("测试被中断");
        } catch (Exception e) {
            fail("异步处理测试失败: " + e.getMessage());
        }
    }
    
    @Test
    void testProcessDifferentProducts() {
        // 测试处理不同商品时不会相互影响
        String teamId = "1";
        String baseTime = String.valueOf(System.currentTimeMillis());
        
        String product1Id = "TEST_PRODUCT_1_" + baseTime;
        String product2Id = "TEST_PRODUCT_2_" + baseTime;
        
        String product1Detail = "<p>商品1详情：这是第一个测试商品</p>";
        String product2Detail = "<p>商品2详情：这是第二个测试商品</p>";
        
        try {
            System.out.println("=== 测试处理不同商品 ===");
            
            // 并行处理两个不同的商品
            asyncProductProcessingService.processProductDetailAsync(product1Id, teamId, product1Detail);
            asyncProductProcessingService.processProductDetailAsync(product2Id, teamId, product2Detail);
            
            // 等待异步处理完成
            Thread.sleep(8000);
            
            System.out.println("=== 不同商品处理测试完成 ===");
            System.out.println("商品1 ID: " + product1Id);
            System.out.println("商品2 ID: " + product2Id);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            fail("测试被中断");
        } catch (Exception e) {
            fail("不同商品处理测试失败: " + e.getMessage());
        }
    }
} 