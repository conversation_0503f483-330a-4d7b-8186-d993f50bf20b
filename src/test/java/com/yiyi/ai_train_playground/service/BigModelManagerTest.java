package com.yiyi.ai_train_playground.service;

import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.yiyi.ai_train_playground.model.ContextResult;
import com.yiyi.ai_train_playground.service.impl.DoubaoBigModelServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.TestPropertySource;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@SpringBootTest
@TestPropertySource(properties = {
        "my.doubao.normal.endpoint.name=ep-20250629195408-gtv9c"
})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class BigModelManagerTest {

    @Autowired
    private BigModelManager bigModelManager;

    @Autowired
    private DoubaoBigModelServiceImpl doubaoBigModelService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String TEST_SYSTEM_PROMPT = "你模拟客户，兼语义分析专家，需要向客服提问，以训练它的知识。";
    private static final String TEST_SESSION_ID = "test-session-" + System.currentTimeMillis();

    @Test
    @Order(1)
    @DisplayName("测试生成上下文ID功能")
    public void testGenerateContextId() {
        System.out.println("=== 测试1: 生成上下文ID功能 ===");

        try {
            ContextResult contextResult = doubaoBigModelService.generateContextId(TEST_SYSTEM_PROMPT);

            Assertions.assertNotNull(contextResult, "上下文结果不应为null");
            Assertions.assertNotNull(contextResult.getId(), "上下文ID不应为null");
            Assertions.assertFalse(contextResult.getId().trim().isEmpty(), "上下文ID不应为空");
            Assertions.assertTrue(contextResult.getId().startsWith("ctx-"), "上下文ID应以'ctx-'开头");

            System.out.println("✅ 上下文ID生成测试通过");
            System.out.println("上下文ID: " + contextResult.getId());
            System.out.println("模型: " + contextResult.getModel());
            System.out.println("模式: " + contextResult.getMode());
            System.out.println("TTL: " + contextResult.getTtl());
            if (contextResult.getUsage() != null) {
                System.out.println("Token使用情况: " + contextResult.getUsage().getTotalTokens());
            }

        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY")) {
                System.out.println("⚠️ 跳过测试：需要配置ARK_API_KEY环境变量");
                Assumptions.assumeTrue(false, "需要配置ARK_API_KEY环境变量");
            } else {
                throw e;
            }
        }
    }

    @Test
    @Order(2)
    @DisplayName("测试带上下文的对话功能")
    public void testContextChatCompletion() {
        System.out.println("=== 测试2: 带上下文的对话功能 ===");

        try {
            // 先生成上下文ID
            ContextResult contextResult = doubaoBigModelService.generateContextId(TEST_SYSTEM_PROMPT);
            String contextId = contextResult.getId();

            // 构建用户消息
            List<ChatMessage> messages = new ArrayList<>();
            ChatMessage userMessage = ChatMessage.builder()
                    .role(ChatMessageRole.USER)
                    .content("你先发言")
                    .build();
            messages.add(userMessage);

            // 调用带上下文的对话
            String response = doubaoBigModelService.ntnsWithCtx(messages, contextId);

            Assertions.assertNotNull(response, "响应不应为null");
            Assertions.assertFalse(response.trim().isEmpty(), "响应不应为空");
            Assertions.assertTrue(response.length() > 5, "响应长度应大于5个字符");

            System.out.println("✅ 带上下文的对话测试通过");
            System.out.println("上下文ID: " + contextId);
            System.out.println("响应长度: " + response.length() + " 字符");
            System.out.println("响应内容: " + response.substring(0, Math.min(100, response.length())) + "...");

        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY")) {
                System.out.println("⚠️ 跳过测试：需要配置ARK_API_KEY环境变量");
                Assumptions.assumeTrue(false, "需要配置ARK_API_KEY环境变量");
            } else {
                throw e;
            }
        }
    }

    @Test
    @Order(3)
    @DisplayName("测试initSingleRobot中的上下文ID存储")
    public void testInitSingleRobotWithContextId() {
        System.out.println("=== 测试3: initSingleRobot中的上下文ID存储 ===");

        try {
            // 模拟initSingleRobot调用（简化版本，只测试上下文相关部分）
            String sessionId = "test-init-session-" + System.currentTimeMillis();

            // 生成上下文ID
            ContextResult contextResult = doubaoBigModelService.generateContextId(TEST_SYSTEM_PROMPT);
            String contextId = contextResult.getId();

            // 存储到Redis
            redisTemplate.opsForHash().put("session:" + sessionId, "sessionContextId", contextId);

            // 验证存储
            Object storedContextId = redisTemplate.opsForHash().get("session:" + sessionId, "sessionContextId");

            Assertions.assertNotNull(storedContextId, "存储的上下文ID不应为null");
            Assertions.assertEquals(contextId, storedContextId.toString(), "存储的上下文ID应与原始ID一致");

            System.out.println("✅ 上下文ID存储测试通过");
            System.out.println("会话ID: " + sessionId);
            System.out.println("存储的上下文ID: " + storedContextId);

            // 清理测试数据
            redisTemplate.delete("session:" + sessionId);

        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY")) {
                System.out.println("⚠️ 跳过测试：需要配置ARK_API_KEY环境变量");
                Assumptions.assumeTrue(false, "需要配置ARK_API_KEY环境变量");
            } else {
                throw e;
            }
        }
    }

    @Test
    @Order(4)
    @DisplayName("测试clearSession清除会话资源功能")
    public void testClearSession() {
        System.out.println("=== 测试4: clearSession清除会话资源功能 ===");

        try {
//            String testSessionId = "test-clear-session-" + System.currentTimeMillis();
            String testSessionId = "05432037_默认客服";

            // 先创建一些测试数据
            String chatLogKey = "chatlog:" + testSessionId;
            String sessionKey = "session:" + testSessionId;

            // 向Redis中存储测试数据
           /* redisTemplate.opsForValue().set(chatLogKey, "test chat log data");
            redisTemplate.opsForHash().put(sessionKey, "testKey", "testValue");
            redisTemplate.opsForHash().put(sessionKey, "sessionContextId", "ctx-test-123");*/

            // 验证数据已存储
            Assertions.assertTrue(redisTemplate.hasKey(chatLogKey), "聊天记录应该存在");
            Assertions.assertTrue(redisTemplate.hasKey(sessionKey), "会话信息应该存在");

            // 调用clearSession方法
            bigModelManager.clearSession(testSessionId);

            // 验证数据已被删除
            Assertions.assertFalse(redisTemplate.hasKey(chatLogKey), "聊天记录应该被删除");
            Assertions.assertFalse(redisTemplate.hasKey(sessionKey), "会话信息应该被删除");

            System.out.println("✅ clearSession清除会话资源测试通过");
            System.out.println("测试会话ID: " + testSessionId);
            System.out.println("已成功清除聊天记录和会话信息");

        } catch (Exception e) {
            log.error("clearSession测试失败", e);
            throw e;
        }
    }

    @Test
    @Order(5)
    @DisplayName("测试sendMessage方法中的向量检索功能")
    public void testSendMessageWithVectorRetrieval() {
        System.out.println("=== 测试5: sendMessage方法中的向量检索功能 ===");
        // 此测试将验证sendMessage方法中的向量检索功能
        System.out.println("BigModelManager 向量检索功能测试");
        // TODO: 实现具体的向量检索测试逻辑
    }
}