package com.yiyi.ai_train_playground.service;

import com.yiyi.ai_train_playground.service.impl.DoubaoChunkServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
class DoubaoChunkServiceTest {
    
    @Autowired
    private DoubaoChunkService doubaoChunkService;
    
    @Test
    void testChunkText() {
        // 准备测试数据
        String testText = "品牌：苹果，型号：iPhone 15 Pro，屏幕尺寸：6.1英寸超大屏幕大号版本，屏幕类型：Super Retina XDR OLED，分辨率：2556 x 1179像素，像素密度：460 PPI，刷新率：ProMotion自适应智能适应10-120Hz，处理器：A17 Pro芯片（6核CPU，6核GPU），内存：8GB LPDDR5 RAM，存储容量：128GB/256GB/512GB/1TB NVMe SSD，操作系统：iOS 17（可升级至未来版本），后置摄像头：48MP主摄（f/1.78光圈）+12MP超广角（f/2.2）+12MP长焦（f/2.8，3倍光学变焦），前置摄像头：12MP TrueDepth（f/1.9），视频录制：4K 60fps HDR，支持Dolby Vision，电池容量：3274mAh，充电方式：20W有线快充，15W MagSafe无线充电，7.5W Qi无线充电，防水防尘等级：IP68（最深6米，30分钟），重量：187克，尺寸：146.6毫米（高）×70.6毫米（宽）×7.8毫米（厚），颜色选项：黑色钛金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异）";
        
        Integer chunkSize = 200;
        Integer overlap = 50;
        
        // 执行测试
        List<String> result = doubaoChunkService.chunkText(testText, chunkSize, overlap);
        
        // 验证结果
        assertNotNull(result, "返回结果不应为null");
        assertFalse(result.isEmpty(), "返回结果不应为空");
        
        System.out.println("=== 文本分块测试结果 ===");
        System.out.println("原始文本长度: " + testText.length());
        System.out.println("分块数量: " + result.size());
        System.out.println("分块大小: " + chunkSize);
        System.out.println("重叠字数: " + overlap);
        
        for (int i = 0; i < result.size(); i++) {
            String chunk = result.get(i);
            System.out.println("\n--- 第" + (i + 1) + "块 (长度:" + chunk.length() + ") ---");
            System.out.println(chunk);
        }
        
        // 验证每个分块都不为空
        for (String chunk : result) {
            assertNotNull(chunk, "分块内容不应为null");
            assertFalse(chunk.trim().isEmpty(), "分块内容不应为空");
        }
        
        // 验证分块数量合理（至少应该有1个分块）
        assertTrue(result.size() >= 1, "至少应该有1个分块");
    }
    
    @Test
    void testChunkTextWithSmallText() {
        // 测试短文本分块
        String shortText = "这是一个短文本测试。";
        Integer chunkSize = 100;
        Integer overlap = 10;
        
        List<String> result = doubaoChunkService.chunkText(shortText, chunkSize, overlap);
        
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        System.out.println("\n=== 短文本分块测试结果 ===");
        System.out.println("原始文本: " + shortText);
        System.out.println("分块结果: " + result);
    }
    
    @Test
    void testChunkTextWithDifferentParameters() {
        // 测试不同参数组合
        String testText = "人工智能技术正在快速发展，机器学习、深度学习、自然语言处理等技术不断突破，为各行各业带来了革命性的变化。从智能助手到自动驾驶，从医疗诊断到金融风控，AI技术正在深刻改变我们的生活和工作方式。";
        
        // 测试不同的分块大小
        List<String> result1 = doubaoChunkService.chunkText(testText, 50, 10);
        List<String> result2 = doubaoChunkService.chunkText(testText, 100, 20);
        
        assertNotNull(result1);
        assertNotNull(result2);
        
        System.out.println("\n=== 不同参数测试结果 ===");
        System.out.println("参数1 (50字/块, 10字重叠): " + result1.size() + "个分块");
        System.out.println("参数2 (100字/块, 20字重叠): " + result2.size() + "个分块");
    }


    @Test
    public void testChunkWithDb(){


        String text = """
                品牌: 禾果小镇
                城市: 定西市
                上市时间: 11月 10月 9月
                售卖方式: 产地直销
                重量: 2500g 4500g 3斤 1斤
                配送频次: 1周1次
                套餐份量: 5人份
                产地: 中国大陆
                省份: 甘肃省
                套餐周期: 1周
                包装方式: 包装
                特产品类: 定西马铃薯
                单果规格: 中果 大果 小果
                食用方式: 蒸煮食用
                厂名: 禾果小镇
                厂址: 甘肃省定西市临洮县
                厂家联系方式: 15309331947
                保质期: 7天
                """;

        List<String> chunks = doubaoChunkService.chunkText(text, 60, 12);
        System.out.println("chunks"+chunks);


    }


} 