package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.config.ProductProcessingConfig;
import com.yiyi.ai_train_playground.model.VectorSearchResult;
import com.yiyi.ai_train_playground.service.VectorSearchService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * VectorSearchService集成测试
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class VectorSearchServiceIntegrationTest {

    @Autowired
    private VectorSearchService vectorSearchService;

    @Autowired
    private ProductProcessingConfig config;

    private final String testTeamId = "100";
    private final String testProductId = "integration_test_product";

    @Test
    void testEmbedTexts_Integration() {
        // Given
        List<String> texts = Arrays.asList("这是一个测试文本", "另一个测试文本");

        // When
        List<List<Double>> result = vectorSearchService.embedTexts(texts);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertFalse(result.get(0).isEmpty());
        assertFalse(result.get(1).isEmpty());
        
        // 验证向量维度
        assertTrue(result.get(0).size() > 100); // 豆包向量通常有较高维度
        assertEquals(result.get(0).size(), result.get(1).size()); // 同一模型的向量维度应该相同
    }

    @Test
    void testEnsureCollectionExists_Integration() {
        // Given
        String testCollectionName = "test_integration_collection";

        // When
        boolean result = vectorSearchService.ensureCollectionExists(testCollectionName);

        // Then
        assertTrue(result);
    }

    @Test
    void testSearchByText_WithoutData() {
        // Given
        String queryText = "这是一个不存在的查询";

        // When
        String result = vectorSearchService.searchByText(queryText, testTeamId, testProductId, 3);

        // Then
        assertNotNull(result);
        // 由于没有数据，应该返回空字符串
        assertTrue(result.isEmpty() || result.isBlank());
    }

    @Test
    void testSearchByVector_WithoutData() {
        // Given
        List<Float> testVector = Arrays.asList(0.1f, 0.2f, 0.3f, 0.4f, 0.5f);

        // When
        VectorSearchResult result = vectorSearchService.searchByVector(testVector, testTeamId, testProductId, 3);

        // Then
        assertNotNull(result);
        // 由于没有数据，结果应该为空
        assertTrue(result.getResults() == null || result.getResults().isEmpty());
    }

    @Test
    void testDeleteProductVectors_WithoutData() {
        // Given
        String nonExistentProductId = "non_existent_product";

        // When
        boolean result = vectorSearchService.deleteProductVectors(nonExistentProductId, testTeamId);

        // Then
        assertTrue(result); // 删除不存在的向量应该返回成功
    }

    @Test
    void testProcessAndStoreProductVectors_FullFlow() {
        // Given
        String productDetail = """
            <div>
                <h1>测试产品标题</h1>
                <p>这是一个用于集成测试的产品详情。产品具有以下特点：</p>
                <ul>
                    <li>高质量材料制造</li>
                    <li>先进的技术工艺</li>
                    <li>用户友好的设计</li>
                    <li>优秀的性价比</li>
                </ul>
                <p>产品适用于各种使用场景，能够满足不同用户的需求。</p>
            </div>
            """;

        // When & Then
        assertDoesNotThrow(() -> {
            vectorSearchService.processAndStoreProductVectors(testProductId, testTeamId, productDetail);
        });

        // 验证向量存储后可以被搜索到
        String searchResult = vectorSearchService.searchByText("测试产品", testTeamId, testProductId, 3);
        assertNotNull(searchResult);
        // 由于是异步处理，可能需要一些时间
        // 这里主要验证不会抛出异常
    }

    @Test
    void testProcessAndStoreProductVectors_UpdateExisting() {
        // Given
        String initialProductDetail = "<p>初始产品详情</p>";
        String updatedProductDetail = "<p>更新后的产品详情，包含更多信息和功能描述</p>";

        // When & Then
        // 第一次存储
        assertDoesNotThrow(() -> {
            vectorSearchService.processAndStoreProductVectors(testProductId, testTeamId, initialProductDetail);
        });

        // 第二次存储（应该覆盖之前的数据）
        assertDoesNotThrow(() -> {
            vectorSearchService.processAndStoreProductVectors(testProductId, testTeamId, updatedProductDetail);
        });
    }

    @Test
    void testDeleteProductVectors_AfterStorage() {
        // Given
        String productDetail = "<p>要被删除的产品详情</p>";
        
        // 先存储一些向量数据
        assertDoesNotThrow(() -> {
            vectorSearchService.processAndStoreProductVectors(testProductId, testTeamId, productDetail);
        });

        // When
        boolean deleteResult = vectorSearchService.deleteProductVectors(testProductId, testTeamId);

        // Then
        assertTrue(deleteResult);

        // 验证删除后搜索不到结果
        String searchResult = vectorSearchService.searchByText("产品详情", testTeamId, testProductId, 3);
        assertNotNull(searchResult);
        assertTrue(searchResult.isEmpty());
    }

    @Test
    void testCompleteWorkflow() {
        // Given
        String productDetail = """
            <div class="product-info">
                <h2>完整工作流程测试产品</h2>
                <div class="description">
                    <p>这是一个完整的集成测试，涵盖了向量搜索服务的所有主要功能。</p>
                    <p>产品特性包括：高性能、可靠性、易用性。</p>
                </div>
                <div class="specifications">
                    <h3>技术规格</h3>
                    <ul>
                        <li>处理器：高性能芯片</li>
                        <li>内存：大容量存储</li>
                        <li>接口：多种连接选项</li>
                    </ul>
                </div>
            </div>
            """;

        try {
            // Step 1: 处理和存储产品向量
            vectorSearchService.processAndStoreProductVectors(testProductId, testTeamId, productDetail);

            // Step 2: 搜索相关内容
            String searchResult1 = vectorSearchService.searchByText("技术规格", testTeamId, testProductId, 2);
            String searchResult2 = vectorSearchService.searchByText("产品特性", testTeamId, testProductId, 2);

            // Step 3: 验证搜索结果
            assertNotNull(searchResult1);
            assertNotNull(searchResult2);

            // Step 4: 更新产品信息
            String updatedDetail = productDetail + "<p>新增功能：智能识别、自动优化</p>";
            vectorSearchService.processAndStoreProductVectors(testProductId, testTeamId, updatedDetail);

            // Step 5: 再次搜索验证更新
            String searchResult3 = vectorSearchService.searchByText("智能识别", testTeamId, testProductId, 2);
            assertNotNull(searchResult3);

            // Step 6: 清理数据
            boolean deleteResult = vectorSearchService.deleteProductVectors(testProductId, testTeamId);
            assertTrue(deleteResult);

            // Step 7: 验证删除成功
            String searchResult4 = vectorSearchService.searchByText("技术规格", testTeamId, testProductId, 2);
            assertNotNull(searchResult4);
            assertTrue(searchResult4.isEmpty());

        } catch (Exception e) {
            fail("完整工作流程测试失败: " + e.getMessage(), e);
        }
    }

    @Test
    void testConcurrentOperations() {
        // Given
        String productDetail1 = "<p>并发测试产品1的详情</p>";
        String productDetail2 = "<p>并发测试产品2的详情</p>";
        String productId1 = testProductId + "_concurrent_1";
        String productId2 = testProductId + "_concurrent_2";

        // When & Then
        assertDoesNotThrow(() -> {
            // 并发处理两个产品
            Thread thread1 = new Thread(() -> {
                try {
                    vectorSearchService.processAndStoreProductVectors(productId1, testTeamId, productDetail1);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            Thread thread2 = new Thread(() -> {
                try {
                    vectorSearchService.processAndStoreProductVectors(productId2, testTeamId, productDetail2);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            thread1.start();
            thread2.start();

            thread1.join();
            thread2.join();

            // 验证两个产品都能被搜索到
            String result1 = vectorSearchService.searchByText("并发测试产品1", testTeamId, productId1, 2);
            String result2 = vectorSearchService.searchByText("并发测试产品2", testTeamId, productId2, 2);

            assertNotNull(result1);
            assertNotNull(result2);

            // 清理数据
            vectorSearchService.deleteProductVectors(productId1, testTeamId);
            vectorSearchService.deleteProductVectors(productId2, testTeamId);
        });
    }
}