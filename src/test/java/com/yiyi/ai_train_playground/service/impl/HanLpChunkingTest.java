package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.model.TextChunk;
import com.yiyi.ai_train_playground.service.impl.HanLpServiceImpl;
import org.junit.jupiter.api.Test;

import java.util.List;

public class HanLpChunkingTest {

    @Test
    public void testSemanticChunkingWithoutInfiniteLoop() {
        try {
            // 初始化HanLP服务
            HanLpServiceImpl hanLpService = new HanLpServiceImpl();
            hanLpService.init();
            
            // 测试文本
            String text = "这款智能手机重量2500g，屏幕尺寸6.7英寸，电池容量4500毫安时，价格3999元。处理器采用骁龙888型号，内存容量8GB，存储空间256GB。";
            
            System.out.println("原始文本: " + text);
            System.out.println("文本长度: " + text.length());
            
            // 测试语义分块 - 使用合理的参数
            int chunkSize = 50;
            int overlapSize = 10;
            
            System.out.println("开始语义分块测试...");
            System.out.println("分块大小: " + chunkSize);
            System.out.println("重叠大小: " + overlapSize);
            
            long startTime = System.currentTimeMillis();
            List<TextChunk> chunks = hanLpService.semanticChunking(text, chunkSize, overlapSize);
            long endTime = System.currentTimeMillis();
            
            System.out.println("分块完成，耗时: " + (endTime - startTime) + "ms");
            System.out.println("生成分块数量: " + chunks.size());
            
            // 验证结果
            if (chunks.size() > 20) {
                System.err.println("警告：分块数量异常，可能存在无限循环！");
                return;
            }
            
            // 输出分块结果
            for (TextChunk chunk : chunks) {
                System.out.println("分块 " + chunk.getIndex() + ": " + 
                                 "位置(" + chunk.getStartPosition() + "-" + chunk.getEndPosition() + ") " +
                                 "长度(" + chunk.getLength() + ") " +
                                 "重叠(" + chunk.getOverlapLength() + ")");
                System.out.println("内容: " + chunk.getContent());
                System.out.println("语义单元: " + chunk.getSemanticUnits().size() + "个");
                System.out.println("---");
            }
            
            // 验证分块的连续性
            for (int i = 1; i < chunks.size(); i++) {
                TextChunk prev = chunks.get(i - 1);
                TextChunk curr = chunks.get(i);
                
                if (curr.getStartPosition() > prev.getEndPosition()) {
                    System.err.println("警告：分块" + (i-1) + "和" + i + "之间有间隙！");
                }
            }
            
            System.out.println("语义分块测试完成！");
            
        } catch (Exception e) {
            System.err.println("语义分块测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testEdgeCases() {
        try {
            HanLpServiceImpl hanLpService = new HanLpServiceImpl();
            hanLpService.init();
            
            // 测试边界情况
            System.out.println("=== 测试边界情况 ===");
            
            // 1. 空文本
            List<TextChunk> emptyChunks = hanLpService.semanticChunking("", 50, 10);
            System.out.println("空文本分块数量: " + emptyChunks.size());
            
            // 2. 短文本
            List<TextChunk> shortChunks = hanLpService.semanticChunking("短文本", 50, 10);
            System.out.println("短文本分块数量: " + shortChunks.size());
            
            // 3. 重叠大小为0
            List<TextChunk> noOverlapChunks = hanLpService.semanticChunking("这是一个测试文本，用来验证没有重叠的情况。", 20, 0);
            System.out.println("无重叠分块数量: " + noOverlapChunks.size());
            
            // 4. 分块大小很大
            List<TextChunk> largeChunkChunks = hanLpService.semanticChunking("这是一个测试文本。", 1000, 10);
            System.out.println("大分块大小分块数量: " + largeChunkChunks.size());
            
            System.out.println("边界情况测试完成！");
            
        } catch (Exception e) {
            System.err.println("边界情况测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 