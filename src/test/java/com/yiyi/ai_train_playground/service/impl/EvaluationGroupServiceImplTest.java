package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.dto.EvaluationGroupDTO;
import com.yiyi.ai_train_playground.entity.EvaluationGroup;
import com.yiyi.ai_train_playground.mapper.EvaluationGroupMapper;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class EvaluationGroupServiceImplTest {

    @Mock
    private EvaluationGroupMapper evaluationGroupMapper;

    @InjectMocks
    private EvaluationGroupServiceImpl evaluationGroupService;

    private static final Long TEST_TEAM_ID = 123L;
    private static final String TEST_GROUP_TITLE = "测试评价分组";

    @Test
    @Order(1)
    @DisplayName("测试获取分组树 - 空数据")
    void testGetGroupTree_EmptyData() {
        System.out.println("=== 测试1: 获取分组树 - 空数据 ===");

        // 模拟空数据
        when(evaluationGroupMapper.selectList(anyString(), eq(TEST_TEAM_ID)))
                .thenReturn(new ArrayList<>());

        // 执行测试
        Map<String, Object> result = evaluationGroupService.getGroupTree(TEST_GROUP_TITLE, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.containsKey("groups"));
        
        @SuppressWarnings("unchecked")
        List<EvaluationGroupDTO> groups = (List<EvaluationGroupDTO>) result.get("groups");
        assertEquals(1, groups.size()); // 应该有根节点
        
        EvaluationGroupDTO rootGroup = groups.get(0);
        assertEquals(-1L, rootGroup.getId());
        assertEquals("全部评价", rootGroup.getGroupTitle());
        assertEquals(0, rootGroup.getSubGroups().size()); // 没有子分组

        // 验证Mapper调用
        verify(evaluationGroupMapper, times(1)).selectList(TEST_GROUP_TITLE, TEST_TEAM_ID);

        System.out.println("✅ 空数据测试通过");
    }

    @Test
    @Order(2)
    @DisplayName("测试获取分组树 - 有数据")
    void testGetGroupTree_WithData() {
        System.out.println("=== 测试2: 获取分组树 - 有数据 ===");

        // 准备测试数据
        List<EvaluationGroup> mockGroups = createMockEvaluationGroups();
        when(evaluationGroupMapper.selectList(anyString(), eq(TEST_TEAM_ID)))
                .thenReturn(mockGroups);

        // 执行测试
        Map<String, Object> result = evaluationGroupService.getGroupTree(TEST_GROUP_TITLE, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.containsKey("groups"));
        
        @SuppressWarnings("unchecked")
        List<EvaluationGroupDTO> groups = (List<EvaluationGroupDTO>) result.get("groups");
        assertEquals(1, groups.size()); // 根节点
        
        EvaluationGroupDTO rootGroup = groups.get(0);
        assertEquals("全部评价", rootGroup.getGroupTitle());
        assertTrue(rootGroup.getSubGroups().size() > 0); // 应该有子分组

        System.out.println("✅ 有数据测试通过");
        System.out.println("根分组子分组数量: " + rootGroup.getSubGroups().size());
    }

    @Test
    @Order(3)
    @DisplayName("测试保存评价分组 - 成功")
    void testSave_Success() {
        System.out.println("=== 测试3: 保存评价分组 - 成功 ===");

        // 准备测试数据
        EvaluationGroup evaluationGroup = createTestEvaluationGroup();
        when(evaluationGroupMapper.insert(any(EvaluationGroup.class))).thenReturn(1);

        // 执行测试
        boolean result = evaluationGroupService.save(evaluationGroup);

        // 验证结果
        assertTrue(result);
        verify(evaluationGroupMapper, times(1)).insert(evaluationGroup);

        System.out.println("✅ 保存成功测试通过");
    }

    @Test
    @Order(4)
    @DisplayName("测试保存评价分组 - 失败")
    void testSave_Failure() {
        System.out.println("=== 测试4: 保存评价分组 - 失败 ===");

        // 准备测试数据
        EvaluationGroup evaluationGroup = createTestEvaluationGroup();
        when(evaluationGroupMapper.insert(any(EvaluationGroup.class))).thenReturn(0);

        // 执行测试
        boolean result = evaluationGroupService.save(evaluationGroup);

        // 验证结果
        assertFalse(result);
        verify(evaluationGroupMapper, times(1)).insert(evaluationGroup);

        System.out.println("✅ 保存失败测试通过");
    }

    @Test
    @Order(5)
    @DisplayName("测试更新评价分组 - 成功")
    void testUpdate_Success() {
        System.out.println("=== 测试5: 更新评价分组 - 成功 ===");

        // 准备测试数据
        EvaluationGroup evaluationGroup = createTestEvaluationGroup();
        evaluationGroup.setId(1L);
        when(evaluationGroupMapper.update(any(EvaluationGroup.class))).thenReturn(1);

        // 执行测试
        boolean result = evaluationGroupService.update(evaluationGroup);

        // 验证结果
        assertTrue(result);
        verify(evaluationGroupMapper, times(1)).update(evaluationGroup);

        System.out.println("✅ 更新成功测试通过");
    }

    @Test
    @Order(6)
    @DisplayName("测试更新评价分组 - 失败")
    void testUpdate_Failure() {
        System.out.println("=== 测试6: 更新评价分组 - 失败 ===");

        // 准备测试数据
        EvaluationGroup evaluationGroup = createTestEvaluationGroup();
        evaluationGroup.setId(1L);
        when(evaluationGroupMapper.update(any(EvaluationGroup.class))).thenReturn(0);

        // 执行测试
        boolean result = evaluationGroupService.update(evaluationGroup);

        // 验证结果
        assertFalse(result);
        verify(evaluationGroupMapper, times(1)).update(evaluationGroup);

        System.out.println("✅ 更新失败测试通过");
    }

    @Test
    @Order(7)
    @DisplayName("测试删除评价分组 - 成功")
    void testDeleteByIds_Success() {
        System.out.println("=== 测试7: 删除评价分组 - 成功 ===");

        // 准备测试数据
        String ids = "1,2,3";
        when(evaluationGroupMapper.deleteByIds(ids, TEST_TEAM_ID)).thenReturn(3);

        // 执行测试
        boolean result = evaluationGroupService.deleteByIds(ids, TEST_TEAM_ID);

        // 验证结果
        assertTrue(result);
        verify(evaluationGroupMapper, times(1)).deleteByIds(ids, TEST_TEAM_ID);

        System.out.println("✅ 删除成功测试通过");
    }

    @Test
    @Order(8)
    @DisplayName("测试删除评价分组 - 失败")
    void testDeleteByIds_Failure() {
        System.out.println("=== 测试8: 删除评价分组 - 失败 ===");

        // 准备测试数据
        String ids = "1,2,3";
        when(evaluationGroupMapper.deleteByIds(ids, TEST_TEAM_ID)).thenReturn(0);

        // 执行测试
        boolean result = evaluationGroupService.deleteByIds(ids, TEST_TEAM_ID);

        // 验证结果
        assertFalse(result);
        verify(evaluationGroupMapper, times(1)).deleteByIds(ids, TEST_TEAM_ID);

        System.out.println("✅ 删除失败测试通过");
    }

    @Test
    @Order(9)
    @DisplayName("测试获取评价分组列表")
    void testGetEvaluationGroups() {
        System.out.println("=== 测试9: 获取评价分组列表 ===");

        // 准备测试数据
        List<EvaluationGroup> mockGroups = createMockEvaluationGroups();
        when(evaluationGroupMapper.selectList(TEST_GROUP_TITLE, TEST_TEAM_ID))
                .thenReturn(mockGroups);

        // 执行测试
        List<EvaluationGroup> result = evaluationGroupService.getEvaluationGroups(TEST_GROUP_TITLE, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockGroups.size(), result.size());
        verify(evaluationGroupMapper, times(1)).selectList(TEST_GROUP_TITLE, TEST_TEAM_ID);

        System.out.println("✅ 获取分组列表测试通过");
        System.out.println("分组数量: " + result.size());
    }

    @Test
    @Order(10)
    @DisplayName("测试分组树构建逻辑")
    void testGroupTreeBuilding() {
        System.out.println("=== 测试10: 分组树构建逻辑 ===");

        // 准备复杂的层级数据
        List<EvaluationGroup> complexGroups = createComplexHierarchyGroups();
        when(evaluationGroupMapper.selectList(isNull(), eq(TEST_TEAM_ID)))
                .thenReturn(complexGroups);

        // 执行测试
        Map<String, Object> result = evaluationGroupService.getGroupTree(null, TEST_TEAM_ID);

        // 验证结果
        assertNotNull(result);
        @SuppressWarnings("unchecked")
        List<EvaluationGroupDTO> groups = (List<EvaluationGroupDTO>) result.get("groups");
        
        EvaluationGroupDTO rootGroup = groups.get(0);
        assertEquals("全部评价", rootGroup.getGroupTitle());
        
        // 验证树形结构
        assertTrue(rootGroup.getSubGroups().size() > 0, "根分组应该有子分组");
        
        // 查找是否有嵌套的子分组
        boolean hasNestedSubGroups = rootGroup.getSubGroups().stream()
                .anyMatch(subGroup -> subGroup.getSubGroups() != null && !subGroup.getSubGroups().isEmpty());
        
        System.out.println("✅ 分组树构建测试通过");
        System.out.println("根分组子分组数: " + rootGroup.getSubGroups().size());
        System.out.println("是否有嵌套子分组: " + hasNestedSubGroups);
    }

    // 辅助方法：创建测试用的评价分组
    private EvaluationGroup createTestEvaluationGroup() {
        EvaluationGroup group = new EvaluationGroup();
        group.setGroupTitle(TEST_GROUP_TITLE);
        group.setTeamId(TEST_TEAM_ID);
        group.setParentId(null);
        group.setIsOfficial(false);
        group.setSortOrder(0);
        group.setCreateTime(LocalDateTime.now());
        group.setUpdateTime(LocalDateTime.now());
        group.setCreator("test-user");
        group.setUpdater("test-user");
        group.setVersion(0L);
        return group;
    }

    // 辅助方法：创建模拟的评价分组列表
    private List<EvaluationGroup> createMockEvaluationGroups() {
        List<EvaluationGroup> groups = new ArrayList<>();
        
        // 用户自定义分组
        EvaluationGroup userGroup = new EvaluationGroup();
        userGroup.setId(1L);
        userGroup.setGroupTitle("用户评价分组");
        userGroup.setTeamId(TEST_TEAM_ID);
        userGroup.setParentId(null);
        userGroup.setIsOfficial(false);
        userGroup.setCreateTime(LocalDateTime.now().minusDays(1));
        groups.add(userGroup);
        
        // 官方分组
        EvaluationGroup officialGroup = new EvaluationGroup();
        officialGroup.setId(2L);
        officialGroup.setGroupTitle("官方评价分组");
        officialGroup.setTeamId(0L);
        officialGroup.setParentId(null);
        officialGroup.setIsOfficial(true);
        officialGroup.setCreateTime(LocalDateTime.now().minusDays(2));
        groups.add(officialGroup);
        
        return groups;
    }

    // 辅助方法：创建复杂层级结构的分组
    private List<EvaluationGroup> createComplexHierarchyGroups() {
        List<EvaluationGroup> groups = new ArrayList<>();
        
        // 父分组
        EvaluationGroup parentGroup = new EvaluationGroup();
        parentGroup.setId(1L);
        parentGroup.setGroupTitle("父级评价分组");
        parentGroup.setTeamId(TEST_TEAM_ID);
        parentGroup.setParentId(null);
        parentGroup.setIsOfficial(false);
        parentGroup.setCreateTime(LocalDateTime.now().minusDays(1));
        groups.add(parentGroup);
        
        // 子分组
        EvaluationGroup childGroup = new EvaluationGroup();
        childGroup.setId(2L);
        childGroup.setGroupTitle("子级评价分组");
        childGroup.setTeamId(TEST_TEAM_ID);
        childGroup.setParentId(1L);
        childGroup.setIsOfficial(false);
        childGroup.setCreateTime(LocalDateTime.now().minusHours(12));
        groups.add(childGroup);
        
        // 孙分组
        EvaluationGroup grandChildGroup = new EvaluationGroup();
        grandChildGroup.setId(3L);
        grandChildGroup.setGroupTitle("孙级评价分组");
        grandChildGroup.setTeamId(TEST_TEAM_ID);
        grandChildGroup.setParentId(2L);
        grandChildGroup.setIsOfficial(false);
        grandChildGroup.setCreateTime(LocalDateTime.now().minusHours(6));
        groups.add(grandChildGroup);
        
        return groups;
    }
}
