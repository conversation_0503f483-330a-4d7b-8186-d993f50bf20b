package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.config.ProductProcessingConfig;
import com.yiyi.ai_train_playground.mapper.TrainProductMapper;
import com.yiyi.ai_train_playground.model.VectorData;
import com.yiyi.ai_train_playground.model.VectorSearchResult;
import com.yiyi.ai_train_playground.service.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * VectorSearchService单元测试
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@ExtendWith(MockitoExtension.class)
class VectorSearchServiceTest {

    @Mock
    private ProductProcessingConfig config;
    
    @Mock
    private HanLpService hanLpService;
    
    @Mock
    private HtmlSanitizerService htmlSanitizerService;
    
    @Mock
    private SuperBigModelInterface bigModelService;
    
    @Mock
    private QdrantService qdrantService;
    
    @Mock
    private TrainProductMapper trainProductMapper;
    
    @Mock
    private DoubaoChunkService doubaoChunkService;

    @InjectMocks
    private VectorSearchServiceImpl vectorSearchService;

    private final String testCollectionName = "test_collection";
    private final String testTeamId = "123";
    private final String testProductId = "product_001";
    private final String testQueryText = "测试查询文本";

    @BeforeEach
    void setUp() {
        // 设置配置默认值
        when(config.getCollectionName()).thenReturn(testCollectionName);
        when(config.getVectorDimension()).thenReturn(1536);
        when(config.getChunkSize()).thenReturn(100);
        when(config.getOverlapSize()).thenReturn(20);
    }

    @Test
    void testSearchByText_Success() {
        // Given
        List<List<Double>> mockEmbeddings = Arrays.asList(
            Arrays.asList(0.1, 0.2, 0.3)
        );
        VectorSearchResult mockResult = createMockSearchResult();
        
        when(bigModelService.embed(any())).thenReturn(mockEmbeddings);
        when(qdrantService.searchVectors(any())).thenReturn(mockResult);

        // When
        String result = vectorSearchService.searchByText(testQueryText, testTeamId, testProductId, 3);

        // Then
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertTrue(result.contains("测试内容"));
        
        verify(bigModelService).embed(Arrays.asList(testQueryText));
        verify(qdrantService).searchVectors(any());
    }

    @Test
    void testSearchByText_NoResults() {
        // Given
        List<List<Double>> mockEmbeddings = Arrays.asList(
            Arrays.asList(0.1, 0.2, 0.3)
        );
        VectorSearchResult emptyResult = VectorSearchResult.builder()
            .results(Collections.emptyList())
            .build();
        
        when(bigModelService.embed(any())).thenReturn(mockEmbeddings);
        when(qdrantService.searchVectors(any())).thenReturn(emptyResult);

        // When
        String result = vectorSearchService.searchByText(testQueryText, testTeamId, testProductId, 3);

        // Then
        assertEquals("", result);
    }

    @Test
    void testSearchByText_EmbeddingFails() {
        // Given
        when(bigModelService.embed(any())).thenThrow(new RuntimeException("Embedding failed"));

        // When
        String result = vectorSearchService.searchByText(testQueryText, testTeamId, testProductId, 3);

        // Then
        assertEquals("", result);
        verify(qdrantService, never()).searchVectors(any());
    }

    @Test
    void testSearchByVector_Success() {
        // Given
        List<Float> testVector = Arrays.asList(0.1f, 0.2f, 0.3f);
        VectorSearchResult mockResult = createMockSearchResult();
        
        when(qdrantService.searchVectors(any())).thenReturn(mockResult);

        // When
        VectorSearchResult result = vectorSearchService.searchByVector(testVector, testTeamId, testProductId, 3);

        // Then
        assertNotNull(result);
        assertNotNull(result.getResults());
        assertFalse(result.getResults().isEmpty());
        
        verify(qdrantService).searchVectors(any());
    }

    @Test
    void testProcessAndStoreProductVectors_Success() {
        // Given
        String productDetail = "<p>测试产品详情</p>";
        String cleanText = "测试产品详情";
        List<String> chunks = Arrays.asList("测试", "产品", "详情");
        List<List<Double>> embeddings = Arrays.asList(
            Arrays.asList(0.1, 0.2),
            Arrays.asList(0.3, 0.4),
            Arrays.asList(0.5, 0.6)
        );
        
        when(htmlSanitizerService.sanitizeAndExtractText(productDetail)).thenReturn(cleanText);
        when(doubaoChunkService.chunkText(eq(cleanText), anyInt(), anyInt())).thenReturn(chunks);
        when(bigModelService.embed(chunks)).thenReturn(embeddings);
        when(qdrantService.collectionExists(testCollectionName)).thenReturn(true);
        when(qdrantService.getVectorIdsByPayload(eq(testCollectionName), any())).thenReturn(Collections.emptyList());
        when(qdrantService.insertVectors(eq(testCollectionName), any())).thenReturn(true);

        // When & Then
        assertDoesNotThrow(() -> {
            vectorSearchService.processAndStoreProductVectors(testProductId, testTeamId, productDetail);
        });

        verify(htmlSanitizerService).sanitizeAndExtractText(productDetail);
        verify(doubaoChunkService).chunkText(cleanText, config.getChunkSize(), config.getOverlapSize());
        verify(bigModelService).embed(chunks);
        verify(qdrantService).insertVectors(eq(testCollectionName), any());
    }

    @Test
    void testProcessAndStoreProductVectors_InvalidTeamId() {
        // Given
        String invalidTeamId = "invalid";
        String productDetail = "测试详情";

        // When & Then
        assertDoesNotThrow(() -> {
            vectorSearchService.processAndStoreProductVectors(testProductId, invalidTeamId, productDetail);
        });

        // 验证不会进行后续处理
        verify(htmlSanitizerService, never()).sanitizeAndExtractText(any());
    }

    @Test
    void testDeleteProductVectors_Success() {
        // Given
        List<String> existingIds = Arrays.asList("vector1", "vector2");
        when(qdrantService.getVectorIdsByPayload(eq(testCollectionName), any())).thenReturn(existingIds);
        when(qdrantService.deleteVectors(testCollectionName, existingIds)).thenReturn(true);

        // When
        boolean result = vectorSearchService.deleteProductVectors(testProductId, testTeamId);

        // Then
        assertTrue(result);
        verify(qdrantService).deleteVectors(testCollectionName, existingIds);
    }

    @Test
    void testDeleteProductVectors_NoExistingVectors() {
        // Given
        when(qdrantService.getVectorIdsByPayload(eq(testCollectionName), any())).thenReturn(Collections.emptyList());

        // When
        boolean result = vectorSearchService.deleteProductVectors(testProductId, testTeamId);

        // Then
        assertTrue(result);
        verify(qdrantService, never()).deleteVectors(any(), any());
    }

    @Test
    void testEmbedTexts_Success() {
        // Given
        List<String> texts = Arrays.asList("文本1", "文本2");
        List<List<Double>> expectedEmbeddings = Arrays.asList(
            Arrays.asList(0.1, 0.2),
            Arrays.asList(0.3, 0.4)
        );
        when(bigModelService.embed(texts)).thenReturn(expectedEmbeddings);

        // When
        List<List<Double>> result = vectorSearchService.embedTexts(texts);

        // Then
        assertEquals(expectedEmbeddings, result);
        verify(bigModelService).embed(texts);
    }

    @Test
    void testEmbedTexts_Failure() {
        // Given
        List<String> texts = Arrays.asList("文本1");
        when(bigModelService.embed(texts)).thenThrow(new RuntimeException("Embedding failed"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            vectorSearchService.embedTexts(texts);
        });
    }

    @Test
    void testEnsureCollectionExists_CollectionExists() {
        // Given
        when(qdrantService.collectionExists(testCollectionName)).thenReturn(true);

        // When
        boolean result = vectorSearchService.ensureCollectionExists(testCollectionName);

        // Then
        assertTrue(result);
        verify(qdrantService, never()).createCollection(any(), anyInt(), any());
    }

    @Test
    void testEnsureCollectionExists_CreateNewCollection() {
        // Given
        when(qdrantService.collectionExists(testCollectionName)).thenReturn(false);
        when(qdrantService.createCollection(testCollectionName, config.getVectorDimension(), "COSINE")).thenReturn(true);

        // When
        boolean result = vectorSearchService.ensureCollectionExists(testCollectionName);

        // Then
        assertTrue(result);
        verify(qdrantService).createCollection(testCollectionName, config.getVectorDimension(), "COSINE");
    }

    @Test
    void testInsertVectors_Success() {
        // Given
        List<VectorData> vectorDataList = Arrays.asList(
            VectorData.builder().id("1").vector(Arrays.asList(0.1f, 0.2f)).build()
        );
        when(qdrantService.insertVectors(testCollectionName, vectorDataList)).thenReturn(true);

        // When
        boolean result = vectorSearchService.insertVectors(testCollectionName, vectorDataList);

        // Then
        assertTrue(result);
        verify(qdrantService).insertVectors(testCollectionName, vectorDataList);
    }

    @Test
    void testGetVectorIdsByConditions_Success() {
        // Given
        Map<String, Object> conditions = Map.of("teamId", testTeamId);
        List<String> expectedIds = Arrays.asList("id1", "id2");
        when(qdrantService.getVectorIdsByPayload(testCollectionName, conditions)).thenReturn(expectedIds);

        // When
        List<String> result = vectorSearchService.getVectorIdsByConditions(testCollectionName, conditions);

        // Then
        assertEquals(expectedIds, result);
        verify(qdrantService).getVectorIdsByPayload(testCollectionName, conditions);
    }

    @Test
    void testGetVectorIdsByConditions_Exception() {
        // Given
        Map<String, Object> conditions = Map.of("teamId", testTeamId);
        when(qdrantService.getVectorIdsByPayload(testCollectionName, conditions))
            .thenThrow(new RuntimeException("Query failed"));

        // When
        List<String> result = vectorSearchService.getVectorIdsByConditions(testCollectionName, conditions);

        // Then
        assertTrue(result.isEmpty());
    }

    /**
     * 创建模拟搜索结果
     */
    private VectorSearchResult createMockSearchResult() {
        Map<String, Object> payload = new HashMap<>();
        payload.put("content", "测试内容");
        payload.put("externalProductId", testProductId);
        payload.put("teamId", testTeamId);

        VectorData vectorData = VectorData.builder()
            .id("test_vector_1")
            .vector(Arrays.asList(0.1f, 0.2f, 0.3f))
            .payload(payload)
            .score(0.95f)
            .build();

        return VectorSearchResult.builder()
            .results(Arrays.asList(vectorData))
            .build();
    }
}