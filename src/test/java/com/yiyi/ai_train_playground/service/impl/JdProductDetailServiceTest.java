package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.dto.JdPrdDtlResponse;
import com.yiyi.ai_train_playground.entity.TrainJdProducts;
import com.yiyi.ai_train_playground.mapper.TrainJdProductsMapper;
import com.yiyi.ai_train_playground.service.jd.impl.TrainJdProductsServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * 京东商品详情服务测试
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
@DisplayName("京东商品详情服务测试")
public class JdProductDetailServiceTest {

    @Mock
    private TrainJdProductsMapper trainJdProductsMapper;

    @InjectMocks
    private TrainJdProductsServiceImpl trainJdProductsService;

    @Test
    @DisplayName("测试查询京东商品详情 - 成功")
    public void testFindJdProductDetail_Success() {
        // 准备测试数据
        Long teamId = 1L;
        Long wareId = 12345L;
        
        TrainJdProducts mockProduct = createMockProduct();
        
        // 模拟Mapper方法
        when(trainJdProductsMapper.findByTeamIdAndWareId(eq(teamId), eq(wareId)))
                .thenReturn(mockProduct);
        
        // 执行测试
        JdPrdDtlResponse response = trainJdProductsService.findJdProductDetail(teamId, wareId);
        
        // 验证结果
        assertThat(response).isNotNull();
        assertThat(response.getId()).isEqualTo(1L);
        assertThat(response.getWareId()).isEqualTo(12345L);
        assertThat(response.getJdProdDtl()).isEqualTo("小米手环7详细介绍...");
        assertThat(response.getJdProdImgList()).isEqualTo("https://img11.360buyimg.com/devfe/img1.jpg,https://img11.360buyimg.com/devfe/img2.jpg");
        
        log.info("✅ 查询京东商品详情成功测试通过");
    }

    @Test
    @DisplayName("测试查询京东商品详情 - 商品不存在")
    public void testFindJdProductDetail_NotFound() {
        // 准备测试数据
        Long teamId = 1L;
        Long wareId = 99999L;
        
        // 模拟Mapper方法返回null
        when(trainJdProductsMapper.findByTeamIdAndWareId(eq(teamId), eq(wareId)))
                .thenReturn(null);
        
        // 执行测试
        JdPrdDtlResponse response = trainJdProductsService.findJdProductDetail(teamId, wareId);
        
        // 验证结果
        assertThat(response).isNull();
        
        log.info("✅ 商品不存在测试通过");
    }

    @Test
    @DisplayName("测试查询京东商品详情 - teamId为空")
    public void testFindJdProductDetail_NullTeamId() {
        // 执行测试
        JdPrdDtlResponse response = trainJdProductsService.findJdProductDetail(null, 12345L);
        
        // 验证结果
        assertThat(response).isNull();
        
        log.info("✅ teamId为空测试通过");
    }

    @Test
    @DisplayName("测试查询京东商品详情 - wareId为空")
    public void testFindJdProductDetail_NullWareId() {
        // 执行测试
        JdPrdDtlResponse response = trainJdProductsService.findJdProductDetail(1L, null);
        
        // 验证结果
        assertThat(response).isNull();
        
        log.info("✅ wareId为空测试通过");
    }

    @Test
    @DisplayName("测试查询京东商品详情 - 数据库异常")
    public void testFindJdProductDetail_DatabaseException() {
        // 准备测试数据
        Long teamId = 1L;
        Long wareId = 12345L;
        
        // 模拟Mapper方法抛出异常
        when(trainJdProductsMapper.findByTeamIdAndWareId(eq(teamId), eq(wareId)))
                .thenThrow(new RuntimeException("数据库连接失败"));
        
        // 执行测试并验证异常
        Exception exception = assertThrows(RuntimeException.class, () -> {
            trainJdProductsService.findJdProductDetail(teamId, wareId);
        });
        
        assertThat(exception.getMessage()).contains("查询京东商品详情失败");
        assertThat(exception.getMessage()).contains("数据库连接失败");
        
        log.info("✅ 数据库异常测试通过");
    }

    /**
     * 创建模拟商品数据
     */
    private TrainJdProducts createMockProduct() {
        TrainJdProducts product = new TrainJdProducts();
        product.setId(1L);
        product.setWareId(12345L);
        product.setJdProdDtl("小米手环7详细介绍...");
        product.setJdProdImgList("https://img11.360buyimg.com/devfe/img1.jpg,https://img11.360buyimg.com/devfe/img2.jpg");
        return product;
    }
}
