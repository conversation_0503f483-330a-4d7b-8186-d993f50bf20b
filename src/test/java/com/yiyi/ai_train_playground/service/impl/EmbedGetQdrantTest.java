package com.yiyi.ai_train_playground.service.impl;


import com.yiyi.ai_train_playground.model.VectorData;
import com.yiyi.ai_train_playground.model.VectorSearchRequest;
import com.yiyi.ai_train_playground.model.VectorSearchResult;
import com.yiyi.ai_train_playground.service.QdrantService;
import com.yiyi.ai_train_playground.service.SuperBigModelInterface;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.stream.Collectors;

@SpringBootTest
public class EmbedGetQdrantTest {
    @Autowired
    private SuperBigModelInterface bigModelService;


    @Autowired
    private QdrantService qdrantService;

    String YIYI_COLLECTION = "train_prod_collection";

    @Test
    public void testEmbed() {
        List<String> texts = Arrays.asList("这个土豆是哪个城市的？");
        List<List<Double>> embeddings = bigModelService.embed(texts);
        //期望：[-2.125, -0.34765625, -3.890625, -2.828125, -1.6953125, 1.5390625, -1.0, -3.03125, 0.5703125, 1.0546875, 3.046875, 2.578125, 3.71875, 0.875,
        System.out.println("向量值是: " + embeddings.get(0));
    }


    @Test
    public void testInsert() {

        //直接暴力提取
//        Double [] vector = {-2.125, -0.34765625, -3.890625, -2.828125, -1.6953125, 1.5390625, -1.0, -3.03125, 0.5703125, 1.0546875, 3.046875, 2.578125, 3.71875, 0.875, 1.34375, 4.375, -0.8515625, -2.90625, -2.578125, -4.40625, 1.609375, 6.34375, 3.140625, -4.15625, 2.546875, -4.125, -1.78125, 3.734375, 1.3984375, 0.86328125, 4.03125, -1.359375, 1.390625, 0.2431640625, 3.25, -3.65625, -4.6875, -3.734375, 4.75, 4.65625, -5.1875, 0.158203125, -2.140625, 1.84375, -1.84375, 0.10546875, -0.73828125, -2.796875, 0.373046875, -3.171875, -3.03125, -1.46875, 4.5, -3.046875, 6.78125, -5.1875, -6.125, 4.28125, -5.40625, -4.71875, 1.734375, -4.25, 1.1796875, -0.12109375, 2.421875, 6.0, 1.4296875, 0.6484375, -2.484375, 1.265625, -0.72265625, 1.84375, 2.640625, 0.71875, 7.59375, 3.609375, 1.75, 2.59375, -1.0546875, 4.09375, -6.21875, 2.0625, 5.625, 0.330078125, 6.40625, 1.1640625, -0.162109375, 2.953125, -3.859375, 2.03125, 0.0400390625, -0.279296875, -5.6875, -2.390625, 1.109375, 1.375, -0.17578125, -4.0625, -4.375, 2.0, -2.765625, 3.375, -0.92578125, -7.125, -1.3046875, -2.234375, -3.734375, -5.375, -0.94921875, -3.59375, 1.1875, 1.5390625, 4.6875, -1.4375, 4.59375, 2.734375, 1.375, -4.75, -3.859375, -0.5625, -1.2578125, 2.09375, 1.3828125, -0.44140625, 3.953125, 1.25, -3.25, 3.171875, 5.03125, -0.84765625, 3.265625, 4.25, 4.5, -0.400390625, -8.0, -2.71875, -2.28125, 4.5, 5.3125, 1.7578125, -6.3125, 0.263671875, -4.96875, -8.3125, 2.8125, -5.8125, -7.3125, -0.984375, 3.15625, -0.2294921875, -0.82421875, 0.9921875, -1.1875, 1.6875, -4.40625, -0.48046875, 2.46875, 3.1875, -0.318359375, 0.95703125, -1.3125, 4.03125, 1.0546875, 3.40625, -0.3515625, 0.52734375, -3.859375, 3.921875, -0.078125, 1.4765625, -0.9375, 13.8125, 4.78125, -3.234375, 0.306640625, 7.09375, -1.1640625, 5.28125, 6.0625, 0.353515625, 0.369140625, -1.5703125, -2.125, 4.03125, -1.2265625, -0.4140625, 0.859375, -3.5625, 2.171875, 2.359375, 4.125, 0.62109375, 0.55859375, -0.1279296875, -4.78125, -4.6875, 5.09375, -1.4453125, 0.333984375, 2.875, 0.48828125, -8.8125, -1.5546875, 0.65625, -3.921875, -1.828125, 2.546875, -3.09375, 0.19921875, 2.578125, 1.75, -1.265625, 3.34375, -0.51953125, 7.96875, 2.484375, 4.5625, -0.5859375, 1.4609375, 1.453125, 5.09375, 5.78125, -0.546875, -1.2265625, 2.421875, 1.8359375, 7.53125, 5.625, 3.890625, 7.21875, 1.5, 0.2431640625, 8.875, -2.765625, 0.373046875, -5.0625, -5.25, 1.8125, -2.609375, 2.796875, -4.84375, -3.234375, -2.390625, 0.70703125, 2.578125, -2.15625, -2.984375, -0.9453125, -0.5, -7.53125, 1.9296875, 2.625, -4.9375, -2.109375, 3.859375, 1.9765625, 0.5078125, 4.8125, 0.220703125, -1.9765625, 2.03125, -5.34375, -7.21875, 0.4453125, 3.59375, -1.1484375, 2.140625, -1.0859375, 2.25, -0.369140625, 5.3125, -1.3359375, 1.0, -2.5, -5.03125, 3.53125, -0.87109375, -2.328125, 4.46875, -4.34375, -2.25, -6.25, 5.34375, 1.59375, 6.0625, -2.59375, -0.62109375, 1.96875, -2.125, 13.0625, 3.90625, -1.1875, 3.09375, -4.1875, -4.8125, -0.8515625, -1.4140625, 1.328125, -2.921875, -0.87890625, 2.265625, 6.0625, -1.40625, 1.2890625, 2.015625, -1.71875, -2.46875, -1.046875, -0.2099609375, 2.515625, -3.1875, 3.90625, 6.90625, -1.5625, -0.103515625, -1.8359375, -4.15625, 5.90625, -0.88671875, 0.87890625, -1.8359375, -4.1875, -1.8671875, -5.0625, -4.3125, -5.34375, 5.53125, -1.140625, 4.96875, -0.443359375, 1.578125, 1.0390625, -7.46875, -6.0, 6.0625, -1.546875, 1.828125, 3.984375, -2.46875, 2.5, -3.375, -1.796875, 2.46875, -3.09375, -1.3203125, 2.03125, 1.5625, -0.671875, 5.0, -7.34375, 2.53125, 4.53125, -3.6875, 4.4375, 6.78125, -3.859375, 0.66796875, 2.078125, -1.6015625, 1.15625, -0.484375, -2.078125, 6.1875, 0.09033203125, -0.267578125, -1.9921875, -0.158203125, 3.875, -1.296875, 0.80078125, -1.3125, -0.94921875, 2.53125, 0.8984375, 5.28125, -2.953125, -1.5390625, -2.59375, -7.6875, -0.333984375, 0.1455078125, -1.6484375, -1.640625, -3.34375, 4.65625, -0.2275390625, 2.0625, -1.875, -1.8359375, -3.0, -1.4921875, -0.77734375, -0.7890625, -2.625, 2.578125, -2.984375, -3.203125, 3.234375, -0.53515625, 0.75390625, 3.671875, -2.125, 0.84765625, -2.75, 0.5859375, 0.09765625, -1.65625, -10.3125, 4.59375, 1.046875, -4.625, 1.1171875, -0.09033203125, -2.21875, -3.3125, 5.8125, -0.5078125, 1.0625, -4.875, -2.390625, -4.0625, -0.2177734375, 4.25, -0.94140625, 0.053466796875, -0.890625, -1.4375, 3.125, 2.625, 1.59375, 2.078125, 4.1875, -7.15625, -2.375, -0.875, -2.421875, 4.78125, -2.203125, -1.7890625, 4.3125, 1.3359375, 0.796875, 3.015625, 0.9375, -7.84375, 2.734375, 1.1484375, 3.59375, -0.83984375, -2.5625, -2.796875, 0.62109375, -4.09375, -0.0673828125, 3.609375, 0.322265625, -3.5, 7.0, 4.25, -1.6171875, 1.9921875, 1.8125, -0.58984375, 1.890625, 0.87109375, -3.53125, -2.484375, -0.447265625, 4.875, -5.3125, -6.875, 5.46875, -3.171875, 0.1474609375, -0.8828125, 3.515625, 0.055908203125, -1.7421875, -2.125, -1.7578125, -0.408203125, -2.875, 3.578125, -1.21875, 1.453125, -3.578125, 2.859375, 9.1875, 1.15625, 0.6171875, -3.046875, 2.40625, 4.875, -0.1337890625, 1.4296875, 6.3125, 8.375, -2.796875, -4.53125, 0.248046875, -6.25, 1.4609375, -3.046875, -1.5, -1.0390625, 2.875, 1.5, -4.65625, -2.75, -2.34375, 2.046875, -1.734375, -2.671875, 1.546875, -4.84375, -4.8125, 0.515625, 2.15625, 3.4375, -0.7109375, -1.6484375, 0.62109375, -0.08349609375, 2.84375, -0.032958984375, -3.453125, -1.375, -3.296875, 0.9140625, 2.46875, -0.0869140625, 1.609375, 2.046875, -7.09375, -4.4375, -4.71875, 0.69140625, -0.443359375, -1.734375, 3.03125, 5.09375, 2.359375, -5.84375, 1.515625, 0.2275390625, 2.09375, -0.96484375, -1.921875, 4.78125, 2.203125, -1.0078125, 0.578125, -3.6875, 0.5703125, 1.5546875, -4.84375, -1.453125, 0.421875, 0.08154296875, -1.0546875, -1.375, -5.46875, -1.0234375, 5.09375, -2.296875, -5.90625, 2.515625, 6.9375, -0.271484375, 0.4765625, 3.8125, 3.46875, -1.75, -1.328125, -5.65625, 3.65625, -4.90625, -5.25, -3.40625, 3.03125, 3.859375, -4.3125, 2.90625, -2.359375, 3.921875, -0.13671875, 2.78125, -10.6875, 2.1875, -2.890625, -3.984375, -2.484375, 0.1748046875, 5.5625, -2.40625, 1.0, 2.328125, -0.765625, 5.09375, 5.9375, -3.234375, 1.890625, 4.78125, 1.1171875, 1.3984375, 3.609375, 0.451171875, -2.890625, 2.359375, -1.1953125, -0.8203125, 6.5, 1.546875, -1.6484375, 1.5390625, 0.6328125, -1.0625, 2.40625, 2.03125, 2.640625, -4.15625, 3.703125, -2.484375, 5.0625, -3.5, 3.875, -2.875, -5.625, 1.25, 3.265625, -1.640625, -6.21875, 3.703125, -0.1845703125, 5.0625, 5.375, -6.46875, -0.72265625, -0.78515625, 1.2578125, 5.15625, 0.59765625, 1.4921875, -7.15625, 2.265625, -1.0390625, -1.203125, 4.1875, -3.515625, -2.125, -0.80078125, 4.09375, -3.265625, -0.2265625, -0.322265625, -0.390625, 1.03125, -3.390625, 1.734375, -1.3359375, -4.96875, -4.40625, 2.453125, 1.796875, 7.03125, -1.4921875, 0.75, -4.5625, 0.46484375, 2.390625, 5.0625, 3.890625, 3.265625, 3.21875, 2.4375, 0.83984375, 5.71875, -0.07080078125, -0.7734375, 3.828125, -5.65625, -1.9921875, 2.125, 0.8203125, -0.1669921875, -2.453125, -0.0810546875, -1.7734375, -4.375, -1.59375, -3.265625, -3.140625, 4.15625, -4.21875, 4.40625, 0.78515625, -2.171875, -1.3203125, -2.71875, -4.125, -2.421875, 3.09375, -6.40625, -0.44140625, 5.0, 3.578125, 0.376953125, 3.328125, 2.546875, -0.5859375, -0.41015625, 2.1875, -5.59375, 2.109375, 3.75, -3.515625, -1.53125, -5.0625, 1.8828125, -4.625, 3.0625, -1.0, -1.453125, -4.9375, -2.484375, -2.625, 0.98046875, -4.40625, 2.0625, 1.2109375, -1.0078125, -1.8828125, 1.625, 4.875, -1.5078125, -1.4765625, 1.8359375, 0.94140625, 2.5, -0.24609375, -4.40625, -2.21875, -1.15625, 4.25, -1.265625, 3.8125, -0.75, 0.46875, 5.25, 2.125, -5.25, -5.0, 5.5625, -0.58984375, -2.25, 1.328125, 4.90625, 0.26953125, 0.83984375, 1.0625, -1.703125, -5.125, 2.234375, 1.4296875, -1.859375, -4.875, 2.125, 2.015625, 6.21875, -5.1875, -5.46875, -0.388671875, 1.15625, -0.48828125, -0.05419921875, 9.9375, 0.0556640625, -0.7421875, -2.6875, -3.65625, 0.09326171875, 1.703125, 0.48828125, 0.166015625, -1.515625, 0.65625, 5.25, 1.3671875, -0.2734375, -0.6640625, -0.81640625, -1.03125, 0.91796875, 0.703125, -0.98828125, -1.5078125, -0.75390625, 0.80078125, -0.78125, 3.9375, 7.65625, -0.92578125, -2.328125, 0.69921875, 0.94140625, -2.4375, 4.6875, 2.046875, 6.53125, -0.77734375, -2.0625, 0.75, -2.1875, -1.0390625, 4.1875, -5.46875, -1.8046875, -1.6953125, 4.8125, -1.796875, 0.69140625, -2.21875, 1.578125, 1.3203125, -1.765625, 0.91796875, 0.279296875, 0.8828125, -1.3984375, 0.46484375, 0.38671875, -5.5625, -0.7578125, 0.75, 0.6328125, -0.86328125, 0.255859375, 0.3359375, 5.21875, -4.875, -1.328125, -1.90625, 2.46875, 0.6953125, 0.66015625, 0.384765625, -3.359375, 1.859375, 1.8203125, -3.109375, -1.1875, -1.234375, 8.9375, 3.203125, -5.53125, 1.125, -0.2099609375, 1.8125, -6.3125, -2.65625, -0.921875, 0.1689453125, -2.703125, -0.009521484375, 1.6328125, -0.1669921875, -3.25, 2.09375, -4.3125, 0.337890625, 0.2578125, 1.6015625, -1.9375, 1.5078125, -0.1982421875, 3.84375, 3.03125, -3.453125, -3.453125, -3.59375, 1.5, -2.25, -2.359375, 2.453125, 7.875, 0.8046875, -1.078125, -3.390625, 1.6796875, -2.5, 1.8359375, 1.203125, -3.15625, -1.8828125, -2.75, -11.6875, -4.5, -1.578125, -2.296875, -2.671875, -4.40625, -0.9296875, -2.859375, -8.125, 0.2734375, -3.875, -1.328125, 2.8125, 0.400390625, 1.34375, -2.640625, 1.03125, 4.125, 2.1875, 2.171875, -2.125, 2.53125, 2.3125, -1.265625, 1.828125, -2.75, 5.90625, -1.9765625, 5.3125, 5.84375, 0.53125, -4.6875, 4.4375, 2.515625, 1.0703125, -8.375, -1.4609375, -2.0625, -0.6171875, 0.314453125, -0.67578125, 2.96875, -0.90234375, -4.5625, 0.142578125, -3.234375, -1.84375, 3.453125, -3.34375, 4.28125, 1.0625, -0.40234375, 1.1640625, 1.6875, 1.0703125, 1.0078125, 3.25, -4.90625, 2.875, -3.34375, 2.625, -1.421875, 2.078125, 2.265625, 2.046875, -0.091796875, 2.125, 1.0234375, 6.59375, 2.984375, 2.734375, -0.049072265625, 2.109375, -1.9296875, -0.7890625, 2.296875, 2.203125, 0.2294921875, -1.7265625, 1.6953125, -0.27734375, 2.71875, -3.09375, -8.125, -0.002288818359375, -2.140625, -3.53125, 6.6875, -2.78125, -2.359375, 3.90625, 2.25, 4.90625, 7.40625, -0.83984375, 2.578125, -0.0537109375, -1.6171875, 4.0625, -6.625, 2.34375, 2.0625, 4.78125, 4.78125, 0.09326171875, -1.6953125, -2.671875, 3.765625, 3.328125, -2.90625, 0.98828125, 4.59375, 1.578125, -2.265625, 3.40625, -2.03125, 2.96875, -5.21875, 2.359375, 3.703125, -0.140625, -2.625, -3.859375, -3.25, -1.84375, -2.046875, 0.47265625, -1.765625, 3.421875, 4.21875, -4.03125, 2.59375, -2.84375, -0.494140625, -3.46875, -0.640625, 3.6875, 1.25, -2.9375, -0.053466796875, 1.875, -0.17578125, 1.5703125, 2.671875, -1.875, -1.3671875, 2.515625, 0.0693359375, -1.203125, 2.453125, 4.625, 2.75, -3.375, -0.462890625, -1.7421875, 0.78125, -0.2265625, -0.9765625, -1.6953125, 5.34375, 0.427734375, -3.328125, 0.47265625, 1.71875, 0.1416015625, 1.5703125, -5.0, -2.03125, 1.25, -0.5390625, -2.9375, 1.3671875, -4.34375, -0.96484375, 6.09375, -4.5625, -0.63671875, -3.375, 8.5, -0.265625, -2.296875, 2.1875, -1.8359375, 3.296875, -2.484375, -3.1875, 2.5625, -3.390625, 0.474609375, -1.9921875, 3.21875, -1.09375, 2.140625, 5.1875, -0.0181884765625, -2.53125, 1.0234375, 4.75, -0.921875, -2.1875, 1.9921875, 1.421875, 0.2353515625, -4.96875, 4.34375, -2.625, 6.40625, -3.703125, 0.439453125, -6.09375, -0.74609375, 0.60546875, 0.515625, 7.5625, 4.5625, -2.171875, -4.125, 2.90625, -4.40625, -4.0625, -1.203125, 6.5625, 0.1220703125, 0.3359375, 0.95703125, -0.0211181640625, -2.75, 2.0, -2.265625, 1.671875, 2.109375, 0.1484375, 2.1875, 0.90234375, 1.984375, -4.875, 0.439453125, -0.85546875, 4.875, 1.5078125, 0.55859375, 1.7109375, -1.5703125, 3.890625, -0.24609375, -3.0625, -2.984375, 4.21875, 3.5, -0.337890625, -3.28125, 2.390625, -2.9375, 1.890625, -5.8125, -3.46875, -5.9375, 5.59375, 4.0625, 1.2734375, -0.1865234375, 4.96875, -1.078125, 2.03125, -5.53125, -4.53125, 6.5625, 2.5, 0.205078125, 5.4375, 0.89453125, -2.765625, 0.37109375, -0.52734375, 0.71484375, -1.1171875, -2.265625, 5.40625, 2.046875, 3.5625, -3.90625, -3.359375, -4.875, 2.59375, 3.65625, -2.28125, -2.03125, 1.8515625, -1.625, -0.390625, 0.451171875, 5.71875, -4.65625, -5.96875, -2.4375, 3.53125, 1.0859375, 0.2734375, 2.1875, 1.3203125, 0.1396484375, 2.859375, 2.984375, -1.2890625, -1.9296875, 4.125, -0.59765625, -1.75, -3.4375, 3.078125, -2.0625, 1.9296875, -1.5, -5.5, -2.984375, -1.8984375, 1.2421875, 1.15625, 2.625, 4.59375, -2.375, 3.96875, 1.234375, -0.9375, 0.58984375, 3.484375, -0.0264892578125, -3.171875, 2.5625, -2.453125, -3.453125, 1.0, -7.40625, -0.80859375, 3.6875, -0.11279296875, 0.9140625, -1.6171875, 4.4375, -5.5625, -3.65625, -3.78125, -4.125, 2.359375, 5.0625, -0.80859375, 0.80859375, 0.6171875, 1.5546875, 1.3125, -4.5, 2.75, 0.3046875, 2.96875, 3.359375, 2.46875, -3.390625, -1.0078125, 2.828125, -3.96875, -3.03125, 0.7109375, -1.3671875, 0.1171875, 3.234375, -0.291015625, -3.390625, -3.609375, -3.71875, 2.171875, -0.84765625, 1.0234375, -3.609375, 2.078125, -1.46875, 0.65625, 4.46875, -4.40625, -1.4453125, 3.484375, 2.359375, 2.8125, 3.859375, 0.60546875, -0.58984375, 2.203125, 0.275390625, -1.7578125, -4.0, -3.609375, -1.359375, -3.96875, 3.65625, -3.4375, 3.375, -4.8125, -0.67578125, -1.828125, -0.7109375, 1.78125, 0.9375, 2.28125, 0.92578125, 2.75, -1.875, 4.3125, 0.0947265625, -3.171875, 3.125, 2.71875, 0.84765625, -1.0859375, 2.984375, -1.4296875, -1.0, 1.9140625, 2.8125, -4.90625, 3.9375, 1.125, 0.83984375, -2.859375, -2.578125, -1.5859375, 2.546875, -2.296875, 1.5546875, -5.71875, -0.2138671875, 6.0, -1.0078125, 1.3125, -0.5234375, 1.765625, -2.46875, -0.984375, 4.4375, 1.5703125, 4.40625, -0.79296875, 0.359375, 1.859375, -0.380859375, 2.140625, 0.75, -0.3125, -2.703125, 3.65625, 0.80078125, -3.359375, -6.1875, 1.9609375, -4.34375, 2.296875, 0.365234375, 2.296875, 2.453125, 0.18359375, -1.2109375, 2.03125, -3.6875, -0.8203125, 2.625, 0.77734375, -3.28125, 2.296875, 1.3203125, 2.59375, 5.25, -1.8046875, 3.875, -2.0, -1.796875, -1.703125, 1.265625, -2.015625, 1.6015625, -0.41015625, -1.375, 1.0078125, -4.0, 2.8125, -4.5625, -1.2421875, -3.546875, -2.890625, 1.6640625, 4.65625, -3.296875, -4.125, 4.90625, 1.4453125, 3.875, 0.78125, -2.265625, 0.83984375, 2.015625, -2.265625, 6.0, 3.21875, 2.71875, -2.8125, 8.6875, 5.1875, 1.96875, 6.21875, 2.25, -0.5546875, -5.4375, 1.96875, 4.09375, 4.59375, -1.109375, 4.0, 4.25, 2.234375, 1.3046875, -8.0625, 3.6875, -1.2734375, -0.42578125, -2.1875, -4.4375, 0.64453125, 3.84375, 0.009033203125, -2.5625, 5.625, 1.546875, 1.3203125, -2.984375, 3.0, 2.6875, 4.3125, -3.578125, -0.6484375, -4.75, 1.53125, -2.9375, -0.232421875, -0.67578125, -1.1015625, 6.6875, 4.0, -1.3203125, -4.03125, 1.5, 1.3203125, -1.46875, -3.578125, 5.34375, 2.59375, 0.46875, -3.953125, -1.46875, 1.0390625, 0.83203125, 0.2060546875, -2.703125, -0.7265625, 1.203125, -2.03125, -4.46875, 3.5625, 5.28125, -3.53125, -4.5625, -3.671875, 1.4140625, 4.28125, 4.4375, 3.890625, -3.203125, 5.5625, 0.3671875, 6.5, 2.734375, -1.03125, -1.5703125, -1.9453125, 0.004119873046875, 4.21875, 0.2578125, -5.03125, 3.859375, -0.236328125, -0.87890625, -0.109375, 0.62890625, -0.369140625, -4.3125, -1.3828125, 0.98828125, 1.734375, 5.96875, -5.125, -1.859375, 3.703125, -4.84375, -1.5, 0.9140625, 0.76171875, -2.390625, -1.21875, -0.9453125, 5.1875, 2.0625, 3.0, -0.333984375, 0.9765625, -1.15625, -5.0, -3.9375, 1.3359375, -2.84375, 2.328125, -1.9609375, -3.1875, 0.7734375, 3.125, 4.6875, 1.9296875, -2.171875, -0.046875, 3.34375, -0.466796875, -3.75, -1.2734375, 0.89453125, -3.296875, -3.78125, -3.0, 0.37109375, -1.046875, -0.90625, -5.03125, -1.8671875, -0.77734375, 1.4453125, 3.34375, 0.91796875, -4.5, -2.40625, -2.234375, -0.03857421875, -2.671875, 1.4765625, -0.205078125, 2.609375, 2.5625, -2.6875, -3.828125, 4.71875, 17.125, 0.66796875, -2.828125, -0.2421875, -5.21875, 4.96875, 3.171875, 2.046875, -1.90625, 2.28125, 0.3359375, 3.71875, -0.9609375, 3.59375, -2.5, -0.78515625, 0.57421875, 3.25, -2.296875, -5.75, -0.84375, 3.421875, 1.703125, 1.9765625, 0.2177734375, 0.97265625, -0.326171875, 0.07568359375, -2.375, -4.125, -6.46875, -3.6875, 5.15625, -0.47265625, 5.4375, -1.84375, 4.53125, 1.2578125, -1.546875, -0.28515625, -4.03125, -3.90625, -0.38671875, 0.498046875, -1.3203125, -0.111328125, -0.0732421875, 0.2080078125, -2.703125, 1.171875, -2.5, -1.1484375, -3.828125, 3.96875, 3.453125, -8.125, 0.7578125, -6.03125, -3.390625, -0.8125, 0.9453125, -2.75, 3.3125, 2.65625, -3.859375, -4.96875, -3.890625, 0.150390625, 1.1640625, -4.75, -2.8125, -0.298828125, 1.8828125, 4.90625, 0.1689453125, -0.1650390625, 2.328125, 3.828125, -0.26953125, 1.71875, -1.015625, -2.171875, -0.92578125, -2.328125, 0.0908203125, -0.53125, -4.03125, 0.53515625, 2.171875, -3.484375, 11.25, -4.1875, 4.40625, 6.65625, -2.578125, -0.609375, 3.609375, 2.6875, -0.43359375, -0.60546875, 2.9375, 2.859375, 2.828125, 1.0546875, 3.390625, 2.25, -0.58984375, 0.1640625, 2.796875, 2.90625, 0.189453125, 2.375, -1.078125, -2.109375, 3.828125, 2.75, -4.53125, -0.65234375, -2.109375, -4.625, 3.890625, 2.65625, -3.71875, -0.33984375, -11.1875, -3.078125, -3.0, -4.71875, 3.265625, -2.359375, -0.3515625, 2.75, -0.9765625, 1.1796875, -3.28125, -2.703125, 3.46875, 3.546875, 0.78125, -3.4375, -4.46875, 2.703125, 2.75, -4.75, 0.10986328125, 1.15625, -6.15625, 1.8515625, -0.47265625, 4.625, -2.203125, 3.953125, 5.6875, -0.6875, -5.125, 4.125, -1.8828125, -6.84375, -5.4375, -5.625, -1.5390625, 0.275390625, -0.61328125, -0.66015625, 0.91796875, -2.609375, 5.46875, -1.5625, -2.90625, 2.359375, 1.2421875, -4.0, -1.3359375, -0.3359375, -1.140625, -4.6875, -1.734375, 6.65625, -3.6875, 1.1171875, 2.5, 1.0703125, -1.796875, -3.3125, 1.1640625, -0.66796875, -0.640625, -1.2421875, -0.2099609375, -1.8125, -2.9375, -3.875, -3.6875, 4.28125, 2.5, -4.46875, -0.85546875, -1.34375, 1.0234375, -1.71875, -2.640625, 4.03125, 1.3125, 0.419921875, 5.84375, -0.75390625, -5.34375, -1.015625, -3.484375, -2.921875, -2.46875, 4.03125, 5.15625, 3.78125, -5.8125, 5.8125, 1.65625, 0.9375, -0.859375, -1.8203125, 4.75, -1.109375, -4.21875, 2.4375, 1.15625, 3.3125, -3.40625, 1.296875, -1.3515625, -0.310546875, -0.3515625, 2.703125, 1.1796875, 2.75, -3.734375, -5.28125, -1.2578125, -1.484375, 0.345703125, 4.21875, 4.71875, -0.5703125, 0.1572265625, -0.94140625, -3.671875, 2.375, 6.03125, -3.625, -1.390625, -1.375, 0.58984375, -0.88671875, 1.40625, -1.796875, -3.984375, -2.5, -3.8125, -2.6875, -0.87890625, -0.9921875, 1.96875, -5.34375, 4.0, -2.890625, -1.515625, -1.78125, 1.703125, -5.15625, 4.8125, 0.11083984375, -0.609375, 3.890625, -2.375, -4.03125, -2.234375, 2.9375, -0.388671875, -4.6875, 2.046875, 2.6875, -2.171875, 2.484375, 2.703125, -2.8125, 0.65625, -3.09375, 5.0625, -2.5625, -0.1591796875, -1.6875, 3.546875, 1.5390625, 0.23046875, 0.02490234375, -5.96875, -0.46875, -2.15625, -3.078125, 6.15625, -3.359375, 2.34375, 3.09375, 2.5625, -5.6875, -2.59375, -0.6953125, 1.40625, 3.046875, 3.09375, -2.34375, -1.1015625, 1.296875, 0.58203125, 0.7265625, -0.134765625, -2.75, -1.46875, 4.46875, 0.61328125, -3.09375, 2.453125, 2.046875, -1.2578125, 1.65625, -1.234375, -1.1640625, -2.828125, 1.515625, -5.25, 1.6171875, 3.75, -0.45703125, 2.96875, 0.8515625, -2.140625, 4.65625, -3.109375, -8.3125, -0.8046875, 1.828125, 2.609375, 2.046875, 3.4375, -1.9453125, -0.8984375, 3.15625, -2.328125, 1.28125, -0.421875, -8.6875, 3.234375, -2.015625, -0.80859375, -4.5625, -0.23046875, -1.6171875, 4.4375, 1.7265625, 2.265625, 2.890625, 1.6015625, 0.86328125, 1.96875, -0.361328125, -3.84375, 1.8984375, 0.462890625, -3.203125, -0.51953125, 0.345703125, 1.8671875, -4.15625, -1.65625, 5.03125, -1.2265625, -3.6875, 4.96875, 7.25, 1.5703125, 1.28125, -5.25, 2.140625, -1.5703125, -4.03125, 3.765625, -3.203125, 0.6640625, -0.73046875, 1.6640625, 2.71875, -4.40625, -1.4765625, -3.671875, -3.515625, -0.12158203125, 1.5625, -0.2275390625, -3.828125, -5.15625, 2.96875, 6.0625, -1.2890625, -4.0625, 2.265625, -3.40625, -0.71875, 0.1806640625, 1.0859375, -5.5625, -1.4765625, 5.75, 3.3125, 2.734375, -1.9296875, -2.390625, 1.8125, 7.3125, -1.5546875, -2.5, 0.044921875, 1.9375, 1.0390625, -1.8515625, -4.5625, -4.0, -1.4765625, -5.25, -5.375, -1.109375, -1.59375, 0.85546875, 2.546875, -2.546875, 4.75, 4.0, -0.7265625, -3.390625, 5.53125, 0.71484375, -1.1875, -3.203125, 2.46875, 1.4453125, 2.875, 5.6875, -4.03125, -3.609375, 0.2080078125, -3.84375, -4.875, 1.7109375, 3.6875, -1.828125, -7.4375, 6.875, 1.390625, 4.40625, 2.109375, -2.140625, 4.0625, 0.2373046875, 2.65625, -3.03125, 2.234375, 0.21875, -6.96875, 0.875, -4.0625, -5.34375, 2.015625, -0.76953125, -0.2109375, 1.3203125, 6.96875, -0.71875, 0.80859375, -0.1708984375, -1.71875, 3.625, -1.984375, -6.78125, 2.046875, -7.5, 0.177734375, -3.21875, 2.71875, -4.28125, 1.875, -2.46875, 1.8984375, -4.84375, 1.4140625, 1.921875, 0.419921875, 1.4453125, 5.34375, 2.890625, 1.2421875, 1.5234375, 4.1875, -1.2265625, -0.91796875, -1.1953125, -2.671875, 0.408203125, -2.6875, 0.177734375, 1.28125, 0.314453125, 0.87109375, -0.287109375, -1.3359375, 2.046875, 2.15625, 0.5703125, 1.3125, 0.4453125, -0.46875, -0.53515625, -3.109375, -1.6953125, -0.75, -2.796875, 0.99609375, 0.64453125, 0.7265625, -2.0625, 0.068359375, -0.4140625, -1.4921875, 1.4921875, -2.890625, 0.828125, 2.515625, -1.2109375, -0.1787109375, -0.298828125, -1.078125, 1.4765625, -1.1875, 2.34375, 0.75390625, 1.640625, -0.796875, -1.3359375, 2.34375, 2.328125, -0.7421875, -0.36328125, -0.95703125, -1.2265625, 0.421875, 0.5625, -0.126953125, 0.9765625, -2.703125, 2.59375, 1.3046875, 0.1201171875, -2.0, 0.85546875, 0.166015625, -1.484375, -1.4140625, 3.171875, -1.1484375, 1.4921875, 0.62109375, 2.1875, -0.55078125, 0.51953125, 0.1328125, 1.984375, -3.625, -0.10595703125, -0.8828125, 1.578125, 2.09375, 1.09375, -0.5390625, 0.7890625, 0.158203125, -1.3515625, 1.046875, 0.81640625, -0.71484375, 0.6953125, -1.546875, -2.515625, 1.90625, 1.125, 2.015625, 0.83203125, 0.6875, 1.4609375, 1.7109375, -0.1201171875, 1.5, 1.4375, 0.62109375, 1.2421875, 2.296875, 1.046875, 0.431640625, -1.3515625, 1.5234375, -0.625, 0.1650390625, 2.015625, -0.01531982421875, -1.078125, -2.03125, -0.5546875, -1.109375, -0.62890625, 0.263671875, -0.7265625, 1.28125, -1.5546875, -0.064453125, 2.421875, 0.47265625, 0.2197265625, 1.1015625, 0.279296875, -0.66796875, 1.3984375, -1.3125, -1.7109375, 0.53515625, -0.9765625, -0.65234375, 1.46875, -0.5625, 0.51171875, -0.96484375, 0.89453125, 2.65625, -0.82421875, -0.6953125, 1.3203125, -1.875, -0.08251953125, -1.6328125, -0.42578125, -1.1640625, 2.765625, 0.703125, -0.27734375, -0.06982421875, -3.265625, 1.390625, -1.375, 1.8125, 2.4375, 1.8046875, -2.296875, -0.44921875, 1.0546875, -0.421875, -0.05712890625, 1.671875, -0.7265625, 2.078125, -0.39453125, 3.359375, 1.375, 1.484375, 0.75390625, -0.455078125, -0.85546875, 1.4140625, 1.78125, -0.24609375, 1.359375, 1.0546875, -1.0078125, -1.8515625, 1.9453125, 0.404296875, -0.73828125, 2.421875, 0.98046875, -0.75390625, -0.56640625, 1.8203125, -1.3203125, -0.90625, 1.4765625, -1.765625, -2.421875, 0.69921875, -1.1875, 0.7890625, -0.78515625, 3.609375, 2.375, -0.67578125, -1.5390625, -0.71875, 0.2109375, 0.6171875, 1.53125, 0.484375, 0.408203125, -1.1484375, -0.4609375, -0.1083984375, 0.56640625, -0.2578125, 2.53125, -1.25, -0.453125, -0.423828125, 1.40625, -2.609375, 1.0546875, 2.234375, -1.0546875, -1.828125, -0.53515625, 2.03125, -2.75, -0.1435546875, -0.2236328125, -0.69921875, -0.047119140625, 1.5703125, -1.203125, -2.421875, -2.296875, 1.015625, 2.171875, 0.28515625, 1.0859375, 2.703125, -2.046875, -1.9375, 1.203125, 0.6328125, -1.4296875, 3.78125, -2.015625, 0.71875, 1.265625, -4.03125, -1.4140625, 0.27734375, 0.54296875, -1.015625, 0.0216064453125, -0.37890625, -1.515625, 0.263671875, -0.64453125, -0.478515625, -2.0, 1.6484375, -0.8828125, -0.515625, -0.1845703125, -0.018798828125, 1.609375, 0.36328125, -0.70703125, -0.212890625, 0.56640625, 1.8203125, 1.1484375, -0.255859375, 0.0084228515625, -0.1787109375, 1.6328125, -3.09375, -1.6953125, -0.85546875, -0.9765625, -2.296875, 0.09130859375, 1.53125, -2.515625, -0.0791015625, 0.419921875, 1.625, -2.71875, 2.203125, 0.0888671875, -1.6796875, -1.9140625, -0.216796875, -0.35546875, -0.88671875, 0.6640625, 0.3828125, 0.97265625, -1.1328125, 1.6328125, 0.9140625, -0.86328125, 1.3359375, 1.4609375, 0.59765625, 0.984375, -0.09423828125, -0.322265625, 0.306640625, -1.0078125, -1.7890625, -4.34375, 0.53125, 1.8203125, -2.03125, -0.328125, -0.044677734375, -0.2490234375, -0.67578125, -4.1875, 0.9921875, 0.333984375, 0.4375, -0.359375, -0.765625, -0.10009765625, 1.21875, -1.625, -0.392578125, 1.2734375, -0.255859375, -0.90234375, -1.0234375, 2.6875, -2.28125, 1.734375, -0.98828125, -1.6015625, -1.0625, -1.6484375, 1.4453125, 0.68359375, 0.76953125, 1.8203125, 0.19921875, -0.31640625, 1.0, 1.671875, 2.5, -0.004547119140625, -0.3671875, 0.68359375, 0.26171875, -0.56640625, 0.345703125, -2.03125, 1.703125, -0.28515625, 1.9765625, 2.78125, 0.0228271484375, -5.0625, 0.51171875, 0.357421875, -1.015625, 1.5390625, 0.85546875, 2.1875, 1.6484375, -0.1669921875, -0.36328125, 1.578125, 0.2451171875, -1.9609375, 3.390625, 1.7109375, 0.8828125, -0.8828125, 0.8671875, -2.640625, -1.0703125, 1.25, 0.474609375, -2.609375, 2.109375, 1.109375, -0.33203125, -0.01025390625, -0.2236328125, -0.5625, 0.4296875, 1.4609375, -1.984375, -3.28125, -1.421875, 1.640625, 0.890625, 2.21875, -0.859375, -0.302734375, -1.109375, 0.478515625, 1.5390625, 0.494140625, 0.55078125, 3.546875, 0.859375, -1.296875, 1.15625, 0.91015625, 1.8125, 1.2109375, -2.3125, -2.4375, 1.5625, 3.078125, -1.8515625, 1.65625, -3.71875, 2.484375, 0.94921875, -0.46875, -1.921875, -4.0, -0.365234375, -0.54296875, 1.6875, -1.8046875, 0.390625, 2.8125, -1.0078125, -2.90625, 0.349609375, -0.333984375, -0.90234375, -1.4453125, 0.92578125, -1.7578125, -0.40625, -0.177734375, 1.2421875, -1.765625, 1.1796875, 0.7265625, 0.041748046875, -0.4140625, 0.52734375, 0.35546875, -0.9375, -1.796875, -2.265625, -0.328125, 1.1640625, 0.25390625, -1.1953125, -0.96875, 0.609375, -0.05810546875, -2.078125, -4.09375, 0.94921875, -2.0625, 0.83203125, 1.6171875, -2.546875, 0.298828125, 0.15625, -0.8671875, -1.2421875, 2.640625, 2.171875, -1.328125, -3.125, 1.5546875, -0.53125, -0.22265625, -2.09375, -0.318359375, -0.83203125, 1.359375, -0.796875, -2.265625, -2.859375, -3.359375, 0.671875, -2.203125, -0.06640625, -1.078125, 0.068359375, -1.2578125, 2.546875, -1.1640625, 1.671875, 1.734375, 0.50390625, -0.62890625, 1.5703125, -0.240234375, -0.44921875, -1.6640625, 0.97265625, 1.2890625, -2.171875, -1.3046875, 2.296875, 2.28125, -1.03125, -1.5078125, -0.01043701171875, -0.58203125, -0.50390625, 1.28125, -0.234375, -3.015625, -0.39453125};


        //将知识库先注入
        List<String> texts = Arrays.asList("品牌: 黑暗小镇 城市: 定西市 上市时间: 11月 10月 9月 售卖方式: 产地直销");

        String originalText1 = """
                品牌：苹果，型号：iPhone 15 Pro，屏幕尺寸：6.1英寸，屏幕类型：Super Retina XDR OLED，分辨率：2556 x 1179像素，像素密度：460 PPI，刷新率：ProMotion自适应10-120Hz，处理器：A17 Pro芯片（6核CPU，6核GPU），内存：8GB LPDDR5 RAM，存储容量：128GB/256GB/512GB/1TB NVMe SSD，操作系统：iOS 17（可升级至未来版本），
                """;

        String originalText2 = """
                后置摄像头：48MP主摄（f/1.78光圈）+12MP超广角（f/2.2）+12MP长焦（f/2.8，3倍光学变焦），前置摄像头：12MP TrueDepth（f/1.9），视频录制：4K 60fps HDR，支持Dolby Vision，电池容量：3274mAh，充电方式：20W有线快充，15W MagSafe无线充电，7.5W Qi无线充电，防水防尘等级：IP68（最深6米，30分钟），重量：187克，尺寸：146.6毫米（高）×70.6毫米（宽）×7.8毫米（厚）
                """;

        String originalText3 = """
        颜色选项：黑色美女金属，白色钛金属，蓝色钛金属，自然钛金属，网络支持：5G（sub-6GHz和mmWave），4G LTE，Wi-Fi标准：Wi-Fi 6E（802.11ax），蓝牙版本：5.3，NFC功能：支持Apple Pay，定位系统：GPS，GLONASS，Galileo，QZSS，北斗，生物识别：Face ID面部识别，材质构造：航空级钛金属边框，陶瓷屏蔽前盖，玻璃背板，SIM卡类型：双eSIM或eSIM + nano-SIM（因地区而异），音频特性：立体声扬声器，支持空间音频，无3.5mm耳机孔，传感器：三轴陀螺仪，加速度计，接近传感器，环境光传感器，气压计，其他功能：Always-On Display，卫星紧急求救（特定地区），MagSafe兼容，包装内容：iPhone 15 Pro主机，USB-C充电线，说明书，SIM卡针，保修期：1年有限保修，环保认证：RoHS合规，可回收材料占比90%，价格区间：7999元起（根据存储配置变化），发布日期：2023年9月，适用场景：日常使用，摄影，游戏，商务办公。
        """;

        String originalText = originalText2;


        List<List<Double>> embeddings = bigModelService.embed(texts);


        List<Float> floatList = new ArrayList<>();
        for (Double d : embeddings.get(0)) {
            floatList.add(d.floatValue());
        }


        Map<String, Object> payload = new HashMap<>();
        payload.put("teamId", "tianziyihao001");
        payload.put("externalProductId", 88889997);
        payload.put("completeText", originalText);

        VectorData vectorData = VectorData.builder()
                .id("3")  // 使用字符串形式的整数ID
                .vector(floatList)
                .payload(payload)
                .build();

        boolean result = qdrantService.insertVector(YIYI_COLLECTION, vectorData);

        //期望：2025-06-20 17:50:21.142 [main] DEBUG c.y.a.service.impl.QdrantServiceImpl - Insert vector response: {"result":{"operation_id":4,"status":"acknowledged"},"status":"ok","time":0.002798096}
        System.out.println(result);


    }

    @Test
    public void testGet() {

        //1.构造查询
        //
        List<String> texts = Arrays.asList("都有什么颜色啊");
        List<List<Double>> embeddingsQuery = bigModelService.embed(texts);

        Map<String, Object> filter = new HashMap<>();
        filter.put("teamId", "tianziyihao001");
        filter.put("externalProductId", 88889999);


        List<Float> floatList = new ArrayList<>();
        for (Double d : embeddingsQuery.get(0)) {
            floatList.add(d.floatValue());
        }

        VectorSearchRequest request = VectorSearchRequest.builder()
                .collectionName(YIYI_COLLECTION)
                .vector(floatList)
                .limit(1)
                .filter(filter)
                .scoreThreshold(0.6f)
                .withPayload(true)
                .build();

        VectorSearchResult result = qdrantService.searchVectors(request);
        System.out.println(result);

    }


}
