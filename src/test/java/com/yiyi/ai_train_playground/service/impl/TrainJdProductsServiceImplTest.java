package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.entity.TrainJdProducts;
import com.yiyi.ai_train_playground.service.jd.TrainJdProductsService;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TrainJdProductsService 测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@SpringBootTest
@Transactional
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class TrainJdProductsServiceImplTest {
    
    @Autowired
    private TrainJdProductsService trainJdProductsService;
    
    private TrainJdProducts createTestProduct(Long wareId, Long teamId, String title) {
        TrainJdProducts product = new TrainJdProducts();
        product.setWareId(wareId);
        product.setTeamId(teamId);
        product.setTitle(title);
        product.setBrandName("测试品牌");
        product.setJdPrice(new BigDecimal("99.99"));
        product.setMarketPrice(new BigDecimal("129.99"));
        product.setStockNum(100);
        product.setWareStatus(1);
        product.setJdProdDtl("测试商品详情");
        product.setCreator("test_user");
        product.setUpdater("test_user");
        return product;
    }
    
    @Test
    @Order(1)
    @DisplayName("测试插入商品")
    public void testInsert() {
        System.out.println("=== 测试插入商品 ===");
        
        TrainJdProducts product = createTestProduct(10001L, 1L, "测试商品1");
        
        boolean result = trainJdProductsService.insert(product);
        
        assertTrue(result, "插入应该成功");
        assertNotNull(product.getId(), "插入后应该生成ID");
        
        System.out.println("插入成功，商品ID: " + product.getId());
        System.out.println("✅ 插入测试通过");
    }
    
    @Test
    @Order(2)
    @DisplayName("测试查询商品")
    public void testFindByWareIdAndTeamId() {
        System.out.println("=== 测试查询商品 ===");
        
        // 先插入一个商品
        TrainJdProducts product = createTestProduct(10002L, 1L, "测试商品2");
        trainJdProductsService.insert(product);
        
        // 查询商品
        TrainJdProducts foundProduct = trainJdProductsService.findByWareIdAndTeamId(10002L, 1L);
        
        assertNotNull(foundProduct, "应该能查询到商品");
        assertEquals("测试商品2", foundProduct.getTitle());
        assertEquals("测试品牌", foundProduct.getBrandName());
        assertEquals("测试商品详情", foundProduct.getJdProdDtl());
        
        System.out.println("查询成功，商品标题: " + foundProduct.getTitle());
        System.out.println("✅ 查询测试通过");
    }
    
    @Test
    @Order(3)
    @DisplayName("测试更新商品")
    public void testUpdateByWareIdAndTeamId() {
        System.out.println("=== 测试更新商品 ===");
        
        // 先插入一个商品
        TrainJdProducts product = createTestProduct(10003L, 1L, "原始标题");
        trainJdProductsService.insert(product);
        
        // 更新商品
        product.setTitle("更新后标题");
        product.setBrandName("更新后品牌");
        product.setJdProdDtl("更新后详情");
        
        boolean result = trainJdProductsService.updateByWareIdAndTeamId(product);
        
        assertTrue(result, "更新应该成功");
        
        // 验证更新结果
        TrainJdProducts updatedProduct = trainJdProductsService.findByWareIdAndTeamId(10003L, 1L);
        assertEquals("更新后标题", updatedProduct.getTitle());
        assertEquals("更新后品牌", updatedProduct.getBrandName());
        assertEquals("更新后详情", updatedProduct.getJdProdDtl());
        
        System.out.println("更新成功，新标题: " + updatedProduct.getTitle());
        System.out.println("✅ 更新测试通过");
    }
    
    @Test
    @Order(4)
    @DisplayName("测试保存或更新商品")
    public void testSaveOrUpdate() {
        System.out.println("=== 测试保存或更新商品 ===");
        
        // 测试插入（商品不存在）
        TrainJdProducts newProduct = createTestProduct(10004L, 1L, "新商品");
        boolean insertResult = trainJdProductsService.saveOrUpdate(newProduct);
        assertTrue(insertResult, "新商品保存应该成功");
        
        // 测试更新（商品已存在）
        newProduct.setTitle("更新的新商品");
        boolean updateResult = trainJdProductsService.saveOrUpdate(newProduct);
        assertTrue(updateResult, "已存在商品更新应该成功");
        
        // 验证结果
        TrainJdProducts savedProduct = trainJdProductsService.findByWareIdAndTeamId(10004L, 1L);
        assertEquals("更新的新商品", savedProduct.getTitle());
        
        System.out.println("保存或更新成功，最终标题: " + savedProduct.getTitle());
        System.out.println("✅ 保存或更新测试通过");
    }
    
    @Test
    @Order(5)
    @DisplayName("测试批量插入商品")
    public void testBatchInsert() {
        System.out.println("=== 测试批量插入商品 ===");
        
        List<TrainJdProducts> productList = Arrays.asList(
            createTestProduct(10005L, 1L, "批量商品1"),
            createTestProduct(10006L, 1L, "批量商品2"),
            createTestProduct(10007L, 1L, "批量商品3")
        );
        
        int result = trainJdProductsService.batchInsert(productList);
        
        assertEquals(3, result, "应该插入3个商品");
        
        // 验证插入结果
        TrainJdProducts product1 = trainJdProductsService.findByWareIdAndTeamId(10005L, 1L);
        TrainJdProducts product2 = trainJdProductsService.findByWareIdAndTeamId(10006L, 1L);
        TrainJdProducts product3 = trainJdProductsService.findByWareIdAndTeamId(10007L, 1L);
        
        assertNotNull(product1);
        assertNotNull(product2);
        assertNotNull(product3);
        assertEquals("批量商品1", product1.getTitle());
        assertEquals("批量商品2", product2.getTitle());
        assertEquals("批量商品3", product3.getTitle());
        
        System.out.println("批量插入成功，插入数量: " + result);
        System.out.println("✅ 批量插入测试通过");
    }
    
    @Test
    @Order(6)
    @DisplayName("测试根据团队ID查询商品列表")
    public void testFindByTeamId() {
        System.out.println("=== 测试根据团队ID查询商品列表 ===");
        
        // 先插入几个商品
        trainJdProductsService.insert(createTestProduct(10008L, 2L, "团队2商品1"));
        trainJdProductsService.insert(createTestProduct(10009L, 2L, "团队2商品2"));
        
        List<TrainJdProducts> products = trainJdProductsService.findByTeamId(2L);
        
        assertTrue(products.size() >= 2, "应该至少有2个商品");
        
        // 验证都是团队2的商品
        for (TrainJdProducts product : products) {
            assertEquals(2L, product.getTeamId());
        }
        
        System.out.println("查询团队商品成功，数量: " + products.size());
        System.out.println("✅ 团队商品查询测试通过");
    }
    
    @Test
    @Order(7)
    @DisplayName("测试删除商品")
    public void testDeleteByWareIdAndTeamId() {
        System.out.println("=== 测试删除商品 ===");
        
        // 先插入一个商品
        TrainJdProducts product = createTestProduct(10010L, 1L, "待删除商品");
        trainJdProductsService.insert(product);
        
        // 确认商品存在
        assertTrue(trainJdProductsService.existsByWareIdAndTeamId(10010L, 1L));
        
        // 删除商品
        boolean result = trainJdProductsService.deleteByWareIdAndTeamId(10010L, 1L);
        
        assertTrue(result, "删除应该成功");
        
        // 确认商品已删除
        assertFalse(trainJdProductsService.existsByWareIdAndTeamId(10010L, 1L));
        
        System.out.println("删除成功");
        System.out.println("✅ 删除测试通过");
    }
    
    @Test
    @Order(8)
    @DisplayName("测试统计团队商品数量")
    public void testCountByTeamId() {
        System.out.println("=== 测试统计团队商品数量 ===");
        
        // 插入几个商品到团队3
        trainJdProductsService.insert(createTestProduct(10011L, 3L, "团队3商品1"));
        trainJdProductsService.insert(createTestProduct(10012L, 3L, "团队3商品2"));
        trainJdProductsService.insert(createTestProduct(10013L, 3L, "团队3商品3"));
        
        long count = trainJdProductsService.countByTeamId(3L);
        
        assertTrue(count >= 3, "应该至少有3个商品");
        
        System.out.println("团队商品数量: " + count);
        System.out.println("✅ 统计测试通过");
    }
    
    @Test
    @Order(9)
    @DisplayName("测试根据标题模糊查询")
    public void testFindByTeamIdAndTitleLike() {
        System.out.println("=== 测试根据标题模糊查询 ===");
        
        // 插入测试商品
        trainJdProductsService.insert(createTestProduct(10014L, 1L, "苹果手机iPhone15"));
        trainJdProductsService.insert(createTestProduct(10015L, 1L, "华为手机Mate60"));
        
        List<TrainJdProducts> products = trainJdProductsService.findByTeamIdAndTitleLike(1L, "手机");
        
        assertTrue(products.size() >= 2, "应该找到包含'手机'的商品");
        
        for (TrainJdProducts product : products) {
            assertTrue(product.getTitle().contains("手机"), "标题应该包含'手机'");
        }
        
        System.out.println("模糊查询成功，找到商品数量: " + products.size());
        System.out.println("✅ 模糊查询测试通过");
    }
    
    @Test
    @Order(10)
    @DisplayName("测试参数验证")
    public void testParameterValidation() {
        System.out.println("=== 测试参数验证 ===");
        
        // 测试null参数
        assertNull(trainJdProductsService.findByWareIdAndTeamId(null, 1L));
        assertNull(trainJdProductsService.findByWareIdAndTeamId(1L, null));
        assertFalse(trainJdProductsService.insert(null));
        assertFalse(trainJdProductsService.updateByWareIdAndTeamId(null));
        assertEquals(0, trainJdProductsService.batchInsert(null));
        
        // 测试无效商品对象
        TrainJdProducts invalidProduct = new TrainJdProducts();
        assertFalse(trainJdProductsService.insert(invalidProduct));
        assertFalse(trainJdProductsService.updateByWareIdAndTeamId(invalidProduct));
        
        System.out.println("✅ 参数验证测试通过");
    }
}
