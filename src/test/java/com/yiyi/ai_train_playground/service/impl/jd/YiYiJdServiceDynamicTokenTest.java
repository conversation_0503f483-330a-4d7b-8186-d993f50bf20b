package com.yiyi.ai_train_playground.service.impl.jd;

import com.yiyi.ai_train_playground.config.JdClientFactory;
import com.yiyi.ai_train_playground.entity.TrainJdProducts;
import com.yiyi.ai_train_playground.service.jd.impl.YiYiJdServiceImpl;
import com.yiyi.ai_train_playground.service.jd.YiYiJdService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * YiYiJdService 动态AccessToken测试
 * 验证动态accessToken功能是否正常工作
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@SpringBootTest
class YiYiJdServiceDynamicTokenTest {

    @Autowired
    private YiYiJdService yiYiJdService;

    @Autowired
    private JdClientFactory jdClientFactory;

    @Test
    void testJdClientFactoryInjection() {
        // 验证JdClientFactory是否正确注入
        assertNotNull(jdClientFactory, "JdClientFactory应该被正确注入");
        
        // 验证YiYiJdService是否正确注入并且是正确的实现类
        assertNotNull(yiYiJdService, "YiYiJdService应该被正确注入");
        assertTrue(yiYiJdService instanceof YiYiJdServiceImpl, "应该注入YiYiJdServiceImpl实例");
        
        System.out.println("✅ JdClientFactory和YiYiJdService注入验证成功");
    }

    @Test
    void testAccessTokenValidation() {
        // 测试accessToken验证功能
        assertTrue(jdClientFactory.isValidAccessToken("89fd9dcc03d34c6d997fc66e019700bcy2mw"), 
                "有效的accessToken应该通过验证");
        
        assertFalse(jdClientFactory.isValidAccessToken(null), 
                "null accessToken应该验证失败");
        
        assertFalse(jdClientFactory.isValidAccessToken(""), 
                "空字符串accessToken应该验证失败");
        
        assertFalse(jdClientFactory.isValidAccessToken("short"), 
                "过短的accessToken应该验证失败");
        
        System.out.println("✅ AccessToken验证功能测试通过");
    }

    @Test
    void testGetWare4ValidProductListWithDynamicToken() {
        // 使用动态accessToken测试
        String testAccessToken = "89fd9dcc03d34c6d997fc66e019700bcy2mw";
        
        List<TrainJdProducts> result = yiYiJdService.getWare4ValidProductList(testAccessToken, 1, 3);
        
        assertNotNull(result, "返回结果不能为空");
        
        if (!result.isEmpty()) {
            // 如果调用成功，验证返回结构
            System.out.println("✅ 动态accessToken调用京东API成功，返回 " + result.size() + " 个商品");
            TrainJdProducts firstProduct = result.get(0);
            assertNotNull(firstProduct.getWareId(), "商品ID不能为空");
            assertNotNull(firstProduct.getTitle(), "商品标题不能为空");
            System.out.println("  - 第一个商品: " + firstProduct.getTitle());
        } else {
            System.out.println("⚠️ 动态accessToken调用返回空列表（这可能是正常的，取决于token状态）");
        }
    }

    @Test
    void testGetWare4ValidProductListWithInvalidToken() {
        // 测试无效accessToken的处理
        String invalidToken = "invalid_token";
        
        List<TrainJdProducts> result = yiYiJdService.getWare4ValidProductList(invalidToken, 1, 5);
        
        assertNotNull(result, "返回结果不能为空");
        assertTrue(result.isEmpty(), "无效token应该返回空列表");
        
        System.out.println("✅ 无效accessToken处理测试通过");
        System.out.println("  - 返回空列表");
    }

    @Test
    void testGetWare4ValidProductListWithNullToken() {
        // 测试null accessToken的处理（应该使用默认token）
        List<TrainJdProducts> result = yiYiJdService.getWare4ValidProductList(null, 1, 2);
        
        assertNotNull(result, "返回结果不能为空");
        
        System.out.println("✅ null accessToken处理测试完成");
        if (!result.isEmpty()) {
            System.out.println("  - 使用默认token成功，返回 " + result.size() + " 个商品");
        } else {
            System.out.println("  - 返回空列表");
        }
    }

    @Test
    void testCompareDefaultAndDynamicToken() {
        // 比较默认token和动态token的调用结果
        String dynamicToken = "89fd9dcc03d34c6d997fc66e019700bcy2mw";
        
        List<TrainJdProducts> defaultResult = yiYiJdService.getWare4ValidProductList(1, 2);
        List<TrainJdProducts> dynamicResult = yiYiJdService.getWare4ValidProductList(dynamicToken, 1, 2);
        
        assertNotNull(defaultResult, "默认token结果不能为空");
        assertNotNull(dynamicResult, "动态token结果不能为空");
        
        System.out.println("✅ 默认token vs 动态token对比测试完成");
        System.out.println("  - 默认token成功: " + !defaultResult.isEmpty());
        System.out.println("  - 动态token成功: " + !dynamicResult.isEmpty());
        
        // 如果两者都成功，比较商品数量
        if (!defaultResult.isEmpty() && !dynamicResult.isEmpty()) {
            System.out.println("  - 默认token商品数量: " + defaultResult.size());
            System.out.println("  - 动态token商品数量: " + dynamicResult.size());
        }
    }
} 