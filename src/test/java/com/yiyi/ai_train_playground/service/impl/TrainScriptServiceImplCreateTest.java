package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.dto.ScriptCreateRequest;
import com.yiyi.ai_train_playground.service.TrainScriptService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 剧本创建服务测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-03
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TrainScriptServiceImplCreateTest {

    @Autowired
    private TrainScriptService trainScriptService;

    private static final Long TEST_TEAM_ID = 1L;
    private static final String TEST_CREATOR = "test_user";

    @Test
    public void testCreateScriptWithRelatedData_Success() {
        // 准备测试数据
        ScriptCreateRequest request = createTestScriptRequest();

        // 执行测试
        Long scriptId = trainScriptService.createScriptWithRelatedData(request, TEST_TEAM_ID, TEST_CREATOR);

        // 验证结果
        assertNotNull(scriptId);
        assertTrue(scriptId > 0);
        log.info("创建剧本成功，scriptId={}", scriptId);
    }

    @Test
    public void testCreateScriptWithRelatedData_NullRequest() {
        // 测试空请求
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.createScriptWithRelatedData(null, TEST_TEAM_ID, TEST_CREATOR);
        });
        assertEquals("请求参数不能为空", exception.getMessage());
    }

    @Test
    public void testCreateScriptWithRelatedData_NullTeamId() {
        // 测试空团队ID
        ScriptCreateRequest request = createTestScriptRequest();
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.createScriptWithRelatedData(request, null, TEST_CREATOR);
        });
        assertEquals("团队ID不能为空", exception.getMessage());
    }

    @Test
    public void testCreateScriptWithRelatedData_NullCreator() {
        // 测试空创建人
        ScriptCreateRequest request = createTestScriptRequest();
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.createScriptWithRelatedData(request, TEST_TEAM_ID, null);
        });
        assertEquals("创建人不能为空", exception.getMessage());
    }

    @Test
    public void testCreateScriptWithRelatedData_EmptyCreator() {
        // 测试空字符串创建人
        ScriptCreateRequest request = createTestScriptRequest();
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.createScriptWithRelatedData(request, TEST_TEAM_ID, "");
        });
        assertEquals("创建人不能为空", exception.getMessage());
    }

    @Test
    public void testCreateScriptWithRelatedData_EmptyName() {
        // 测试空剧本名称
        ScriptCreateRequest request = createTestScriptRequest();
        request.setName("");
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.createScriptWithRelatedData(request, TEST_TEAM_ID, TEST_CREATOR);
        });
        assertEquals("剧本名称不能为空", exception.getMessage());
    }

    @Test
    public void testCreateScriptWithRelatedData_OnlyScript() {
        // 测试只创建剧本，不包含关联数据
        ScriptCreateRequest request = new ScriptCreateRequest();
        request.setName("测试剧本-仅主体");
        request.setGenerationTypeCode(0);
        request.setGroupId(13L);
        request.setIntentId(5L);
        request.setEvaluationPlanId(2001L);
        request.setBuyerRequirement("测试需求");
        request.setOrderPriority(3);
        request.setSimulationTool("Simulator_V3");

        Long scriptId = trainScriptService.createScriptWithRelatedData(request, TEST_TEAM_ID, TEST_CREATOR);

        assertNotNull(scriptId);
        assertTrue(scriptId > 0);
        log.info("创建纯剧本成功，scriptId={}", scriptId);
    }

    @Test
    public void testCreateScriptWithRelatedData_WithProducts() {
        // 测试创建剧本和商品
        ScriptCreateRequest request = new ScriptCreateRequest();
        request.setName("测试剧本-包含商品");
        request.setGenerationTypeCode(0);
        request.setGroupId(13L);
        request.setIntentId(5L);
        request.setEvaluationPlanId(2001L);
        request.setBuyerRequirement("测试需求");
        request.setOrderPriority(3);
        request.setSimulationTool("Simulator_V3");

        // 添加商品列表
        List<ScriptCreateRequest.ProductCreateDTO> productList = Arrays.asList(
            createTestProduct("TEST001", "测试商品1"),
            createTestProduct("TEST002", "测试商品2")
        );
        request.setProductList(productList);

        Long scriptId = trainScriptService.createScriptWithRelatedData(request, TEST_TEAM_ID, TEST_CREATOR);

        assertNotNull(scriptId);
        assertTrue(scriptId > 0);
        log.info("创建剧本和商品成功，scriptId={}", scriptId);
    }

    @Test
    public void testBatchDeleteScripts_Success() {
        // 准备测试数据 - 先创建一些剧本
        ScriptCreateRequest request1 = new ScriptCreateRequest();
        request1.setName("测试剧本-批量删除1");
        request1.setGenerationTypeCode(0);
        request1.setGroupId(13L);
        request1.setIntentId(5L);
        request1.setEvaluationPlanId(2001L);
        request1.setBuyerRequirement("测试需求1");
        request1.setOrderPriority(3);
        request1.setSimulationTool("Simulator_V3");

        ScriptCreateRequest request2 = new ScriptCreateRequest();
        request2.setName("测试剧本-批量删除2");
        request2.setGenerationTypeCode(0);
        request2.setGroupId(13L);
        request2.setIntentId(5L);
        request2.setEvaluationPlanId(2001L);
        request2.setBuyerRequirement("测试需求2");
        request2.setOrderPriority(3);
        request2.setSimulationTool("Simulator_V3");

        Long scriptId1 = trainScriptService.createScriptWithRelatedData(request1, TEST_TEAM_ID, TEST_CREATOR);
        Long scriptId2 = trainScriptService.createScriptWithRelatedData(request2, TEST_TEAM_ID, TEST_CREATOR);

        // 执行批量删除测试
        String ids = scriptId1 + "," + scriptId2;
        boolean result = trainScriptService.batchDeleteScripts(ids, TEST_TEAM_ID);

        // 验证结果
        assertTrue(result);
        log.info("批量删除剧本测试通过：ids={}, teamId={}", ids, TEST_TEAM_ID);
    }

    @Test
    public void testBatchDeleteScripts_EmptyIds() {
        // 准备测试数据
        String ids = "";
        Long teamId = TEST_TEAM_ID;

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.batchDeleteScripts(ids, teamId);
        });

        // 验证异常信息
        assertEquals("剧本ID列表不能为空", exception.getMessage());
        log.info("空ID列表异常测试通过");
    }

    @Test
    public void testBatchDeleteScripts_InvalidIds() {
        // 准备测试数据
        String ids = "1,abc,3";
        Long teamId = TEST_TEAM_ID;

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.batchDeleteScripts(ids, teamId);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("剧本ID格式不正确"));
        log.info("无效ID格式异常测试通过");
    }

    @Test
    public void testBatchDeleteScripts_NullTeamId() {
        // 准备测试数据
        String ids = "1,2,3";
        Long teamId = null;

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainScriptService.batchDeleteScripts(ids, teamId);
        });

        // 验证异常信息
        assertEquals("团队ID不能为空", exception.getMessage());
        log.info("空团队ID异常测试通过");
    }

    /**
     * 创建测试用的剧本请求
     */
    private ScriptCreateRequest createTestScriptRequest() {
        ScriptCreateRequest request = new ScriptCreateRequest();
        request.setName("数码相机选购指南：全画幅VS微单");
        request.setGenerationTypeCode(0);
        request.setGroupId(13L);
        request.setIntentId(5L);
        request.setEvaluationPlanId(2001L);
        request.setBuyerRequirement("摄影爱好者，预算1万元，主要拍摄人像和风景");
        request.setOrderIsRemarked(null);
        request.setOrderPriority(3);
        request.setOrderRemark("优先处理：客户对专业术语有疑问");
        request.setSimulationTool("Simulator_V3");

        // 商品列表
        List<ScriptCreateRequest.ProductCreateDTO> productList = Arrays.asList(
            createTestProduct("838591834200", "无线蓝牙耳机1"),
            createTestProduct("838591834201", "无线蓝牙耳机2"),
            createTestProduct("838591834202", "无线蓝牙耳机3")
        );
        request.setProductList(productList);

        // 关联图片
        List<ScriptCreateRequest.RelatedImageCreateDTO> relateImgs = Arrays.asList(
            createTestImage(1, 1, "剧本封面图", "https://cdn.example.com/scripts/1/cover.jpg"),
            createTestImage(2, 1, "宣传视频", "https://cdn.example.com/videos/1/promo.mp4"),
            createTestImage(1, 2, "道具设计图", "https://design-storage.com/props/1/sketch.jpg")
        );
        request.setRelateImgs(relateImgs);

        // 流程节点
        List<ScriptCreateRequest.FlowNodeCreateDTO> flowNodes = Arrays.asList(
            createTestFlowNode("开场导入", "主持人需在5分钟内完成背景介绍，营造悬疑氛围"),
            createTestFlowNode("第一轮搜证", "每位玩家限时15分钟搜查3个场景"),
            createTestFlowNode("最终推理", "要求主持人控制讨论节奏")
        );
        request.setFlowNodes(flowNodes);

        return request;
    }

    /**
     * 创建测试商品
     */
    private ScriptCreateRequest.ProductCreateDTO createTestProduct(String externalId, String name) {
        ScriptCreateRequest.ProductCreateDTO product = new ScriptCreateRequest.ProductCreateDTO();
        product.setExternalProductId(externalId);
        product.setExternalProductName(name);
        product.setExternalProductLink("https://shop.com/" + externalId);
        product.setExternalProductImage("https://img.com/" + externalId + ".jpg");
        return product;
    }

    /**
     * 创建测试图片
     */
    private ScriptCreateRequest.RelatedImageCreateDTO createTestImage(Integer mediaType, Integer uploadType, String text, String url) {
        ScriptCreateRequest.RelatedImageCreateDTO image = new ScriptCreateRequest.RelatedImageCreateDTO();
        image.setMediaType(mediaType);
        image.setUploadType(uploadType);
        image.setRecognizedText(text);
        image.setUrl(url);
        return image;
    }

    /**
     * 创建测试流程节点
     */
    private ScriptCreateRequest.FlowNodeCreateDTO createTestFlowNode(String nodeName, String requirement) {
        ScriptCreateRequest.FlowNodeCreateDTO flowNode = new ScriptCreateRequest.FlowNodeCreateDTO();
        flowNode.setNodeName(nodeName);
        flowNode.setNodeBuyerRequirement(requirement);
        return flowNode;
    }
}
