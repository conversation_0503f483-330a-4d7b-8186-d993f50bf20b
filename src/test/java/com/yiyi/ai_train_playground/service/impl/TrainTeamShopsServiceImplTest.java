package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.entity.TrainTeamShops;
import com.yiyi.ai_train_playground.service.TrainTeamShopsService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * TrainTeamShopsService 测试类
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TrainTeamShopsServiceImplTest {

    @Autowired
    private TrainTeamShopsService trainTeamShopsService;

    private static final Long TEST_TEAM_ID = 1L;
    private static final Long TEST_SHOP_ID = 12345L;
    private static final Integer TEST_SHOP_TYPE = 0; // 京东

    @Test
    @DisplayName("测试根据店铺ID更新团队店铺信息（完整实体更新）")
    void testUpdateByShopIdWithEntity() {
        log.info("=== 测试根据店铺ID更新团队店铺信息（完整实体更新） ===");

        // 1. 创建并插入测试数据
        TrainTeamShops testShop = createTestTeamShops();
        testShop.setTeamId(TEST_TEAM_ID);
        testShop.setShopId(TEST_SHOP_ID);
        testShop.setShopType(TEST_SHOP_TYPE);
        
        boolean insertResult = trainTeamShopsService.insert(testShop);
        assertThat(insertResult).isTrue();
        log.info("插入测试数据成功");

        // 2. 查询插入的数据
        TrainTeamShops foundShop = trainTeamShopsService.findByShopId(TEST_SHOP_ID);
        assertThat(foundShop).isNotNull();
        assertThat(foundShop.getIsAuthorize()).isTrue();
        assertThat(foundShop.getIsSyncComplete()).isEqualTo(0);

        // 3. 更新数据
        foundShop.setIsAuthorize(false);
        foundShop.setIsSyncComplete(2);
        foundShop.setUpdater("updated-by-entity");
        
        boolean updateResult = trainTeamShopsService.updateEntityByShopId(foundShop);
        assertThat(updateResult).isTrue();
        log.info("更新操作成功");

        // 4. 验证更新结果
        TrainTeamShops updatedShop = trainTeamShopsService.findByShopId(TEST_SHOP_ID);
        assertThat(updatedShop).isNotNull();
        assertThat(updatedShop.getIsAuthorize()).isFalse();
        assertThat(updatedShop.getIsSyncComplete()).isEqualTo(2);
        assertThat(updatedShop.getUpdater()).isEqualTo("updated-by-entity");
        
        log.info("根据店铺ID更新团队店铺信息成功: shopId={}, isAuthorize={}, isSyncComplete={}", 
                TEST_SHOP_ID, updatedShop.getIsAuthorize(), updatedShop.getIsSyncComplete());
    }

    @Test
    @DisplayName("测试根据店铺ID更新授权状态和同步状态")
    void testUpdateByShopIdWithParameters() {
        log.info("=== 测试根据店铺ID更新授权状态和同步状态 ===");

        // 1. 创建并插入测试数据
        TrainTeamShops testShop = createTestTeamShops();
        testShop.setTeamId(TEST_TEAM_ID);
        testShop.setShopId(TEST_SHOP_ID);
        testShop.setShopType(TEST_SHOP_TYPE);
        
        boolean insertResult = trainTeamShopsService.insert(testShop);
        assertThat(insertResult).isTrue();
        log.info("插入测试数据成功");

        // 2. 使用参数方式更新
        boolean updateResult = trainTeamShopsService.updateByShopId(
                TEST_SHOP_ID, false, 1, "updated-by-params");
        assertThat(updateResult).isTrue();
        log.info("参数方式更新成功");

        // 3. 验证更新结果
        TrainTeamShops updatedShop = trainTeamShopsService.findByShopId(TEST_SHOP_ID);
        assertThat(updatedShop).isNotNull();
        assertThat(updatedShop.getIsAuthorize()).isFalse();
        assertThat(updatedShop.getIsSyncComplete()).isEqualTo(1);
        assertThat(updatedShop.getUpdater()).isEqualTo("updated-by-params");
        
        log.info("参数方式更新验证成功");
    }

    @Test
    @DisplayName("测试saveOrUpdate方法")
    void testSaveOrUpdate() {
        log.info("=== 测试saveOrUpdate方法 ===");

        // 1. 测试插入新记录
        TrainTeamShops newShop = createTestTeamShops();
        newShop.setTeamId(TEST_TEAM_ID);
        newShop.setShopId(TEST_SHOP_ID);
        newShop.setShopType(TEST_SHOP_TYPE);
        
        boolean saveResult = trainTeamShopsService.saveOrUpdate(newShop);
        assertThat(saveResult).isTrue();
        log.info("saveOrUpdate插入新记录成功");

        // 2. 验证记录已存在
        TrainTeamShops foundShop = trainTeamShopsService.findByShopId(TEST_SHOP_ID);
        assertThat(foundShop).isNotNull();
        assertThat(foundShop.getIsAuthorize()).isTrue();

        // 3. 测试更新现有记录
        newShop.setIsAuthorize(false);
        newShop.setIsSyncComplete(2);
        newShop.setUpdater("save-or-update-test");
        
        boolean updateResult = trainTeamShopsService.saveOrUpdate(newShop);
        assertThat(updateResult).isTrue();
        log.info("saveOrUpdate更新现有记录成功");

        // 4. 验证更新结果
        TrainTeamShops updatedShop = trainTeamShopsService.findByShopId(TEST_SHOP_ID);
        assertThat(updatedShop).isNotNull();
        assertThat(updatedShop.getIsAuthorize()).isFalse();
        assertThat(updatedShop.getIsSyncComplete()).isEqualTo(2);
        log.info("saveOrUpdate更新结果验证成功");
    }

    @Test
    @DisplayName("测试existsByShopId方法")
    void testExistsByShopId() {
        log.info("=== 测试existsByShopId方法 ===");

        // 1. 测试不存在的店铺
        boolean exists1 = trainTeamShopsService.existsByShopId(TEST_SHOP_ID);
        assertThat(exists1).isFalse();
        log.info("不存在的店铺检查正确");

        // 2. 插入测试数据
        TrainTeamShops testShop = createTestTeamShops();
        testShop.setTeamId(TEST_TEAM_ID);
        testShop.setShopId(TEST_SHOP_ID);
        testShop.setShopType(TEST_SHOP_TYPE);
        
        boolean insertResult = trainTeamShopsService.insert(testShop);
        assertThat(insertResult).isTrue();

        // 3. 测试存在的店铺
        boolean exists2 = trainTeamShopsService.existsByShopId(TEST_SHOP_ID);
        assertThat(exists2).isTrue();
        log.info("存在的店铺检查正确");
    }

    @Test
    @DisplayName("测试各种查询方法")
    void testQueryMethods() {
        log.info("=== 测试各种查询方法 ===");

        // 1. 插入测试数据
        TrainTeamShops testShop = createTestTeamShops();
        testShop.setTeamId(TEST_TEAM_ID);
        testShop.setShopId(TEST_SHOP_ID);
        testShop.setShopType(TEST_SHOP_TYPE);
        
        boolean insertResult = trainTeamShopsService.insert(testShop);
        assertThat(insertResult).isTrue();
        log.info("插入测试数据成功");

        // 2. 根据店铺ID查询
        TrainTeamShops foundByShopId = trainTeamShopsService.findByShopId(TEST_SHOP_ID);
        assertThat(foundByShopId).isNotNull();
        assertThat(foundByShopId.getTeamId()).isEqualTo(TEST_TEAM_ID);
        assertThat(foundByShopId.getShopType()).isEqualTo(TEST_SHOP_TYPE);

        // 3. 根据团队ID和店铺类型查询
        TrainTeamShops foundByTeamAndType = trainTeamShopsService.findByTeamIdAndShopType(TEST_TEAM_ID, TEST_SHOP_TYPE);
        assertThat(foundByTeamAndType).isNotNull();
        assertThat(foundByTeamAndType.getShopId()).isEqualTo(TEST_SHOP_ID);

        // 4. 验证查询结果一致性
        assertThat(foundByShopId.getId()).isEqualTo(foundByTeamAndType.getId());
        
        log.info("各种查询方法测试成功");
    }

    /**
     * 创建测试用的TrainTeamShops对象
     */
    private TrainTeamShops createTestTeamShops() {
        TrainTeamShops shop = new TrainTeamShops();
        shop.setIsAuthorize(true);
        shop.setIsSyncComplete(0);
        shop.setCreator("test-creator");
        shop.setUpdater("test-updater");
        shop.setCreateTime(LocalDateTime.now());
        shop.setUpdateTime(LocalDateTime.now());
        shop.setVersion(0L);
        return shop;
    }
}
