package com.yiyi.ai_train_playground.service.impl;

import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.seg.common.Term;
import org.junit.jupiter.api.Test;

import java.net.URL;
import java.util.List;

public class HanLpTest {

    @Test
    public void testHanLpBasic() {
        try {
            // 设置数据路径
            URL dataUrl = this.getClass().getClassLoader().getResource("data");
            if (dataUrl != null) {
                String dataPath = dataUrl.getPath();
                if (dataPath.startsWith("/") && System.getProperty("os.name").toLowerCase().contains("windows")) {
                    dataPath = dataPath.substring(1);
                }
                
                // 设置HanLP根目录和具体路径
                System.setProperty("hanlp.root", dataPath + "/");
                
                // 直接设置词典路径
                String dictPath = dataPath + "/dictionary/";
                HanLP.Config.CoreDictionaryPath = dictPath + "CoreNatureDictionary.txt";
                HanLP.Config.BiGramDictionaryPath = dictPath + "CoreNatureDictionary.ngram.txt";
                HanLP.Config.CoreStopWordDictionaryPath = dictPath + "stopwords.txt";
                HanLP.Config.CustomDictionaryPath = new String[]{dictPath + "custom/CustomDictionary.txt"};
                
                // 设置基本配置
                HanLP.Config.ShowTermNature = true;
                HanLP.Config.Normalization = true;
                
                System.out.println("HanLP数据路径: " + dataPath);
                System.out.println("核心词典路径: " + HanLP.Config.CoreDictionaryPath);
            }
            
            // 测试基本分词
            String text = "这款智能手机重量2500g，屏幕尺寸6.7英寸";
            List<Term> terms = HanLP.segment(text);
            
            System.out.println("原文: " + text);
            System.out.println("分词结果:");
            for (Term term : terms) {
                System.out.println(term.word + " / " + term.nature);
            }
            
        } catch (Exception e) {
            System.err.println("HanLP测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 