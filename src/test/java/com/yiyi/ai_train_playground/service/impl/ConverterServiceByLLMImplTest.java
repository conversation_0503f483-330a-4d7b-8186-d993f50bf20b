package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.service.ConverterServiceByLLM;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * ConverterServiceByLLM 测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class ConverterServiceByLLMImplTest {
    
    @Autowired
    private ConverterServiceByLLM converterService;
    
    @Test
    @Order(1)
    @DisplayName("测试LLM转换服务 - 正常情况")
    public void testConvert_Success() {
        System.out.println("=== 测试1: LLM转换服务 - 正常情况 ===");
        
        // 准备测试数据
        String prmtTemplateKW = "html_md_converter:SU";  // 假设这个关键词存在
        String systemPrompt = "你是一个专业的HTML到Markdown转换助手";
        String userPrompt = "<div><h1>测试标题</h1><p>这是一个测试段落</p></div>";
        
        try {
            String result = converterService.convertText(prmtTemplateKW, systemPrompt, userPrompt);
            
            Assertions.assertNotNull(result, "转换结果不应为null");
            Assertions.assertTrue(result.length() > 0, "转换结果不应为空");
            
            System.out.println("转换成功!");
            System.out.println("输入系统提示词: " + systemPrompt);
            System.out.println("输入用户提示词: " + userPrompt);
            System.out.println("转换结果长度: " + result.length());
            System.out.println("转换结果预览: " + result.substring(0, Math.min(200, result.length())) + "...");
            System.out.println("✅ LLM转换服务测试通过");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("未找到提示词模板")) {
                System.out.println("⚠️ 跳过测试：需要配置有效的API密钥或提示词模板");
                Assumptions.assumeTrue(false, "需要配置有效的API密钥或提示词模板");
            } else {
                throw e;
            }
        }
    }
    
    @Test
    @Order(2)
    @DisplayName("测试LLM转换服务 - 空关键词")
    public void testConvert_EmptyKeyword() {
        System.out.println("=== 测试2: LLM转换服务 - 空关键词 ===");
        
        String systemPrompt = "测试系统提示词";
        String userPrompt = "测试用户提示词";
        
        // 测试null关键词
        IllegalArgumentException exception1 = Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> converterService.convertText(null, systemPrompt, userPrompt),
                "null关键词应该抛出IllegalArgumentException"
        );
        Assertions.assertTrue(exception1.getMessage().contains("提示词模板关键词不能为空"));
        
        // 测试空字符串关键词
        IllegalArgumentException exception2 = Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> converterService.convertText("", systemPrompt, userPrompt),
                "空字符串关键词应该抛出IllegalArgumentException"
        );
        Assertions.assertTrue(exception2.getMessage().contains("提示词模板关键词不能为空"));
        
        // 测试空白字符串关键词
        IllegalArgumentException exception3 = Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> converterService.convertText("   ", systemPrompt, userPrompt),
                "空白字符串关键词应该抛出IllegalArgumentException"
        );
        Assertions.assertTrue(exception3.getMessage().contains("提示词模板关键词不能为空"));
        
        System.out.println("✅ 空关键词测试通过");
    }
    
    @Test
    @Order(3)
    @DisplayName("测试LLM转换服务 - null参数")
    public void testConvert_NullParameters() {
        System.out.println("=== 测试3: LLM转换服务 - null参数 ===");
        
        String prmtTemplateKW = "test:SU";
        
        // 测试null系统提示词
        IllegalArgumentException exception1 = Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> converterService.convertText(prmtTemplateKW, null, "用户提示词"),
                "null系统提示词应该抛出IllegalArgumentException"
        );
        Assertions.assertTrue(exception1.getMessage().contains("系统提示词不能为null"));
        
        // 测试null用户提示词
        IllegalArgumentException exception2 = Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> converterService.convertText(prmtTemplateKW, "系统提示词", null),
                "null用户提示词应该抛出IllegalArgumentException"
        );
        Assertions.assertTrue(exception2.getMessage().contains("用户提示词不能为null"));
        
        System.out.println("✅ null参数测试通过");
    }
    
    @Test
    @Order(4)
    @DisplayName("测试LLM转换服务 - 不存在的关键词")
    public void testConvert_NonExistentKeyword() {
        System.out.println("=== 测试4: LLM转换服务 - 不存在的关键词 ===");
        
        String nonExistentKeyword = "non_existent_keyword_12345:SU";
        String systemPrompt = "测试系统提示词";
        String userPrompt = "测试用户提示词";
        
        RuntimeException exception = Assertions.assertThrows(
                RuntimeException.class,
                () -> converterService.convertText(nonExistentKeyword, systemPrompt, userPrompt),
                "不存在的关键词应该抛出RuntimeException"
        );
        
        Assertions.assertTrue(exception.getMessage().contains("未找到提示词模板"));
        System.out.println("异常信息: " + exception.getMessage());
        System.out.println("✅ 不存在关键词测试通过");
    }
    
    @Test
    @Order(5)
    @DisplayName("测试LLM转换服务 - 变量替换功能")
    public void testConvert_VariableReplacement() {
        System.out.println("=== 测试5: LLM转换服务 - 变量替换功能 ===");
        
        // 这个测试主要验证变量替换逻辑，即使API调用失败也能验证替换功能
        String prmtTemplateKW = "html_md_converter:SU";
        String systemPrompt = "特殊字符测试: @#$%^&*()";
        String userPrompt = "包含变量的文本: {test} [value] <tag>";
        
        try {
            String result = converterService.convertText(prmtTemplateKW, systemPrompt, userPrompt);
            
            // 如果成功执行，说明变量替换正常工作
            Assertions.assertNotNull(result, "结果不应为null");
            System.out.println("变量替换和LLM调用都成功");
            System.out.println("✅ 变量替换功能测试通过");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("未找到提示词模板")) {
                System.out.println("⚠️ 跳过测试：需要配置有效的API密钥或提示词模板");
                Assumptions.assumeTrue(false, "需要配置有效的API密钥或提示词模板");
            } else {
                // 其他异常说明变量替换可能有问题
                System.out.println("变量替换测试遇到异常: " + e.getMessage());
                throw e;
            }
        }
    }
    
    @Test
    @Order(6)
    @DisplayName("测试LLM转换服务 - 长文本处理")
    public void testConvert_LongText() {
        System.out.println("=== 测试6: LLM转换服务 - 长文本处理 ===");
        
        String prmtTemplateKW = "html_md_converter:SU";
        
        // 构造长文本
        StringBuilder longSystemPrompt = new StringBuilder("长系统提示词: ");
        StringBuilder longUserPrompt = new StringBuilder("长用户提示词: ");
        
        for (int i = 0; i < 100; i++) {
            longSystemPrompt.append("这是第").append(i).append("段系统提示词内容。");
            longUserPrompt.append("这是第").append(i).append("段用户提示词内容。");
        }
        
        try {
            String result = converterService.convertText(prmtTemplateKW,
                    longSystemPrompt.toString(), longUserPrompt.toString());
            
            Assertions.assertNotNull(result, "长文本处理结果不应为null");
            System.out.println("长文本处理成功");
            System.out.println("系统提示词长度: " + longSystemPrompt.length());
            System.out.println("用户提示词长度: " + longUserPrompt.length());
            System.out.println("处理结果长度: " + result.length());
            System.out.println("✅ 长文本处理测试通过");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("未找到提示词模板")) {
                System.out.println("⚠️ 跳过测试：需要配置有效的API密钥或提示词模板");
                Assumptions.assumeTrue(false, "需要配置有效的API密钥或提示词模板");
            } else {
                throw e;
            }
        }
    }

    @Test
    @DisplayName("测试convertImg方法 - 基本功能")
    void testConvertImg() {
        // 准备测试数据
        String prmtTemplateKW = "image_converter:SU";
        String systemPrompt = "你是一个专业的图像分析助手";
        String userPrompt = "请分析这张图片的内容";
        String imageUrl = "https://example.com/test-image.jpg";

        try {
            String result = converterService.convertImg(prmtTemplateKW, systemPrompt, userPrompt, imageUrl);

            Assertions.assertNotNull(result, "图像转换结果不应为null");
            Assertions.assertTrue(result.length() > 0, "图像转换结果不应为空");

            System.out.println("图像转换成功!");
            System.out.println("输入系统提示词: " + systemPrompt);
            System.out.println("输入用户提示词: " + userPrompt);
            System.out.println("图像URL: " + imageUrl);
            System.out.println("转换结果长度: " + result.length());
            System.out.println("转换结果预览: " + result.substring(0, Math.min(200, result.length())) + "...");
            System.out.println("✅ LLM图像转换服务测试通过");

        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("未找到提示词模板")) {
                System.out.println("⚠️ 跳过测试：需要配置有效的API密钥或提示词模板");
                Assumptions.assumeTrue(false, "需要配置有效的API密钥或提示词模板");
            } else {
                throw e;
            }
        }
    }

    @Test
    @DisplayName("测试convertImg方法 - 参数验证")
    void testConvertImgParameterValidation() {
        String validPrmtTemplateKW = "image_converter:SU";
        String validSystemPrompt = "系统提示词";
        String validUserPrompt = "用户提示词";
        String validImageUrl = "https://example.com/test.jpg";

        // 测试空的prmtTemplateKW
        Assertions.assertThrows(IllegalArgumentException.class, () -> {
            converterService.convertImg(null, validSystemPrompt, validUserPrompt, validImageUrl);
        }, "prmtTemplateKW为null时应抛出异常");

        Assertions.assertThrows(IllegalArgumentException.class, () -> {
            converterService.convertImg("", validSystemPrompt, validUserPrompt, validImageUrl);
        }, "prmtTemplateKW为空字符串时应抛出异常");

        // 测试null的systemPrompt
        Assertions.assertThrows(IllegalArgumentException.class, () -> {
            converterService.convertImg(validPrmtTemplateKW, null, validUserPrompt, validImageUrl);
        }, "systemPrompt为null时应抛出异常");

        // 测试null的userPrompt
        Assertions.assertThrows(IllegalArgumentException.class, () -> {
            converterService.convertImg(validPrmtTemplateKW, validSystemPrompt, null, validImageUrl);
        }, "userPrompt为null时应抛出异常");

        // 测试空的imageUrl
        Assertions.assertThrows(IllegalArgumentException.class, () -> {
            converterService.convertImg(validPrmtTemplateKW, validSystemPrompt, validUserPrompt, null);
        }, "imageUrl为null时应抛出异常");

        Assertions.assertThrows(IllegalArgumentException.class, () -> {
            converterService.convertImg(validPrmtTemplateKW, validSystemPrompt, validUserPrompt, "");
        }, "imageUrl为空字符串时应抛出异常");

        System.out.println("✅ convertImg参数验证测试通过");
    }
}
