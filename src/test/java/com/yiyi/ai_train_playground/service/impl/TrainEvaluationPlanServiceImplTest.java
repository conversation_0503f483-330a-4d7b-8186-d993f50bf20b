package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.dto.EvaluationPlanShortDTO;
import com.yiyi.ai_train_playground.service.TrainEvaluationPlanService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 评价方案服务实现类测试
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-03
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TrainEvaluationPlanServiceImplTest {

    @Autowired
    private TrainEvaluationPlanService trainEvaluationPlanService;

    private static final Long TEST_TEAM_ID = 1L;

    @Test
    public void testGetShortList_Success() {
        // 查询评价方案简短列表
        List<EvaluationPlanShortDTO> result = trainEvaluationPlanService.getShortList(TEST_TEAM_ID);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.size() > 0);
        
        // 验证第一个结果的字段
        EvaluationPlanShortDTO first = result.get(0);
        assertNotNull(first.getId());
        assertNotNull(first.getTeamId());
        assertNotNull(first.getGroupId());
        assertNotNull(first.getEvaName());
        assertNotNull(first.getEvaGroupName());
        
        assertEquals(TEST_TEAM_ID, first.getTeamId());
        
        log.info("查询评价方案简短列表成功：size={}", result.size());
        for (EvaluationPlanShortDTO dto : result) {
            log.info("评价方案：id={}, teamId={}, groupId={}, evaName={}, evaGroupName={}", 
                    dto.getId(), dto.getTeamId(), dto.getGroupId(), dto.getEvaName(), dto.getEvaGroupName());
        }
    }

    @Test
    public void testGetShortList_NullTeamId() {
        // 测试空团队ID
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            trainEvaluationPlanService.getShortList(null);
        });
        
        assertEquals("团队ID不能为空", exception.getMessage());
        log.info("空团队ID异常测试通过");
    }

    @Test
    public void testGetShortList_EmptyResult() {
        // 测试不存在的团队ID
        List<EvaluationPlanShortDTO> result = trainEvaluationPlanService.getShortList(999L);
        
        assertNotNull(result);
        assertEquals(0, result.size());
        log.info("空结果测试通过");
    }
}
