package com.yiyi.ai_train_playground.service.impl;

import com.yiyi.ai_train_playground.entity.TrainTeam;
import com.yiyi.ai_train_playground.entity.TrainTeamShops;
import com.yiyi.ai_train_playground.service.TrainTeamService;
import com.yiyi.ai_train_playground.service.TrainTeamShopsService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * TrainTeamService 测试类
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TrainTeamServiceImplTest {

    @Autowired
    private TrainTeamService trainTeamService;

    @Autowired
    private TrainTeamShopsService trainTeamShopsService;

    private static final Long TEST_TEAM_ID = 1L;
    private static final Long TEST_SHOP_ID = 12345L;

    @Test
    @DisplayName("测试根据店铺ID查询团队信息")
    void testFindByShopId() {
        log.info("=== 测试根据店铺ID查询团队信息 ===");

        // 1. 创建测试的团队店铺关系
        TrainTeamShops testShop = createTestTeamShops();
        testShop.setTeamId(TEST_TEAM_ID);
        testShop.setShopId(TEST_SHOP_ID);
        
        // 2. 插入测试数据
        boolean insertResult = trainTeamShopsService.insert(testShop);
        assertThat(insertResult).isTrue();
        log.info("插入团队店铺关系成功");

        // 3. 根据店铺ID查询团队信息
        TrainTeam foundTeam = trainTeamService.findByShopId(TEST_SHOP_ID);
        
        // 4. 验证结果
        assertThat(foundTeam).isNotNull();
        assertThat(foundTeam.getId()).isEqualTo(TEST_TEAM_ID);
        
        log.info("根据店铺ID查询团队信息成功: shopId={}, teamId={}, teamName={}", 
                TEST_SHOP_ID, foundTeam.getId(), foundTeam.getName());
    }

    @Test
    @DisplayName("测试根据不存在的店铺ID查询团队信息")
    void testFindByShopIdNotFound() {
        log.info("=== 测试根据不存在的店铺ID查询团队信息 ===");

        Long nonExistentShopId = 99999L;
        TrainTeam foundTeam = trainTeamService.findByShopId(nonExistentShopId);
        
        assertThat(foundTeam).isNull();
        log.info("根据不存在的店铺ID查询，正确返回null");
    }

    @Test
    @DisplayName("测试店铺ID为null的情况")
    void testFindByShopIdWithNull() {
        log.info("=== 测试店铺ID为null的情况 ===");

        TrainTeam foundTeam = trainTeamService.findByShopId(null);
        
        assertThat(foundTeam).isNull();
        log.info("店铺ID为null时，正确返回null");
    }

    @Test
    @DisplayName("测试完整的查询流程")
    void testCompleteQueryFlow() {
        log.info("=== 测试完整的查询流程 ===");

        // 1. 创建多个团队店铺关系
        TrainTeamShops shop1 = createTestTeamShops();
        shop1.setTeamId(TEST_TEAM_ID);
        shop1.setShopId(TEST_SHOP_ID);
        shop1.setShopType(0); // 京东

        TrainTeamShops shop2 = createTestTeamShops();
        shop2.setTeamId(TEST_TEAM_ID);
        shop2.setShopId(TEST_SHOP_ID + 1);
        shop2.setShopType(1); // 淘宝

        // 2. 插入测试数据
        boolean insert1 = trainTeamShopsService.insert(shop1);
        boolean insert2 = trainTeamShopsService.insert(shop2);
        assertThat(insert1).isTrue();
        assertThat(insert2).isTrue();
        log.info("插入多个团队店铺关系成功");

        // 3. 分别查询团队信息
        TrainTeam team1 = trainTeamService.findByShopId(TEST_SHOP_ID);
        TrainTeam team2 = trainTeamService.findByShopId(TEST_SHOP_ID + 1);

        // 4. 验证结果
        assertThat(team1).isNotNull();
        assertThat(team2).isNotNull();
        assertThat(team1.getId()).isEqualTo(TEST_TEAM_ID);
        assertThat(team2.getId()).isEqualTo(TEST_TEAM_ID);
        assertThat(team1.getId()).isEqualTo(team2.getId()); // 同一个团队

        log.info("完整查询流程测试成功: 两个不同店铺都属于同一个团队");
    }

    @Test
    @DisplayName("测试根据团队ID查询团队信息（原有功能）")
    void testGetTeamById() {
        log.info("=== 测试根据团队ID查询团队信息（原有功能） ===");

        // 直接根据团队ID查询
        TrainTeam team = trainTeamService.getTeamById(TEST_TEAM_ID);
        
        // 验证结果
        assertThat(team).isNotNull();
        assertThat(team.getId()).isEqualTo(TEST_TEAM_ID);
        
        log.info("根据团队ID查询成功: teamId={}, teamName={}", team.getId(), team.getName());
    }

    /**
     * 创建测试用的TrainTeamShops对象
     */
    private TrainTeamShops createTestTeamShops() {
        TrainTeamShops shop = new TrainTeamShops();
        shop.setShopType(0); // 京东
        shop.setIsAuthorize(true);
        shop.setIsSyncComplete(0);
        shop.setCreator("test-creator");
        shop.setUpdater("test-updater");
        shop.setCreateTime(LocalDateTime.now());
        shop.setUpdateTime(LocalDateTime.now());
        shop.setVersion(0L);
        return shop;
    }
}
