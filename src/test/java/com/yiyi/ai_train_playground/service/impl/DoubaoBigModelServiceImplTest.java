package com.yiyi.ai_train_playground.service.impl;

import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.yiyi.ai_train_playground.config.DoubaoConfig;
import com.yiyi.ai_train_playground.service.SuperBigModelInterface;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * DoubaoBigModelServiceImpl 全面测试类
 * 
 * 测试覆盖：
 * 1. 服务初始化和配置
 * 2. 非流式对话接口（Normal/Thinking模型）
 * 3. 流式对话接口（Normal/Thinking模型）
 * 4. 一次性对话接口（系统提示词+用户输入）
 * 5. 图像对话接口
 * 6. 文本向量化接口
 * 7. 异常处理和边界条件
 * 8. 性能和并发测试
 * 
 * 注意：部分测试需要配置有效的ARK_API_KEY环境变量
 */
@SpringBootTest
@TestPropertySource(properties = {
        "my.doubao.think.model.name=doubao-1.5-thinking-pro-250415",
        "my.doubao.normal.model.name=doubao-1-5-pro-32k-250115",
        "my.doubao.url=https://ark.cn-beijing.volces.com/api/v3/",
        "my.doubao.embed.model-name=doubao-embedding-text-240715",
        "my.doubao.image.model.name=doubao-1-5-vision-pro-32k-250115",
        "my.doubao.estimate-token=500",
        "my.doubao.connection-pool.max-idle=100",
        "my.doubao.connection-pool.max-requests=100",
        "my.doubao.connection-pool.max-requests-per-host=50",
        "my.doubao.connection-pool.keep-alive-duration=5",
        "my.doubao.normal.endpoint.name=ep-20250629195408-gtv9c"
})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class DoubaoBigModelServiceImplTest {

    @Autowired
    private SuperBigModelInterface bigModelService;
    
    @Autowired
    private DoubaoConfig doubaoConfig;

    private List<ChatMessage> testMessages;
    private String testSystemPrompt;
    private String testUserPrompt;
    
    @BeforeEach
    public void setUp() {
        // 准备测试数据
        testSystemPrompt = "你是一个有用的AI助手，请简洁地回答用户的问题。";
        testUserPrompt = "请简单介绍一下Java编程语言。";
        
        testMessages = Arrays.asList(
            ChatMessage.builder()
                .role(ChatMessageRole.SYSTEM)
                .content(testSystemPrompt)
                .build(),
            ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .content(testUserPrompt)
                .build()
        );
        
        System.out.println("=== 豆包大模型服务测试初始化 ===");
    }

    @Test
    @Order(1)
    @DisplayName("测试服务初始化和配置")
    public void testServiceInitialization() {
        System.out.println("=== 测试1: 服务初始化和配置 ===");
        
        // 验证服务实例
        Assertions.assertNotNull(bigModelService, "大模型服务不应为null");
        Assertions.assertTrue(bigModelService instanceof DoubaoBigModelServiceImpl, 
                "服务应为DoubaoBigModelServiceImpl实例");
        
        // 验证配置
        Assertions.assertNotNull(doubaoConfig, "豆包配置不应为null");
        Assertions.assertNotNull(doubaoConfig.getThink(), "Thinking模型配置不应为null");
        Assertions.assertNotNull(doubaoConfig.getNormal(), "Normal模型配置不应为null");
        Assertions.assertNotNull(doubaoConfig.getEmbed(), "Embed模型配置不应为null");
        Assertions.assertNotNull(doubaoConfig.getUrl(), "API URL不应为null");
        
        // 验证模型名称
        Assertions.assertEquals("doubao-1.5-thinking-pro-250415", 
                doubaoConfig.getThink().getModel().getName(), "Thinking模型名称不匹配");
        Assertions.assertEquals("doubao-1-5-pro-32k-250115", 
                doubaoConfig.getNormal().getModel().getName(), "Normal模型名称不匹配");
        Assertions.assertEquals("doubao-embedding-text-240715", 
                doubaoConfig.getEmbed().getModelName(), "Embed模型名称不匹配");
        
        // 验证连接池配置
        DoubaoConfig.ConnectionPool poolConfig = doubaoConfig.getConnectionPool();
        Assertions.assertNotNull(poolConfig, "连接池配置不应为null");
        Assertions.assertEquals(100, poolConfig.getMaxIdle(), "最大空闲连接数不匹配");
        Assertions.assertEquals(100, poolConfig.getMaxRequests(), "最大请求数不匹配");
        Assertions.assertEquals(50, poolConfig.getMaxRequestsPerHost(), "每主机最大请求数不匹配");
        
        System.out.println("✅ 服务初始化测试通过");
        System.out.println("Thinking模型: " + doubaoConfig.getThink().getModel().getName());
        System.out.println("Normal模型: " + doubaoConfig.getNormal().getModel().getName());
        System.out.println("API URL: " + doubaoConfig.getUrl());
    }

    @Test
    @Order(2)
    @DisplayName("测试Normal模型非流式对话")
    public void testNormalModelNonStreaming() {
        System.out.println("=== 测试2: Normal模型非流式对话 ===");
        
        try {
            String response = bigModelService.ntns(testMessages);
            
            Assertions.assertNotNull(response, "响应不应为null");
            Assertions.assertFalse(response.trim().isEmpty(), "响应不应为空");
            Assertions.assertTrue(response.length() > 10, "响应长度应大于10个字符");
            
            System.out.println("✅ Normal模型非流式对话测试通过");
            System.out.println("响应长度: " + response.length() + " 字符");
            System.out.println("响应预览: " + response.substring(0, Math.min(100, response.length())) + "...");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY")) {
                System.out.println("⚠️ 跳过测试：需要配置ARK_API_KEY环境变量");
                Assumptions.assumeTrue(false, "需要配置ARK_API_KEY环境变量");
            } else {
                throw e;
            }
        }
    }

    @Test
    @Order(3)
    @DisplayName("测试Thinking模型非流式对话")
    public void testThinkingModelNonStreaming() {
        System.out.println("=== 测试3: Thinking模型非流式对话 ===");
        
        try {
            String response = bigModelService.tns(testMessages);
            
            Assertions.assertNotNull(response, "响应不应为null");
            Assertions.assertFalse(response.trim().isEmpty(), "响应不应为空");
            Assertions.assertTrue(response.length() > 10, "响应长度应大于10个字符");
            
            System.out.println("✅ Thinking模型非流式对话测试通过");
            System.out.println("响应长度: " + response.length() + " 字符");
            System.out.println("响应预览: " + response.substring(0, Math.min(100, response.length())) + "...");
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY")) {
                System.out.println("⚠️ 跳过测试：需要配置ARK_API_KEY环境变量");
                Assumptions.assumeTrue(false, "需要配置ARK_API_KEY环境变量");
            } else {
                throw e;
            }
        }
    }

    @Test
    @Order(4)
    @DisplayName("测试Normal模型流式对话")
    public void testNormalModelStreaming() {
        System.out.println("=== 测试4: Normal模型流式对话 ===");

        try {
            Flux<String> responseFlux = bigModelService.nts(testMessages);

            Assertions.assertNotNull(responseFlux, "响应流不应为null");

            // 简单的流式测试，收集所有响应
            StringBuilder fullResponse = new StringBuilder();
            responseFlux.doOnNext(content -> {
                System.out.print(content);
                fullResponse.append(content);
            }).doOnComplete(() -> {
                System.out.println("\n流式响应完成");
            }).blockLast(Duration.ofSeconds(30));

            Assertions.assertTrue(fullResponse.length() > 0, "应该收到流式响应内容");
            System.out.println("\n✅ Normal模型流式对话测试通过");
            System.out.println("总响应长度: " + fullResponse.length() + " 字符");

        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY")) {
                System.out.println("⚠️ 跳过测试：需要配置ARK_API_KEY环境变量");
                Assumptions.assumeTrue(false, "需要配置ARK_API_KEY环境变量");
            } else {
                throw e;
            }
        }
    }

    @Test
    @Order(5)
    @DisplayName("测试Thinking模型流式对话")
    public void testThinkingModelStreaming() {
        System.out.println("=== 测试5: Thinking模型流式对话 ===");

        try {
            Flux<String> responseFlux = bigModelService.ts(testMessages);

            Assertions.assertNotNull(responseFlux, "响应流不应为null");

            // 简单的流式测试，收集所有响应
            StringBuilder fullResponse = new StringBuilder();
            responseFlux.doOnNext(content -> {
                System.out.print(content);
                fullResponse.append(content);
            }).doOnComplete(() -> {
                System.out.println("\n流式响应完成");
            }).blockLast(Duration.ofSeconds(30));

            Assertions.assertTrue(fullResponse.length() > 0, "应该收到流式响应内容");
            System.out.println("\n✅ Thinking模型流式对话测试通过");
            System.out.println("总响应长度: " + fullResponse.length() + " 字符");

        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY")) {
                System.out.println("⚠️ 跳过测试：需要配置ARK_API_KEY环境变量");
                Assumptions.assumeTrue(false, "需要配置ARK_API_KEY环境变量");
            } else {
                throw e;
            }
        }
    }

    @Test
    @Order(6)
    @DisplayName("测试一次性对话接口")
    public void testOneTimeChat() {
        System.out.println("=== 测试6: 一次性对话接口 ===");

        try {
            // 测试Thinking模型一次性对话
            String thinkingResponse = bigModelService.tnsOnce(testSystemPrompt, testUserPrompt);

            Assertions.assertNotNull(thinkingResponse, "Thinking模型响应不应为null");
            Assertions.assertFalse(thinkingResponse.trim().isEmpty(), "Thinking模型响应不应为空");
            Assertions.assertTrue(thinkingResponse.length() > 10, "Thinking模型响应长度应大于10个字符");

            System.out.println("✅ Thinking模型一次性对话测试通过");
            System.out.println("响应长度: " + thinkingResponse.length() + " 字符");
            System.out.println("响应预览: " + thinkingResponse.substring(0, Math.min(100, thinkingResponse.length())) + "...");

            // 测试Normal模型一次性对话
            String normalResponse = bigModelService.ntnsOnce(testSystemPrompt, testUserPrompt);

            Assertions.assertNotNull(normalResponse, "Normal模型响应不应为null");
            Assertions.assertFalse(normalResponse.trim().isEmpty(), "Normal模型响应不应为空");
            Assertions.assertTrue(normalResponse.length() > 10, "Normal模型响应长度应大于10个字符");

            System.out.println("✅ Normal模型一次性对话测试通过");
            System.out.println("响应长度: " + normalResponse.length() + " 字符");

        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY")) {
                System.out.println("⚠️ 跳过测试：需要配置ARK_API_KEY环境变量");
                Assumptions.assumeTrue(false, "需要配置ARK_API_KEY环境变量");
            } else {
                throw e;
            }
        }
    }

    @Test
    @Order(7)
    @DisplayName("测试文本向量化接口")
    public void testTextEmbedding() {
        System.out.println("=== 测试7: 文本向量化接口 ===");

        List<String> texts = Arrays.asList(
                "Java是一种面向对象的编程语言",
                "Python是一种简洁易学的编程语言",
                "人工智能正在改变世界"
        );

        try {
            List<List<Double>> embeddings = bigModelService.embed(texts);

            Assertions.assertNotNull(embeddings, "向量化结果不应为null");
            Assertions.assertEquals(3, embeddings.size(), "应该返回3个向量");

            // 检查每个向量
            for (int i = 0; i < embeddings.size(); i++) {
                List<Double> embedding = embeddings.get(i);
                Assertions.assertNotNull(embedding, "第" + (i+1) + "个向量不应为null");
                Assertions.assertTrue(embedding.size() > 0, "第" + (i+1) + "个向量维度应大于0");

                // 检查向量维度一致性
                if (i > 0) {
                    Assertions.assertEquals(embeddings.get(0).size(), embedding.size(),
                            "所有向量的维度应该相同");
                }

                // 检查向量值的有效性
                boolean hasValidValues = embedding.stream()
                        .anyMatch(val -> val != null && !val.isNaN() && !val.isInfinite());
                Assertions.assertTrue(hasValidValues, "向量应包含有效的数值");
            }

            System.out.println("✅ 文本向量化测试通过");
            System.out.println("向量数量: " + embeddings.size());
            System.out.println("向量维度: " + embeddings.get(0).size());

        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY")) {
                System.out.println("⚠️ 跳过测试：需要配置ARK_API_KEY环境变量");
                Assumptions.assumeTrue(false, "需要配置ARK_API_KEY环境变量");
            } else {
                throw e;
            }
        }
    }

    @Test
    @Order(8)
    @DisplayName("测试带上下文的Normal模型对话")
    public void testNormalModelWithContext() {
        System.out.println("=== 测试8: 带上下文的Normal模型对话 ===");

        String contextId = "ctx-20250630111148-ptcg9";

        try {
            String response = bigModelService.ntnsWithCtx(testMessages, contextId);

            Assertions.assertNotNull(response, "响应不应为null");
            Assertions.assertFalse(response.trim().isEmpty(), "响应不应为空");
            Assertions.assertTrue(response.length() > 10, "响应长度应大于10个字符");

            System.out.println("✅ 带上下文的Normal模型对话测试通过");
            System.out.println("上下文ID: " + contextId);
            System.out.println("响应长度: " + response.length() + " 字符");
            System.out.println("响应预览: " + response.substring(0, Math.min(100, response.length())) + "...");

        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY")) {
                System.out.println("⚠️ 跳过测试：需要配置ARK_API_KEY环境变量");
                Assumptions.assumeTrue(false, "需要配置ARK_API_KEY环境变量");
            } else if (e.getMessage().contains("endpoint") || e.getMessage().contains("context")) {
                System.out.println("⚠️ 跳过测试：上下文功能可能不可用或endpoint配置问题");
                Assumptions.assumeTrue(false, "上下文功能可能不可用或endpoint配置问题");
            } else {
                throw e;
            }
        }
    }

    @Test
    @Order(9)
    @DisplayName("测试图像对话接口")
    public void testImageChat() {
        System.out.println("=== 测试8: 图像对话接口 ===");

        // 使用一个有效的图片URL而不是base64
        String imageUrl = "https://example.com/test-image.jpg";
        String imagePrompt = "请描述这张图片的内容。";
        String systemPrompt = "你是一个图像分析助手，请详细描述用户提供的图片内容。";

        try {
            // imageChat方法需要3个参数：imageUrl, prompt, systemPrompt
            Flux<String> responseFlux = bigModelService.imageChatWithStream(imageUrl, imagePrompt, systemPrompt);

            Assertions.assertNotNull(responseFlux, "响应流不应为null");

            // 收集流式响应
            StringBuilder fullResponse = new StringBuilder();
            responseFlux.doOnNext(content -> {
                System.out.print(content);
                fullResponse.append(content);
            }).doOnComplete(() -> {
                System.out.println("\n图像对话响应完成");
            }).blockLast(Duration.ofSeconds(30));

            Assertions.assertTrue(fullResponse.length() > 0, "应该收到图像对话响应内容");
            System.out.println("✅ 图像对话测试通过");
            System.out.println("响应长度: " + fullResponse.length() + " 字符");

        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY")) {
                System.out.println("⚠️ 跳过测试：需要配置ARK_API_KEY环境变量");
                Assumptions.assumeTrue(false, "需要配置ARK_API_KEY环境变量");
            } else if (e.getMessage().contains("不支持") || e.getMessage().contains("unsupported") ||
                       e.getMessage().contains("InvalidParameter") || e.getMessage().contains("not valid")) {
                System.out.println("⚠️ 跳过测试：当前模型不支持图像对话或参数无效");
                Assumptions.assumeTrue(false, "当前模型不支持图像对话或参数无效");
            } else {
                throw e;
            }
        }
    }

    @Test
    @Order(10)
    @DisplayName("测试非流式图像对话接口")
    public void testImageChatNonStream() {
        System.out.println("=== 测试9: 非流式图像对话接口 ===");

        // 使用一个有效的图片URL而不是base64
        String imageUrl = "https://ai-playground.oss-cn-shanghai.aliyuncs.com/temp/pic/pic_1_1750213792944_C-天猫商品详情.png";
        String imagePrompt = "请描述这张图片的内容。";
        String systemPrompt = "你是一个图像分析助手，请详细描述用户提供的图片内容。";

        try {
            // 测试非流式imageChat方法
            String response = bigModelService.imageChat(imageUrl, imagePrompt, systemPrompt);

            Assertions.assertNotNull(response, "响应不应为null");
            Assertions.assertTrue(response.length() > 0, "应该收到图像对话响应内容");

            System.out.println("非流式图像对话响应: " + response);
            System.out.println("✅ 非流式图像对话测试通过");
            System.out.println("响应长度: " + response.length() + " 字符");

        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY")) {
                System.out.println("⚠️ 跳过测试：需要配置ARK_API_KEY环境变量");
                Assumptions.assumeTrue(false, "需要配置ARK_API_KEY环境变量");
            } else if (e.getMessage().contains("不支持") || e.getMessage().contains("unsupported") ||
                       e.getMessage().contains("InvalidParameter") || e.getMessage().contains("not valid")) {
                System.out.println("⚠️ 跳过测试：当前模型不支持图像对话或参数无效");
                Assumptions.assumeTrue(false, "当前模型不支持图像对话或参数无效");
            } else {
                throw e;
            }
        }
    }

    @Test
    @Order(11)
    @DisplayName("测试异常处理 - 空消息列表")
    public void testExceptionHandling_EmptyMessages() {
        System.out.println("=== 测试10: 异常处理 - 空消息列表 ===");

        List<ChatMessage> emptyMessages = Collections.emptyList();

        // 测试非流式接口
        Assertions.assertThrows(RuntimeException.class, () -> {
            bigModelService.ntns(emptyMessages);
        }, "空消息列表应该抛出异常");

        Assertions.assertThrows(RuntimeException.class, () -> {
            bigModelService.tns(emptyMessages);
        }, "空消息列表应该抛出异常");

        // 测试流式接口 - 简单的错误检查
        try {
            Flux<String> normalFlux = bigModelService.nts(emptyMessages);
            normalFlux.blockFirst(Duration.ofSeconds(5));
            Assertions.fail("空消息列表应该抛出异常");
        } catch (RuntimeException e) {
            System.out.println("Normal流式接口正确抛出异常: " + e.getMessage());
        }

        try {
            Flux<String> thinkingFlux = bigModelService.ts(emptyMessages);
            thinkingFlux.blockFirst(Duration.ofSeconds(5));
            Assertions.fail("空消息列表应该抛出异常");
        } catch (RuntimeException e) {
            System.out.println("Thinking流式接口正确抛出异常: " + e.getMessage());
        }

        System.out.println("✅ 空消息列表异常处理测试通过");
    }

    @Test
    @Order(12)
    @DisplayName("测试异常处理和null参数容错")
    public void testExceptionHandling_NullParameters() {
        System.out.println("=== 测试11: 异常处理和null参数容错 ===");

        // 测试null消息列表 - 这个应该抛出异常
        Assertions.assertThrows(RuntimeException.class, () -> {
            bigModelService.ntns(null);
        }, "null消息列表应该抛出异常");

        try {
            // 测试null系统提示词 - 这个应该能正常处理（只使用用户提示词）
            String response1 = bigModelService.tnsOnce(null, testUserPrompt);
            Assertions.assertNotNull(response1, "null系统提示词时应该能正常返回响应");
            Assertions.assertFalse(response1.trim().isEmpty(), "响应不应为空");
            System.out.println("✅ null系统提示词容错处理正常");

            // 测试null用户提示词 - 这个可能会导致空消息列表，从而抛出异常
            try {
                String response2 = bigModelService.tnsOnce(testSystemPrompt, null);
                // 如果没有抛出异常，说明服务有容错处理
                System.out.println("✅ null用户提示词容错处理正常，响应: " + response2.substring(0, Math.min(50, response2.length())));
            } catch (RuntimeException e) {
                // 如果抛出异常，也是合理的（因为没有用户输入）
                System.out.println("✅ null用户提示词正确抛出异常: " + e.getMessage());
                Assertions.assertTrue(e.getMessage().contains("豆包"), "异常信息应包含豆包相关信息");
            }

        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY")) {
                System.out.println("⚠️ 跳过测试：需要配置ARK_API_KEY环境变量");
                Assumptions.assumeTrue(false, "需要配置ARK_API_KEY环境变量");
            } else {
                throw e;
            }
        }

        // 测试null文本列表 - 这个应该抛出异常
        Assertions.assertThrows(RuntimeException.class, () -> {
            bigModelService.embed(null);
        }, "null文本列表应该抛出异常");

        System.out.println("✅ null参数处理测试通过");
    }

    @Test
    @Order(13)
    @DisplayName("测试一次性流式对话接口")
    public void testOneTimeStreamingChat() {
        System.out.println("=== 测试12: 一次性流式对话接口 ===");

        try {
            // 测试Thinking模型一次性流式对话
            Flux<String> thinkingFlux = bigModelService.tsOnce(testSystemPrompt, testUserPrompt);

            Assertions.assertNotNull(thinkingFlux, "Thinking流式响应不应为null");

            StringBuilder thinkingResponse = new StringBuilder();
            thinkingFlux.doOnNext(content -> {
                System.out.print(content);
                thinkingResponse.append(content);
            }).doOnComplete(() -> {
                System.out.println("\nThinking流式响应完成");
            }).blockLast(Duration.ofSeconds(30));

            Assertions.assertTrue(thinkingResponse.length() > 0, "应该收到Thinking流式响应内容");

            // 测试Normal模型一次性流式对话
            Flux<String> normalFlux = bigModelService.ntsOnce(testSystemPrompt, testUserPrompt);

            Assertions.assertNotNull(normalFlux, "Normal流式响应不应为null");

            StringBuilder normalResponse = new StringBuilder();
            normalFlux.doOnNext(content -> {
                System.out.print(content);
                normalResponse.append(content);
            }).doOnComplete(() -> {
                System.out.println("\nNormal流式响应完成");
            }).blockLast(Duration.ofSeconds(30));

            Assertions.assertTrue(normalResponse.length() > 0, "应该收到Normal流式响应内容");

            System.out.println("✅ 一次性流式对话测试通过");
            System.out.println("Thinking响应长度: " + thinkingResponse.length() + " 字符");
            System.out.println("Normal响应长度: " + normalResponse.length() + " 字符");

        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY")) {
                System.out.println("⚠️ 跳过测试：需要配置ARK_API_KEY环境变量");
                Assumptions.assumeTrue(false, "需要配置ARK_API_KEY环境变量");
            } else {
                throw e;
            }
        }
    }

    @Test
    @Order(14)
    @DisplayName("测试边界条件 - 长文本处理")
    public void testLongTextHandling() {
        System.out.println("=== 测试13: 边界条件 - 长文本处理 ===");

        // 构造长文本
        StringBuilder longText = new StringBuilder();
        for (int i = 0; i < 100; i++) {
            longText.append("这是一段很长的测试文本，用于测试大模型对长文本的处理能力。");
        }

        List<ChatMessage> longMessages = Arrays.asList(
            ChatMessage.builder()
                .role(ChatMessageRole.SYSTEM)
                .content("你是一个文本分析助手，请简洁地总结用户提供的文本内容。")
                .build(),
            ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .content(longText.toString())
                .build()
        );

        try {
            String response = bigModelService.ntns(longMessages);

            Assertions.assertNotNull(response, "长文本响应不应为null");
            Assertions.assertFalse(response.trim().isEmpty(), "长文本响应不应为空");

            System.out.println("✅ 长文本处理测试通过");
            System.out.println("输入文本长度: " + longText.length() + " 字符");
            System.out.println("响应长度: " + response.length() + " 字符");
            System.out.println("响应预览: " + response.substring(0, Math.min(100, response.length())) + "...");

        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY")) {
                System.out.println("⚠️ 跳过测试：需要配置ARK_API_KEY环境变量");
                Assumptions.assumeTrue(false, "需要配置ARK_API_KEY环境变量");
            } else if (e.getMessage().contains("token") || e.getMessage().contains("length")) {
                System.out.println("⚠️ 长文本超出模型限制（符合预期）: " + e.getMessage());
            } else {
                throw e;
            }
        }
    }

    @Test
    @Order(15)
    @DisplayName("测试特殊字符处理")
    public void testSpecialCharacterHandling() {
        System.out.println("=== 测试14: 特殊字符处理 ===");

        String specialText = "测试特殊字符：\n换行符\t制表符\"引号'单引号\\反斜杠&符号<标签>emoji😀🎉数学符号∑∏∫";

        List<ChatMessage> specialMessages = Arrays.asList(
            ChatMessage.builder()
                .role(ChatMessageRole.SYSTEM)
                .content("你是一个文本处理助手，请简单回复用户的输入。")
                .build(),
            ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .content(specialText)
                .build()
        );

        try {
            String response = bigModelService.ntns(specialMessages);

            Assertions.assertNotNull(response, "特殊字符响应不应为null");
            Assertions.assertFalse(response.trim().isEmpty(), "特殊字符响应不应为空");

            System.out.println("✅ 特殊字符处理测试通过");
            System.out.println("输入: " + specialText);
            System.out.println("响应: " + response.substring(0, Math.min(100, response.length())) + "...");

        } catch (RuntimeException e) {
            if (e.getMessage().contains("ARK_API_KEY")) {
                System.out.println("⚠️ 跳过测试：需要配置ARK_API_KEY环境变量");
                Assumptions.assumeTrue(false, "需要配置ARK_API_KEY环境变量");
            } else {
                throw e;
            }
        }
    }

    @Test
    @Order(16)
    @DisplayName("测试性能基准")
    @Disabled("仅在需要进行性能测试时启用")
    public void testPerformanceBenchmark() {
        System.out.println("=== 测试15: 性能基准测试 ===");

        int testRounds = 5;
        long totalTime = 0;

        for (int i = 0; i < testRounds; i++) {
            long startTime = System.currentTimeMillis();

            try {
                String response = bigModelService.ntns(testMessages);
                Assertions.assertNotNull(response);

                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                totalTime += duration;

                System.out.println("第" + (i+1) + "轮耗时: " + duration + "ms");

            } catch (RuntimeException e) {
                if (e.getMessage().contains("ARK_API_KEY")) {
                    System.out.println("⚠️ 跳过性能测试：需要配置ARK_API_KEY环境变量");
                    return;
                } else {
                    throw e;
                }
            }
        }

        long averageTime = totalTime / testRounds;
        System.out.println("✅ 性能基准测试完成");
        System.out.println("平均响应时间: " + averageTime + "ms");
        System.out.println("总耗时: " + totalTime + "ms");

        // 性能应该在合理范围内（比如平均10秒内）
        Assertions.assertTrue(averageTime < 10000, "平均响应时间应在10秒内");
    }

    @AfterEach
    public void tearDown() {
        System.out.println("测试完成\n");
    }
}
