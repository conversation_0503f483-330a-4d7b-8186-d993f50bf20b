package com.yiyi.ai_train_playground.service.impl.jd;

import com.jd.open.api.sdk.domain.ware.WareReadService.response.searchWare4Valid.Ware;
import com.yiyi.ai_train_playground.service.jd.YiYiJdService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * YiYiJdService商品Ware列表测试类
 * 
 * <AUTHOR>
 * @since 2025-07-10
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
class YiYiJdServiceWareTest {

    @Autowired
    private YiYiJdService yiYiJdService;

    @Test
    @DisplayName("测试获取京东商品Ware列表")
    void testGetWareList() {
        // 给定
        String accessToken = "测试令牌"; // 使用数据库中存在的测试令牌
        Integer pageNo = 1;
        Integer pageSize = 5;

        // 当
        List<Ware> wareList = yiYiJdService.getWareList(accessToken, pageNo, pageSize);

        // 那么
        assertThat(wareList).isNotNull();
        log.info("获取到 {} 个Ware对象", wareList.size());

        // 验证返回的数据结构
        if (!wareList.isEmpty()) {
            Ware firstWare = wareList.get(0);
            log.info("第一个Ware对象: wareId={}, title={}", firstWare.getWareId(), firstWare.getTitle());
            
            // 基本字段验证
            assertThat(firstWare.getWareId()).isNotNull();
            assertThat(firstWare.getTitle()).isNotNull();
            
            // 检查features和multiCateProps字段是否存在
            if (firstWare.getFeatures() != null) {
                log.info("Features字段: {}", firstWare.getFeatures());
            }
            if (firstWare.getMultiCateProps() != null) {
                log.info("MultiCateProps字段: {}", firstWare.getMultiCateProps());
            }
        }
    }

    @Test
    @DisplayName("测试空访问令牌")
    void testGetWareListWithEmptyAccessToken() {
        // 给定
        String accessToken = "";
        Integer pageNo = 1;
        Integer pageSize = 5;

        // 当
        List<Ware> wareList = yiYiJdService.getWareList(accessToken, pageNo, pageSize);

        // 那么
        assertThat(wareList).isNotNull().isEmpty();
        log.info("空访问令牌测试通过，返回空列表");
    }

    @Test
    @DisplayName("测试无效访问令牌")
    void testGetWareListWithInvalidAccessToken() {
        // 给定
        String accessToken = "invalid_token_123";
        Integer pageNo = 1;
        Integer pageSize = 5;

        // 当
        List<Ware> wareList = yiYiJdService.getWareList(accessToken, pageNo, pageSize);

        // 那么
        assertThat(wareList).isNotNull().isEmpty();
        log.info("无效访问令牌测试通过，返回空列表");
    }
} 