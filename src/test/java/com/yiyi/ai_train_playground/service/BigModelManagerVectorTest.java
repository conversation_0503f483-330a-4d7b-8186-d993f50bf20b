package com.yiyi.ai_train_playground.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class BigModelManagerVectorTest {
    
    @Autowired
    private BigModelManager bigModelManager;
    
    @Test
    public void testBigModelManagerExists() {
        System.out.println("BigModelManager已成功注入: " + (bigModelManager != null));
    }
} 