package com.yiyi.ai_train_playground.service.sms.impl;

import com.yiyi.ai_train_playground.dto.SmsCodeResponse;
import com.yiyi.ai_train_playground.dto.SmsLoginResponse;
import com.yiyi.ai_train_playground.entity.TrainJdAccessToken;
import com.yiyi.ai_train_playground.entity.TrainTeam;
import com.yiyi.ai_train_playground.entity.User;
import com.yiyi.ai_train_playground.mapper.TrainJdAccessTokenMapper;
import com.yiyi.ai_train_playground.mapper.TrainTeamMapper;
import com.yiyi.ai_train_playground.mapper.UserMapper;
import com.yiyi.ai_train_playground.util.JwtUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SmsServiceV2Impl测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-22
 */
@ExtendWith(MockitoExtension.class)
class SmsServiceV2ImplTest {

    @Mock
    private StringRedisTemplate stringRedisTemplate;

    @Mock
    private UserMapper userMapper;

    @Mock
    private TrainTeamMapper trainTeamMapper;

    @Mock
    private TrainJdAccessTokenMapper trainJdAccessTokenMapper;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private JwtUtil jwtUtil;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @InjectMocks
    private SmsServiceV2Impl smsServiceV2Impl;

    @BeforeEach
    void setUp() {
        lenient().when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    void testSendVerificationCode_Success() {
        // Given
        String phone = "13800138000";
        when(stringRedisTemplate.hasKey(anyString())).thenReturn(false);

        // When
        SmsCodeResponse response = smsServiceV2Impl.sendVerificationCode(phone);

        // Then
        assertNotNull(response);
        assertNotNull(response.getVerificationKey());
        assertFalse(response.getVerificationKey().isEmpty());

        // 验证Redis操作
        verify(valueOperations, times(2)).set(anyString(), anyString(), any(Duration.class));
    }

    @Test
    void testSendVerificationCode_TooFrequent() {
        // Given
        String phone = "13800138000";
        when(stringRedisTemplate.hasKey(anyString())).thenReturn(true);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            smsServiceV2Impl.sendVerificationCode(phone);
        });

        assertEquals("发送过于频繁，请1分钟后再试", exception.getMessage());
    }

    @Test
    void testVerifyCode_Success() {
        // Given
        String verificationKey = "test-key";
        String verificationCode = "1234";
        String storedValue = "13800138000:1234";

        when(valueOperations.get(anyString())).thenReturn(storedValue);

        // When
        boolean result = smsServiceV2Impl.verifyCode(verificationKey, verificationCode);

        // Then
        assertTrue(result);
        verify(stringRedisTemplate).delete(anyString());
    }

    @Test
    void testVerifyCode_WrongCode() {
        // Given
        String verificationKey = "test-key";
        String verificationCode = "5678";
        String storedValue = "13800138000:1234";

        when(valueOperations.get(anyString())).thenReturn(storedValue);

        // When
        boolean result = smsServiceV2Impl.verifyCode(verificationKey, verificationCode);

        // Then
        assertFalse(result);
        verify(stringRedisTemplate, never()).delete(anyString());
    }

    @Test
    void testVerifyCode_ExpiredCode() {
        // Given
        String verificationKey = "test-key";
        String verificationCode = "1234";

        when(valueOperations.get(anyString())).thenReturn(null);

        // When
        boolean result = smsServiceV2Impl.verifyCode(verificationKey, verificationCode);

        // Then
        assertFalse(result);
    }

    @Test
    void testSmsLogin_ExistingUser() {
        // Given
        String verificationKey = "test-key";
        String verificationCode = "1234";
        String phone = "13800138000";
        String storedValue = phone + ":" + verificationCode;

        User existingUser = new User();
        existingUser.setId(1L);
        existingUser.setUsername("testuser");
        existingUser.setDisplayName("Test User");
        existingUser.setMobile(phone);
        existingUser.setTeamId(1L);

        when(valueOperations.get(anyString())).thenReturn(storedValue);
        when(userMapper.findByMobile(phone)).thenReturn(existingUser);
        when(jwtUtil.generateToken(anyLong(), anyString(), anyLong(), anyBoolean())).thenReturn("test-token");

        // When
        SmsLoginResponse response = smsServiceV2Impl.smsLogin(verificationKey, verificationCode, null, "test-xid");

        // Then
        assertNotNull(response);
        assertEquals(existingUser.getId(), response.getUserId());
        assertEquals(existingUser.getUsername(), response.getUsername());
        assertEquals(existingUser.getDisplayName(), response.getDisplayName());
        assertEquals(existingUser.getMobile(), response.getMobile());
        assertEquals("test-token", response.getToken());

        verify(stringRedisTemplate).delete(anyString());
        verify(trainTeamMapper, never()).insert(any(TrainTeam.class));
        verify(userMapper, never()).insert(any(User.class));
    }

    @Test
    void testSmsLogin_NewUser() {
        // Given
        String verificationKey = "test-key";
        String verificationCode = "1234";
        String phone = "13800138000";
        String storedValue = phone + ":" + verificationCode;

        TrainTeam team = new TrainTeam();
        team.setId(1L);

        User newUser = new User();
        newUser.setId(2L);
        newUser.setUsername("user_12345678");
        newUser.setDisplayName("手机用户");
        newUser.setMobile(phone);
        newUser.setTeamId(1L);

        when(valueOperations.get(anyString())).thenReturn(storedValue);
        when(userMapper.findByMobile(phone)).thenReturn(null);
        when(trainTeamMapper.insert(any(TrainTeam.class))).thenAnswer(invocation -> {
            TrainTeam t = invocation.getArgument(0);
            t.setId(1L);
            return 1;
        });
        when(userMapper.findByUsername(anyString())).thenReturn(null);
        when(passwordEncoder.encode(anyString())).thenReturn("encoded-password");
        when(userMapper.insert(any(User.class))).thenAnswer(invocation -> {
            User u = invocation.getArgument(0);
            u.setId(2L);
            return 1;
        });
        when(trainJdAccessTokenMapper.updateJATByXid(any(TrainJdAccessToken.class))).thenReturn(1);
        when(jwtUtil.generateToken(anyLong(), anyString(), anyLong(), anyBoolean())).thenReturn("test-token");

        // When
        SmsLoginResponse response = smsServiceV2Impl.smsLogin(verificationKey, verificationCode, null, "test-xid");

        // Then
        assertNotNull(response);
        assertEquals(2L, response.getUserId());
        assertTrue(response.getUsername().startsWith("user_"));
        assertEquals("手机用户", response.getDisplayName());
        assertEquals(phone, response.getMobile());
        assertEquals("test-token", response.getToken());

        verify(stringRedisTemplate).delete(anyString());
        verify(trainTeamMapper).insert(any(TrainTeam.class));
        verify(userMapper).insert(any(User.class));
    }

    @Test
    void testSmsLogin_WrongCode() {
        // Given
        String verificationKey = "test-key";
        String verificationCode = "5678";
        String storedValue = "13800138000:1234";

        when(valueOperations.get(anyString())).thenReturn(storedValue);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            smsServiceV2Impl.smsLogin(verificationKey, verificationCode, null, "test-xid");
        });

        assertEquals("验证码错误", exception.getMessage());
    }

    @Test
    void testSmsLogin_ExpiredCode() {
        // Given
        String verificationKey = "test-key";
        String verificationCode = "1234";

        when(valueOperations.get(anyString())).thenReturn(null);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            smsServiceV2Impl.smsLogin(verificationKey, verificationCode, null, "test-xid");
        });

        assertEquals("验证码不存在或已过期", exception.getMessage());
    }
}
