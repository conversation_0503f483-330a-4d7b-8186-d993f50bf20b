package com.yiyi.ai_train_playground.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.TestPropertySource;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * RedisCacheManager集成测试
 * 使用真实的Redis环境进行测试
 */
@Slf4j
@SpringBootTest
@TestPropertySource(properties = {
        "spring.data.redis.host=localhost",
        "spring.data.redis.port=6379",
        "spring.data.redis.database=15"  // 使用专用测试数据库
})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("RedisCacheManager集成测试")
public class RedisCacheManagerIntegrationTest {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    private RedisCacheManager cacheManager;

    private static final String TEST_KEY_PREFIX = "test:cache:integration:";
    private static final String TEST_VALUE_KEY = TEST_KEY_PREFIX + "value";
    private static final String TEST_HASH_KEY = TEST_KEY_PREFIX + "hash";
    private static final String TEST_LIST_KEY = TEST_KEY_PREFIX + "list";

    @BeforeEach
    void setUp() {
        cacheManager = new RedisCacheManager(redisTemplate, objectMapper);
        
        // 清理测试数据
        cleanupTestData();
    }

    @AfterEach
    void tearDown() {
        // 清理测试数据
        cleanupTestData();
    }

    private void cleanupTestData() {
        Set<String> keys = redisTemplate.keys(TEST_KEY_PREFIX + "*");
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
    }

    @Test
    @Order(1)
    @DisplayName("测试基本的值存储和获取")
    void testBasicValueOperations() {
        log.info("=== 测试1: 基本的值存储和获取 ===");

        // 测试字符串值
        String stringValue = "Hello Redis Cache Manager";
        cacheManager.put(TEST_VALUE_KEY + ":string", stringValue);
        
        Object retrievedString = cacheManager.get(TEST_VALUE_KEY + ":string");
        Assertions.assertEquals(stringValue, retrievedString, "字符串值应该相等");

        // 测试数字值
        Integer intValue = 12345;
        cacheManager.put(TEST_VALUE_KEY + ":int", intValue);
        
        var retrievedInt = cacheManager.get(TEST_VALUE_KEY + ":int", Integer.class);
        Assertions.assertTrue(retrievedInt.isPresent(), "应该能获取到整数值");
        Assertions.assertEquals(intValue, retrievedInt.get(), "整数值应该相等");

        log.info("✅ 基本值操作测试通过");
    }

    @Test
    @Order(2)
    @DisplayName("测试复杂对象的存储和获取")
    void testComplexObjectOperations() {
        log.info("=== 测试2: 复杂对象的存储和获取 ===");

        // 创建测试用的ChatMessage列表
        List<ChatMessage> messages = Arrays.asList(
            ChatMessage.builder().role(ChatMessageRole.USER).content("用户消息1").build(),
            ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content("助手回复1").build(),
            ChatMessage.builder().role(ChatMessageRole.USER).content("用户消息2").build()
        );

        // 存储复杂对象
        cacheManager.put(TEST_LIST_KEY, messages);

        // 获取复杂对象
        Object retrievedObj = cacheManager.get(TEST_LIST_KEY);
        Assertions.assertNotNull(retrievedObj, "应该能获取到对象");

        // 验证对象类型和内容
        Assertions.assertTrue(retrievedObj instanceof List, "对象应该是List类型");
        @SuppressWarnings("unchecked")
        List<Object> retrievedList = (List<Object>) retrievedObj;
        Assertions.assertEquals(messages.size(), retrievedList.size(), "列表大小应该相等");

        log.info("✅ 复杂对象操作测试通过");
    }

    @Test
    @Order(3)
    @DisplayName("测试带过期时间的操作")
    void testOperationsWithExpiration() {
        log.info("=== 测试3: 带过期时间的操作 ===");

        String key = TEST_VALUE_KEY + ":expiring";
        String value = "这个值会过期";

        // 存储带短过期时间的值
        cacheManager.put(key, value, 2, TimeUnit.SECONDS);

        // 立即验证值存在
        Assertions.assertTrue(cacheManager.exists(key), "键应该存在");
        Assertions.assertEquals(value, cacheManager.get(key), "值应该正确");

        // 验证过期时间设置
        long expireTime = cacheManager.getExpire(key);
        Assertions.assertTrue(expireTime > 0 && expireTime <= 2, 
                             "过期时间应该在0-2秒之间，实际：" + expireTime);

        // 等待过期（增加一点缓冲时间）
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 验证键已过期
        Assertions.assertFalse(cacheManager.exists(key), "键应该已过期");
        Assertions.assertNull(cacheManager.get(key), "过期后应该获取不到值");

        log.info("✅ 过期时间操作测试通过");
    }

    @Test
    @Order(4)
    @DisplayName("测试Hash操作")
    void testHashOperations() {
        log.info("=== 测试4: Hash操作 ===");

        Map<String, Object> sessionConfig = new HashMap<>();
        sessionConfig.put("sessionSystemPrompts", "你是一个AI助手");
        sessionConfig.put("sessionRobotNames", "测试机器人");
        sessionConfig.put("isThinking", true);
        sessionConfig.put("isStreaming", false);
        sessionConfig.put("teamId", "12345");

        // 存储Hash
        cacheManager.putHash(TEST_HASH_KEY, sessionConfig);

        // 验证Hash存在
        Assertions.assertTrue(cacheManager.exists(TEST_HASH_KEY), "Hash键应该存在");

        // 获取整个Hash
        Map<Object, Object> retrievedHash = cacheManager.getHash(TEST_HASH_KEY);
        Assertions.assertFalse(retrievedHash.isEmpty(), "Hash不应该为空");
        Assertions.assertEquals(sessionConfig.size(), retrievedHash.size(), "Hash大小应该相等");

        // 验证具体字段
        Assertions.assertEquals("你是一个AI助手", 
                               cacheManager.getHashField(TEST_HASH_KEY, "sessionSystemPrompts"));
        Assertions.assertEquals("测试机器人", 
                               cacheManager.getHashField(TEST_HASH_KEY, "sessionRobotNames"));
        Assertions.assertEquals(true, 
                               cacheManager.getHashField(TEST_HASH_KEY, "isThinking"));

        log.info("✅ Hash操作测试通过");
    }

    @Test
    @Order(5)
    @DisplayName("测试Hash带过期时间操作")
    void testHashWithExpiration() {
        log.info("=== 测试5: Hash带过期时间操作 ===");

        String key = TEST_HASH_KEY + ":expiring";
        Map<String, Object> hashData = new HashMap<>();
        hashData.put("field1", "value1");
        hashData.put("field2", "value2");

        // 存储带过期时间的Hash
        cacheManager.putHash(key, hashData, 1, TimeUnit.HOURS);

        // 验证Hash存在且有过期时间
        Assertions.assertTrue(cacheManager.exists(key), "Hash键应该存在");
        long expireTime = cacheManager.getExpire(key);
        Assertions.assertTrue(expireTime > 3500 && expireTime <= 3600, 
                             "过期时间应该接近1小时，实际：" + expireTime + "秒");

        // 验证Hash内容
        Map<Object, Object> retrievedHash = cacheManager.getHash(key);
        Assertions.assertEquals(hashData.size(), retrievedHash.size(), "Hash大小应该相等");

        log.info("✅ Hash带过期时间操作测试通过");
    }

    @Test
    @Order(6)
    @DisplayName("测试Duration过期时间")
    void testDurationExpiration() {
        log.info("=== 测试6: Duration过期时间 ===");

        String key = TEST_VALUE_KEY + ":duration";
        String value = "Duration过期测试";

        // 使用Duration设置过期时间
        Duration duration = Duration.ofMinutes(5);
        cacheManager.put(key, value, duration);

        // 验证值存在
        Assertions.assertTrue(cacheManager.exists(key), "键应该存在");
        Assertions.assertEquals(value, cacheManager.get(key), "值应该正确");

        // 验证过期时间（大约5分钟 = 300秒，允许一些误差）
        long expireTime = cacheManager.getExpire(key);
        Assertions.assertTrue(expireTime > 290 && expireTime <= 300, 
                             "过期时间应该接近5分钟，实际：" + expireTime + "秒");

        log.info("✅ Duration过期时间测试通过");
    }

    @Test
    @Order(7)
    @DisplayName("测试键删除操作")
    void testDeleteOperations() {
        log.info("=== 测试7: 键删除操作 ===");

        String key1 = TEST_VALUE_KEY + ":delete1";
        String key2 = TEST_VALUE_KEY + ":delete2";

        // 存储两个值
        cacheManager.put(key1, "value1");
        cacheManager.put(key2, "value2");

        // 验证值存在
        Assertions.assertTrue(cacheManager.exists(key1), "键1应该存在");
        Assertions.assertTrue(cacheManager.exists(key2), "键2应该存在");

        // 删除键1
        boolean deleted1 = cacheManager.delete(key1);
        Assertions.assertTrue(deleted1, "键1应该删除成功");
        Assertions.assertFalse(cacheManager.exists(key1), "键1应该不存在");
        Assertions.assertTrue(cacheManager.exists(key2), "键2应该仍然存在");

        // 删除不存在的键
        boolean deleted3 = cacheManager.delete(TEST_VALUE_KEY + ":nonexistent");
        Assertions.assertFalse(deleted3, "删除不存在的键应该返回false");

        log.info("✅ 键删除操作测试通过");
    }

    @Test
    @Order(8)
    @DisplayName("测试边界情况和异常处理")
    void testEdgeCasesAndExceptionHandling() {
        log.info("=== 测试8: 边界情况和异常处理 ===");

        // 测试获取不存在的键
        Object nonExistent = cacheManager.get("nonexistent:key");
        Assertions.assertNull(nonExistent, "不存在的键应该返回null");

        var nonExistentTyped = cacheManager.get("nonexistent:key", String.class);
        Assertions.assertFalse(nonExistentTyped.isPresent(), "不存在的键应该返回空Optional");

        // 测试空值处理
        cacheManager.put(TEST_VALUE_KEY + ":null", null);
        Object nullValue = cacheManager.get(TEST_VALUE_KEY + ":null");
        Assertions.assertNull(nullValue, "存储null值应该返回null");

        // 测试空Map
        Map<String, Object> emptyMap = new HashMap<>();
        cacheManager.putHash(TEST_HASH_KEY + ":empty", emptyMap);
        Map<Object, Object> retrievedEmptyMap = cacheManager.getHash(TEST_HASH_KEY + ":empty");
        Assertions.assertTrue(retrievedEmptyMap.isEmpty(), "空Map应该返回空Map");

        // 测试获取不存在的Hash字段
        Object nonExistentField = cacheManager.getHashField(TEST_HASH_KEY + ":empty", "nonexistent");
        Assertions.assertNull(nonExistentField, "不存在的Hash字段应该返回null");

        log.info("✅ 边界情况和异常处理测试通过");
    }

    @Test
    @Order(9)
    @DisplayName("性能基准测试")
    void testPerformanceBenchmark() {
        log.info("=== 测试9: 性能基准测试 ===");

        int operationCount = 1000;
        String keyPrefix = TEST_VALUE_KEY + ":perf:";

        // 测试批量写入性能
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < operationCount; i++) {
            cacheManager.put(keyPrefix + i, "value" + i);
        }
        long writeTime = System.currentTimeMillis() - startTime;
        log.info("批量写入{}个键耗时: {}ms", operationCount, writeTime);

        // 测试批量读取性能
        startTime = System.currentTimeMillis();
        for (int i = 0; i < operationCount; i++) {
            Object value = cacheManager.get(keyPrefix + i);
            Assertions.assertNotNull(value, "值不应该为null");
        }
        long readTime = System.currentTimeMillis() - startTime;
        log.info("批量读取{}个键耗时: {}ms", operationCount, readTime);

        // 性能断言（比较宽松的限制）
        Assertions.assertTrue(writeTime < 10000, "写入性能应该在合理范围内");
        Assertions.assertTrue(readTime < 5000, "读取性能应该在合理范围内");

        log.info("✅ 性能基准测试通过");
    }
}