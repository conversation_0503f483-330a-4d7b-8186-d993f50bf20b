package com.yiyi.ai_train_playground.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * RedisCacheManager单元测试
 * 使用Mock测试各种缓存操作
 */
@SpringBootTest
@DisplayName("RedisCacheManager单元测试")
public class RedisCacheManagerTest {

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private ObjectMapper objectMapper;

    private RedisCacheManager cacheManager;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        cacheManager = new RedisCacheManager(redisTemplate, objectMapper);
    }

    @Test
    @DisplayName("测试存储对象值")
    void testPutValue() {
        // Arrange
        String key = "test:key";
        String value = "test value";

        // Act
        cacheManager.put(key, value);

        // Assert
        verify(redisTemplate.opsForValue()).set(key, value);
    }

    @Test
    @DisplayName("测试存储对象值（带过期时间）")
    void testPutValueWithTimeout() {
        // Arrange
        String key = "test:key";
        String value = "test value";
        long timeout = 1;
        TimeUnit unit = TimeUnit.HOURS;

        // Act
        cacheManager.put(key, value, timeout, unit);

        // Assert
        verify(redisTemplate.opsForValue()).set(key, value, timeout, unit);
    }

    @Test
    @DisplayName("测试存储对象值（带Duration过期时间）")
    void testPutValueWithDuration() {
        // Arrange
        String key = "test:key";
        String value = "test value";
        Duration duration = Duration.ofHours(1);

        // Act
        cacheManager.put(key, value, duration);

        // Assert
        verify(redisTemplate.opsForValue()).set(key, value, duration.toMillis(), TimeUnit.MILLISECONDS);
    }

    @Test
    @DisplayName("测试获取对象值")
    void testGetValue() {
        // Arrange
        String key = "test:key";
        String expectedValue = "test value";
        when(redisTemplate.opsForValue().get(key)).thenReturn(expectedValue);

        // Act
        Object result = cacheManager.get(key);

        // Assert
        assertEquals(expectedValue, result);
        verify(redisTemplate.opsForValue()).get(key);
    }

    @Test
    @DisplayName("测试获取指定类型的对象值")
    void testGetValueWithType() {
        // Arrange
        String key = "test:key";
        String expectedValue = "test value";
        when(redisTemplate.opsForValue().get(key)).thenReturn(expectedValue);

        // Act
        var result = cacheManager.get(key, String.class);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(expectedValue, result.get());
    }

    @Test
    @DisplayName("测试获取不存在的值")
    void testGetNonExistentValue() {
        // Arrange
        String key = "test:nonexistent";
        when(redisTemplate.opsForValue().get(key)).thenReturn(null);

        // Act
        var result = cacheManager.get(key, String.class);

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    @DisplayName("测试批量存储Hash字段")
    void testPutHash() {
        // Arrange
        String key = "test:hash";
        Map<String, Object> hash = new HashMap<>();
        hash.put("field1", "value1");
        hash.put("field2", "value2");

        // Act
        cacheManager.putHash(key, hash);

        // Assert
        verify(redisTemplate.opsForHash()).putAll(key, hash);
    }

    @Test
    @DisplayName("测试批量存储Hash字段（带过期时间）")
    void testPutHashWithTimeout() {
        // Arrange
        String key = "test:hash";
        Map<String, Object> hash = new HashMap<>();
        hash.put("field1", "value1");
        long timeout = 2;
        TimeUnit unit = TimeUnit.HOURS;
        when(redisTemplate.expire(key, timeout, unit)).thenReturn(true);

        // Act
        cacheManager.putHash(key, hash, timeout, unit);

        // Assert
        verify(redisTemplate.opsForHash()).putAll(key, hash);
        verify(redisTemplate).expire(key, timeout, unit);
    }

    @Test
    @DisplayName("测试获取Hash所有字段")
    void testGetHash() {
        // Arrange
        String key = "test:hash";
        Map<Object, Object> expectedHash = new HashMap<>();
        expectedHash.put("field1", "value1");
        expectedHash.put("field2", "value2");
        when(redisTemplate.opsForHash().entries(key)).thenReturn(expectedHash);

        // Act
        Map<Object, Object> result = cacheManager.getHash(key);

        // Assert
        assertEquals(expectedHash, result);
        verify(redisTemplate.opsForHash()).entries(key);
    }

    @Test
    @DisplayName("测试获取Hash单个字段")
    void testGetHashField() {
        // Arrange
        String key = "test:hash";
        String hashKey = "field1";
        String expectedValue = "value1";
        when(redisTemplate.opsForHash().get(key, hashKey)).thenReturn(expectedValue);

        // Act
        Object result = cacheManager.getHashField(key, hashKey);

        // Assert
        assertEquals(expectedValue, result);
        verify(redisTemplate.opsForHash()).get(key, hashKey);
    }

    @Test
    @DisplayName("测试检查键是否存在")
    void testExists() {
        // Arrange
        String key = "test:key";
        when(redisTemplate.hasKey(key)).thenReturn(true);

        // Act
        boolean result = cacheManager.exists(key);

        // Assert
        assertTrue(result);
        verify(redisTemplate).hasKey(key);
    }

    @Test
    @DisplayName("测试删除键")
    void testDelete() {
        // Arrange
        String key = "test:key";
        when(redisTemplate.delete(key)).thenReturn(true);

        // Act
        boolean result = cacheManager.delete(key);

        // Assert
        assertTrue(result);
        verify(redisTemplate).delete(key);
    }

    @Test
    @DisplayName("测试设置键的过期时间")
    void testExpire() {
        // Arrange
        String key = "test:key";
        long timeout = 1;
        TimeUnit unit = TimeUnit.HOURS;
        when(redisTemplate.expire(key, timeout, unit)).thenReturn(true);

        // Act
        boolean result = cacheManager.expire(key, timeout, unit);

        // Assert
        assertTrue(result);
        verify(redisTemplate).expire(key, timeout, unit);
    }

    @Test
    @DisplayName("测试设置键的过期时间（Duration）")
    void testExpireWithDuration() {
        // Arrange
        String key = "test:key";
        Duration duration = Duration.ofHours(1);
        when(redisTemplate.expire(key, duration.toMillis(), TimeUnit.MILLISECONDS)).thenReturn(true);

        // Act
        boolean result = cacheManager.expire(key, duration);

        // Assert
        assertTrue(result);
        verify(redisTemplate).expire(key, duration.toMillis(), TimeUnit.MILLISECONDS);
    }

    @Test
    @DisplayName("测试获取键的剩余生存时间")
    void testGetExpire() {
        // Arrange
        String key = "test:key";
        long expectedExpire = 3600L;
        when(redisTemplate.getExpire(key)).thenReturn(expectedExpire);

        // Act
        long result = cacheManager.getExpire(key);

        // Assert
        assertEquals(expectedExpire, result);
        verify(redisTemplate).getExpire(key);
    }

    @Test
    @DisplayName("测试存储时异常处理")
    void testPutWithException() {
        // Arrange
        String key = "test:key";
        String value = "test value";
        doThrow(new RuntimeException("Redis连接失败")).when(redisTemplate.opsForValue()).set(key, value);

        // Act & Assert
        assertThrows(RuntimeException.class, () -> cacheManager.put(key, value));
    }

    @Test
    @DisplayName("测试获取时异常处理")
    void testGetWithException() {
        // Arrange
        String key = "test:key";
        when(redisTemplate.opsForValue().get(key)).thenThrow(new RuntimeException("Redis连接失败"));

        // Act
        Object result = cacheManager.get(key);

        // Assert
        assertNull(result);
    }
}