package com.yiyi.ai_train_playground.debug;

import com.yiyi.ai_train_playground.scheduler.JdImg2VecScheduler;
import com.yiyi.ai_train_playground.service.jd.impl.JdProductSyncServiceUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JdImg2VecScheduler 修复验证测试
 * 验证通过公共方法调用是否能解决 converterService 为 null 的问题
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-20
 */
@Slf4j
@SpringBootTest
public class JdImg2VecSchedulerFixTest {

    @Autowired
    private JdImg2VecScheduler jdImg2VecScheduler;

    @Autowired
    private JdProductSyncServiceUtil jdProductSyncService;

    @Test
    @DisplayName("验证 JdImg2VecScheduler 中的 JdProductSyncServiceImpl 注入是否正常")
    public void testJdProductSyncServiceInjection() {
        log.info("=== 验证 JdImg2VecScheduler 中的服务注入 ===");

        // 1. 检查 JdImg2VecScheduler 是否正确注入
        assertNotNull(jdImg2VecScheduler, "JdImg2VecScheduler 应该被正确注入");
        log.info("✅ JdImg2VecScheduler 注入成功");

        // 2. 使用反射获取 JdImg2VecScheduler 中的 jdProductSyncService 字段
        Object injectedService = ReflectionTestUtils.getField(jdImg2VecScheduler, "jdProductSyncService");
        assertNotNull(injectedService, "JdImg2VecScheduler 中的 jdProductSyncService 字段不应为 null");
        log.info("✅ JdImg2VecScheduler 中的 jdProductSyncService 字段注入成功");

        // 3. 检查注入的服务是否是同一个实例
        assertSame(jdProductSyncService, injectedService, "注入的服务应该是同一个 Spring Bean 实例");
        log.info("✅ 注入的服务是同一个 Spring Bean 实例");

        // 4. 检查注入的服务中的 converterService 字段
        Object converterService = ReflectionTestUtils.getField(injectedService, "converterService");
        assertNotNull(converterService, "JdProductSyncServiceImpl 中的 converterService 字段不应为 null");
        log.info("✅ JdProductSyncServiceImpl 中的 converterService 字段不为 null: {}", 
                converterService.getClass().getName());

        log.info("=== 服务注入验证完成 ===");
    }

    @Test
    @DisplayName("测试 convertImageToMarkdown 公共方法调用")
    public void testConvertImageToMarkdownMethod() {
        log.info("=== 测试 convertImageToMarkdown 公共方法调用 ===");

        String testImageUrl = "https://example.com/test-image.jpg";

        try {
            // 直接调用公共方法
            String result = jdProductSyncService.convertImageToMarkdown(testImageUrl);
            
            // 由于没有有效的提示词模板，预期会抛出异常
            log.info("⚠️ 意外成功：convertImageToMarkdown 调用成功，结果长度: {}", 
                    result != null ? result.length() : 0);
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("ConverterServiceByLLM 服务未正确注入")) {
                log.error("❌ converterService 仍然为 null，修复未生效");
                fail("converterService 仍然为 null，修复未生效: " + e.getMessage());
            } else if (e.getMessage().contains("未找到提示词模板") || 
                       e.getMessage().contains("ARK_API_KEY")) {
                log.info("✅ convertImageToMarkdown 方法调用成功，converterService 不为 null");
                log.info("⚠️ 预期的业务异常（缺少提示词模板或API密钥）: {}", e.getMessage());
            } else {
                log.error("❌ 意外的异常", e);
                throw e;
            }
        }

        log.info("=== convertImageToMarkdown 方法测试完成 ===");
    }

    @Test
    @DisplayName("测试 JdImg2VecScheduler 的 getImgMdFromService 方法")
    public void testGetImgMdFromServiceMethod() {
        log.info("=== 测试 JdImg2VecScheduler 的 getImgMdFromService 方法 ===");

        String testImageUrl = "https://example.com/test-image.jpg";

        try {
            // 使用反射调用私有方法 getImgMdFromService
            String result = (String) ReflectionTestUtils.invokeMethod(
                    jdImg2VecScheduler, "getImgMdFromService", testImageUrl);
            
            log.info("⚠️ 意外成功：getImgMdFromService 调用成功，结果长度: {}", 
                    result != null ? result.length() : 0);
            
        } catch (RuntimeException e) {
            if (e.getMessage().contains("JdProductSyncServiceImpl 服务未正确注入")) {
                log.error("❌ jdProductSyncService 为 null，注入失败");
                fail("jdProductSyncService 为 null，注入失败: " + e.getMessage());
            } else if (e.getMessage().contains("ConverterServiceByLLM 服务未正确注入")) {
                log.error("❌ converterService 仍然为 null，修复未生效");
                fail("converterService 仍然为 null，修复未生效: " + e.getMessage());
            } else if (e.getMessage().contains("未找到提示词模板") || 
                       e.getMessage().contains("ARK_API_KEY")) {
                log.info("✅ getImgMdFromService 方法调用成功，所有依赖都正确注入");
                log.info("⚠️ 预期的业务异常（缺少提示词模板或API密钥）: {}", e.getMessage());
            } else {
                log.error("❌ 意外的异常", e);
                throw e;
            }
        }

        log.info("=== getImgMdFromService 方法测试完成 ===");
    }
}
