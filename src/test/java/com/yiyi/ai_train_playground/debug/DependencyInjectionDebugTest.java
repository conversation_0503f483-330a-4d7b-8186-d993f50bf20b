package com.yiyi.ai_train_playground.debug;

import com.yiyi.ai_train_playground.service.BigmodelPromptsService;
import com.yiyi.ai_train_playground.service.ConverterServiceByLLM;
import com.yiyi.ai_train_playground.service.SuperBigModelInterface;
import com.yiyi.ai_train_playground.service.jd.impl.JdProductSyncServiceUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 依赖注入调试测试类
 * 用于诊断 JdProductSyncServiceImpl 中 converterService 为 null 的问题
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-20
 */
@Slf4j
@SpringBootTest
public class DependencyInjectionDebugTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    @DisplayName("检查所有相关 Bean 是否正确创建")
    public void testBeanCreation() {
        log.info("=== 开始检查 Bean 创建状态 ===");

        // 1. 检查 JdProductSyncServiceImpl
        try {
            JdProductSyncServiceUtil jdProductSyncService = applicationContext.getBean(JdProductSyncServiceUtil.class);
            assertNotNull(jdProductSyncService, "JdProductSyncServiceImpl Bean 应该存在");
            log.info("✅ JdProductSyncServiceImpl Bean 创建成功");

            // 使用反射检查 converterService 字段
            Object converterService = ReflectionTestUtils.getField(jdProductSyncService, "converterService");
            if (converterService == null) {
                log.error("❌ JdProductSyncServiceImpl.converterService 字段为 null");
            } else {
                log.info("✅ JdProductSyncServiceImpl.converterService 字段不为 null: {}", converterService.getClass().getName());
            }
        } catch (Exception e) {
            log.error("❌ JdProductSyncServiceImpl Bean 创建失败", e);
            fail("JdProductSyncServiceImpl Bean 创建失败: " + e.getMessage());
        }

        // 2. 检查 ConverterServiceByLLM
        try {
            ConverterServiceByLLM converterService = applicationContext.getBean(ConverterServiceByLLM.class);
            assertNotNull(converterService, "ConverterServiceByLLM Bean 应该存在");
            log.info("✅ ConverterServiceByLLM Bean 创建成功: {}", converterService.getClass().getName());
        } catch (Exception e) {
            log.error("❌ ConverterServiceByLLM Bean 创建失败", e);
        }

        // 3. 检查 SuperBigModelInterface
        try {
            SuperBigModelInterface bigModelService = applicationContext.getBean(SuperBigModelInterface.class);
            assertNotNull(bigModelService, "SuperBigModelInterface Bean 应该存在");
            log.info("✅ SuperBigModelInterface Bean 创建成功: {}", bigModelService.getClass().getName());
        } catch (Exception e) {
            log.error("❌ SuperBigModelInterface Bean 创建失败", e);
        }

        // 4. 检查 BigmodelPromptsService
        try {
            BigmodelPromptsService promptsService = applicationContext.getBean(BigmodelPromptsService.class);
            assertNotNull(promptsService, "BigmodelPromptsService Bean 应该存在");
            log.info("✅ BigmodelPromptsService Bean 创建成功: {}", promptsService.getClass().getName());
        } catch (Exception e) {
            log.error("❌ BigmodelPromptsService Bean 创建失败", e);
        }

        log.info("=== Bean 创建状态检查完成 ===");
    }

    @Test
    @DisplayName("检查环境变量和配置")
    public void testConfiguration() {
        log.info("=== 开始检查配置状态 ===");

        // 检查 ARK_API_KEY 环境变量
        String arkApiKey = System.getenv("ARK_API_KEY");
        if (arkApiKey == null || arkApiKey.trim().isEmpty()) {
            log.warn("⚠️ ARK_API_KEY 环境变量未设置或为空");
        } else {
            log.info("✅ ARK_API_KEY 环境变量已设置 (长度: {})", arkApiKey.length());
        }

        // 检查 Spring 配置属性
        try {
            String arkApiKeyFromProperty = applicationContext.getEnvironment().getProperty("ARK_API_KEY");
            if (arkApiKeyFromProperty == null || arkApiKeyFromProperty.trim().isEmpty()) {
                log.warn("⚠️ ARK_API_KEY 配置属性未设置或为空");
            } else {
                log.info("✅ ARK_API_KEY 配置属性已设置 (长度: {})", arkApiKeyFromProperty.length());
            }
        } catch (Exception e) {
            log.error("❌ 获取 ARK_API_KEY 配置属性失败", e);
        }

        log.info("=== 配置状态检查完成 ===");
    }

    @Test
    @DisplayName("测试 ConverterServiceByLLM 基本功能")
    public void testConverterServiceBasicFunction() {
        log.info("=== 开始测试 ConverterServiceByLLM 基本功能 ===");

        try {
            ConverterServiceByLLM converterService = applicationContext.getBean(ConverterServiceByLLM.class);
            assertNotNull(converterService, "ConverterServiceByLLM Bean 应该存在");

            // 尝试调用 convertText 方法（使用简单参数避免实际 API 调用）
            try {
                String result = converterService.convertText("test:SU", "", "test");
                log.info("✅ ConverterServiceByLLM.convertText 调用成功");
            } catch (RuntimeException e) {
                if (e.getMessage().contains("ARK_API_KEY") || e.getMessage().contains("未找到提示词模板")) {
                    log.info("⚠️ ConverterServiceByLLM.convertText 调用失败，但这是预期的（需要有效的 API 密钥或提示词模板）");
                } else {
                    log.error("❌ ConverterServiceByLLM.convertText 调用失败", e);
                }
            }

        } catch (Exception e) {
            log.error("❌ ConverterServiceByLLM Bean 获取失败", e);
        }

        log.info("=== ConverterServiceByLLM 基本功能测试完成 ===");
    }
}
