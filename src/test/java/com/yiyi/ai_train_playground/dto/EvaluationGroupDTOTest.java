package com.yiyi.ai_train_playground.dto;

import com.yiyi.ai_train_playground.entity.EvaluationGroup;
import org.junit.jupiter.api.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class EvaluationGroupDTOTest {

    @Test
    @Order(1)
    @DisplayName("测试isOfficial字段转换")
    public void testIsOfficialFieldConversion() {
        System.out.println("=== 测试1: isOfficial字段转换 ===");
        
        // 测试官方分组 (isOfficial = true -> 1)
        EvaluationGroup officialGroup = new EvaluationGroup();
        officialGroup.setId(1L);
        officialGroup.setGroupTitle("官方评价分组");
        officialGroup.setIsOfficial(true);
        
        EvaluationGroupDTO officialDTO = EvaluationGroupDTO.fromEntity(officialGroup);
        assertEquals(1, officialDTO.getIsOfficial(), "官方分组的isOfficial应该为1");
        
        // 测试用户分组 (isOfficial = false -> 0)
        EvaluationGroup userGroup = new EvaluationGroup();
        userGroup.setId(2L);
        userGroup.setGroupTitle("用户评价分组");
        userGroup.setIsOfficial(false);
        
        EvaluationGroupDTO userDTO = EvaluationGroupDTO.fromEntity(userGroup);
        assertEquals(0, userDTO.getIsOfficial(), "用户分组的isOfficial应该为0");
        
        // 测试null值 (isOfficial = null -> 0)
        EvaluationGroup nullGroup = new EvaluationGroup();
        nullGroup.setId(3L);
        nullGroup.setGroupTitle("null评价分组");
        nullGroup.setIsOfficial(null);
        
        EvaluationGroupDTO nullDTO = EvaluationGroupDTO.fromEntity(nullGroup);
        assertEquals(0, nullDTO.getIsOfficial(), "null分组的isOfficial应该为0");
        
        System.out.println("✅ isOfficial字段转换测试通过");
    }

    @Test
    @Order(2)
    @DisplayName("测试基本字段转换")
    public void testBasicFieldConversion() {
        System.out.println("=== 测试2: 基本字段转换 ===");
        
        // 创建测试实体
        EvaluationGroup entity = createTestEvaluationGroup();
        
        // 转换为DTO
        EvaluationGroupDTO dto = EvaluationGroupDTO.fromEntity(entity);
        
        // 验证基本字段
        assertNotNull(dto, "DTO不应为null");
        assertEquals(entity.getId(), dto.getId(), "ID应该一致");
        assertEquals(entity.getGroupTitle(), dto.getGroupTitle(), "分组标题应该一致");
        assertEquals(entity.getTeamId(), dto.getTeamId(), "团队ID应该一致");
        assertEquals(entity.getParentId(), dto.getParentId(), "父级ID应该一致");
        assertEquals(entity.getSortOrder(), dto.getSortOrder(), "排序应该一致");
        assertEquals(entity.getCreateTime(), dto.getCreateTime(), "创建时间应该一致");
        assertEquals(entity.getUpdateTime(), dto.getUpdateTime(), "更新时间应该一致");
        assertEquals(entity.getCreator(), dto.getCreator(), "创建者应该一致");
        assertEquals(entity.getUpdater(), dto.getUpdater(), "更新者应该一致");
        assertEquals(entity.getVersion(), dto.getVersion(), "版本应该一致");
        assertEquals(entity.getFullPath(), dto.getFullPath(), "完整路径应该一致");
        
        System.out.println("✅ 基本字段转换测试通过");
    }

    @Test
    @Order(3)
    @DisplayName("测试null实体转换")
    public void testNullEntityConversion() {
        System.out.println("=== 测试3: null实体转换 ===");
        
        // 测试null实体
        EvaluationGroupDTO dto = EvaluationGroupDTO.fromEntity(null);
        assertNull(dto, "null实体应该返回null DTO");
        
        // 测试带processedIds的null实体
        Set<Long> processedIds = new HashSet<>();
        EvaluationGroupDTO dtoWithIds = EvaluationGroupDTO.fromEntity(null, processedIds);
        assertNull(dtoWithIds, "null实体应该返回null DTO");
        
        System.out.println("✅ null实体转换测试通过");
    }

    @Test
    @Order(4)
    @DisplayName("测试循环引用处理")
    public void testCircularReferenceHandling() {
        System.out.println("=== 测试4: 循环引用处理 ===");
        
        // 创建父分组
        EvaluationGroup parentGroup = new EvaluationGroup();
        parentGroup.setId(1L);
        parentGroup.setGroupTitle("父级评价分组");
        parentGroup.setTeamId(123L);
        parentGroup.setIsOfficial(false);
        parentGroup.setSubGroups(new ArrayList<>());
        
        // 创建子分组
        EvaluationGroup childGroup = new EvaluationGroup();
        childGroup.setId(2L);
        childGroup.setGroupTitle("子级评价分组");
        childGroup.setTeamId(123L);
        childGroup.setParentId(1L);
        childGroup.setIsOfficial(false);
        childGroup.setSubGroups(new ArrayList<>());
        
        // 建立父子关系
        parentGroup.getSubGroups().add(childGroup);
        
        // 测试转换（不应该出现无限循环）
        EvaluationGroupDTO parentDTO = EvaluationGroupDTO.fromEntity(parentGroup);
        
        assertNotNull(parentDTO, "父分组DTO不应为null");
        assertEquals(1L, parentDTO.getId(), "父分组ID应该正确");
        assertNotNull(parentDTO.getSubGroups(), "子分组列表不应为null");
        assertEquals(1, parentDTO.getSubGroups().size(), "应该有1个子分组");
        
        EvaluationGroupDTO childDTO = parentDTO.getSubGroups().get(0);
        assertEquals(2L, childDTO.getId(), "子分组ID应该正确");
        assertEquals("子级评价分组", childDTO.getGroupTitle(), "子分组标题应该正确");
        
        System.out.println("✅ 循环引用处理测试通过");
    }

    @Test
    @Order(5)
    @DisplayName("测试重复ID处理")
    public void testDuplicateIdHandling() {
        System.out.println("=== 测试5: 重复ID处理 ===");
        
        // 创建已处理ID集合
        Set<Long> processedIds = new HashSet<>();
        processedIds.add(1L);
        
        // 创建实体（ID已在processedIds中）
        EvaluationGroup entity = new EvaluationGroup();
        entity.setId(1L);
        entity.setGroupTitle("重复ID分组");
        
        // 转换应该返回null（因为ID已处理过）
        EvaluationGroupDTO dto = EvaluationGroupDTO.fromEntity(entity, processedIds);
        assertNull(dto, "重复ID的实体应该返回null");
        
        System.out.println("✅ 重复ID处理测试通过");
    }

    @Test
    @Order(6)
    @DisplayName("测试子分组转换")
    public void testSubGroupsConversion() {
        System.out.println("=== 测试6: 子分组转换 ===");
        
        // 创建父分组
        EvaluationGroup parentGroup = new EvaluationGroup();
        parentGroup.setId(1L);
        parentGroup.setGroupTitle("父级评价分组");
        parentGroup.setTeamId(123L);
        parentGroup.setIsOfficial(false);
        parentGroup.setSubGroups(new ArrayList<>());
        
        // 创建多个子分组
        for (int i = 1; i <= 3; i++) {
            EvaluationGroup childGroup = new EvaluationGroup();
            childGroup.setId((long) (i + 1));
            childGroup.setGroupTitle("子级评价分组" + i);
            childGroup.setTeamId(123L);
            childGroup.setParentId(1L);
            childGroup.setIsOfficial(false);
            parentGroup.getSubGroups().add(childGroup);
        }
        
        // 转换为DTO
        EvaluationGroupDTO parentDTO = EvaluationGroupDTO.fromEntity(parentGroup);
        
        // 验证子分组转换
        assertNotNull(parentDTO.getSubGroups(), "子分组列表不应为null");
        assertEquals(3, parentDTO.getSubGroups().size(), "应该有3个子分组");
        
        for (int i = 0; i < 3; i++) {
            EvaluationGroupDTO childDTO = parentDTO.getSubGroups().get(i);
            assertNotNull(childDTO, "子分组DTO不应为null");
            assertEquals("子级评价分组" + (i + 1), childDTO.getGroupTitle(), "子分组标题应该正确");
            assertEquals(1L, childDTO.getParentId(), "子分组父ID应该正确");
        }
        
        System.out.println("✅ 子分组转换测试通过");
    }

    @Test
    @Order(7)
    @DisplayName("测试空子分组列表")
    public void testEmptySubGroupsList() {
        System.out.println("=== 测试7: 空子分组列表 ===");
        
        // 创建没有子分组的实体
        EvaluationGroup entity = createTestEvaluationGroup();
        entity.setSubGroups(new ArrayList<>()); // 空列表
        
        EvaluationGroupDTO dto = EvaluationGroupDTO.fromEntity(entity);
        
        assertNotNull(dto.getSubGroups(), "子分组列表不应为null");
        assertTrue(dto.getSubGroups().isEmpty(), "子分组列表应该为空");
        
        // 测试null子分组列表
        entity.setSubGroups(null);
        dto = EvaluationGroupDTO.fromEntity(entity);
        
        assertNotNull(dto.getSubGroups(), "子分组列表不应为null（即使原始为null）");
        assertTrue(dto.getSubGroups().isEmpty(), "子分组列表应该为空");
        
        System.out.println("✅ 空子分组列表测试通过");
    }

    @Test
    @Order(8)
    @DisplayName("测试深层嵌套转换")
    public void testDeepNestedConversion() {
        System.out.println("=== 测试8: 深层嵌套转换 ===");
        
        // 创建三层嵌套结构
        EvaluationGroup grandParent = new EvaluationGroup();
        grandParent.setId(1L);
        grandParent.setGroupTitle("祖父级分组");
        grandParent.setSubGroups(new ArrayList<>());
        
        EvaluationGroup parent = new EvaluationGroup();
        parent.setId(2L);
        parent.setGroupTitle("父级分组");
        parent.setParentId(1L);
        parent.setSubGroups(new ArrayList<>());
        
        EvaluationGroup child = new EvaluationGroup();
        child.setId(3L);
        child.setGroupTitle("子级分组");
        child.setParentId(2L);
        child.setSubGroups(new ArrayList<>());
        
        // 建立嵌套关系
        parent.getSubGroups().add(child);
        grandParent.getSubGroups().add(parent);
        
        // 转换为DTO
        EvaluationGroupDTO grandParentDTO = EvaluationGroupDTO.fromEntity(grandParent);
        
        // 验证三层结构
        assertNotNull(grandParentDTO, "祖父级DTO不应为null");
        assertEquals(1, grandParentDTO.getSubGroups().size(), "祖父级应该有1个子分组");
        
        EvaluationGroupDTO parentDTO = grandParentDTO.getSubGroups().get(0);
        assertEquals("父级分组", parentDTO.getGroupTitle(), "父级分组标题应该正确");
        assertEquals(1, parentDTO.getSubGroups().size(), "父级应该有1个子分组");
        
        EvaluationGroupDTO childDTO = parentDTO.getSubGroups().get(0);
        assertEquals("子级分组", childDTO.getGroupTitle(), "子级分组标题应该正确");
        assertEquals(0, childDTO.getSubGroups().size(), "子级应该没有子分组");
        
        System.out.println("✅ 深层嵌套转换测试通过");
    }

    // 辅助方法：创建测试用的评价分组实体
    private EvaluationGroup createTestEvaluationGroup() {
        EvaluationGroup entity = new EvaluationGroup();
        entity.setId(1L);
        entity.setGroupTitle("测试评价分组");
        entity.setTeamId(123L);
        entity.setParentId(null);
        entity.setIsOfficial(false);
        entity.setSortOrder(0);
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setCreator("test-user");
        entity.setUpdater("test-user");
        entity.setVersion(0L);
        entity.setFullPath("测试评价分组");
        return entity;
    }
}
