package com.yiyi.ai_train_playground.config;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 测试环境JdConfig配置验证
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
class JdConfigTestEnvironmentTest {
    
    @Autowired
    private JdConfig jdConfig;
    
    @Test
    @DisplayName("验证测试环境模拟数据开关默认为true")
    void testMockSwitchInTestEnvironment() {
        // 验证配置是否正确加载
        assertThat(jdConfig).isNotNull();
        assertThat(jdConfig.getSync()).isNotNull();
        
        // 验证测试环境下模拟数据开关为true
        Boolean isMockSwitch = jdConfig.getSync().getIsMockSwitch();
        assertThat(isMockSwitch).isNotNull();
        assertThat(isMockSwitch).isTrue();
        
        log.info("测试环境模拟数据开关验证通过: {}", isMockSwitch);
        log.info("测试环境将使用本地模拟数据而不是远程京东API");
    }
    
    @Test
    @DisplayName("验证测试环境其他京东配置")
    void testOtherJdConfigInTestEnvironment() {
        // 验证其他配置
        assertThat(jdConfig.getSync().getPageSize()).isEqualTo(10);
        
        // 验证Token刷新配置（测试环境特殊配置）
        assertThat(jdConfig.getTokenRefresh()).isNotNull();
        assertThat(jdConfig.getTokenRefresh().getIntervalMinutes()).isEqualTo(1);
        assertThat(jdConfig.getTokenRefresh().getLockExpireMinutes()).isEqualTo(1);
        
        log.info("测试环境京东配置验证通过:");
        log.info("  - 分页大小: {}", jdConfig.getSync().getPageSize());
        log.info("  - 模拟数据开关: {}", jdConfig.getSync().getIsMockSwitch());
        log.info("  - Token刷新间隔: {}分钟", jdConfig.getTokenRefresh().getIntervalMinutes());
        log.info("  - 锁过期时间: {}分钟", jdConfig.getTokenRefresh().getLockExpireMinutes());
    }
}
