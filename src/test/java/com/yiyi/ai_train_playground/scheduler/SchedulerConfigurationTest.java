package com.yiyi.ai_train_playground.scheduler;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 定时任务配置验证测试
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@SpringBootTest
@TestPropertySource(properties = {
    "spring.task.scheduling.enabled=true",
    "jd.token-refresh.interval-minutes=60",
    "jd.token-refresh.lock-expire-minutes=50"
})
public class SchedulerConfigurationTest {

    @Value("${jd.token-refresh.interval-minutes:60}")
    private int intervalMinutes;

    @Value("${jd.token-refresh.lock-expire-minutes:50}")
    private int lockExpireMinutes;

    @Test
    @DisplayName("验证定时任务配置参数")
    void testSchedulerConfiguration() {
        System.out.println("=== 验证定时任务配置参数 ===");
        
        // 验证间隔时间配置
        assertTrue(intervalMinutes > 0, "Token刷新间隔时间必须大于0");
        assertTrue(intervalMinutes <= 1440, "Token刷新间隔时间不应超过24小时(1440分钟)");
        
        // 验证锁过期时间配置
        assertTrue(lockExpireMinutes > 0, "分布式锁过期时间必须大于0");
        assertTrue(lockExpireMinutes < intervalMinutes, "锁过期时间应小于刷新间隔时间");
        
        // 验证计算后的毫秒值不会溢出
        long intervalMillis = (long) intervalMinutes * 60 * 1000;
        assertTrue(intervalMillis > 0, "计算后的毫秒值必须为正数");
        assertTrue(intervalMillis <= Integer.MAX_VALUE, "计算后的毫秒值不应超过Integer.MAX_VALUE");
        
        System.out.println("Token刷新间隔: " + intervalMinutes + " 分钟 (" + intervalMillis + " 毫秒)");
        System.out.println("分布式锁过期时间: " + lockExpireMinutes + " 分钟");
        System.out.println("✅ 定时任务配置参数验证通过");
    }

    @Test
    @DisplayName("验证SpEL表达式计算")
    void testSpELExpression() {
        System.out.println("=== 验证SpEL表达式计算 ===");
        
        // 模拟@Scheduled注解中的SpEL表达式计算
        long calculatedInterval = (long) intervalMinutes * 60 * 1000;
        
        // 验证计算结果
        assertNotNull(calculatedInterval, "SpEL表达式计算结果不应为null");
        assertTrue(calculatedInterval > 0, "SpEL表达式计算结果应为正数");
        
        // 验证不会导致ScheduledThreadPoolExecutor异常
        assertDoesNotThrow(() -> {
            if (calculatedInterval <= 0) {
                throw new IllegalArgumentException("Period must be positive");
            }
        }, "SpEL表达式计算结果不应导致IllegalArgumentException");
        
        System.out.println("SpEL表达式: #{${jd.token-refresh.interval-minutes:60} * 60 * 1000}");
        System.out.println("计算结果: " + calculatedInterval + " 毫秒");
        System.out.println("✅ SpEL表达式计算验证通过");
    }
}
