package com.yiyi.ai_train_playground.scheduler;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 京东Token刷新定时任务测试
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@SpringBootTest
@TestPropertySource(properties = {
    "spring.task.scheduling.enabled=false" // 测试环境禁用定时任务
})
public class JdTokenRefreshSchedulerTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    @DisplayName("测试定时任务开关功能 - 禁用状态")
    void testSchedulerDisabled() {
        System.out.println("=== 测试定时任务开关功能 - 禁用状态 ===");

        // 验证定时任务Bean不应该被创建（因为@ConditionalOnProperty条件不满足）
        boolean schedulerExists = applicationContext.containsBean("jdTokenRefreshScheduler");

        assertFalse(schedulerExists, "当spring.task.scheduling.enabled=false时，定时任务Bean不应该被创建");

        System.out.println("✅ 定时任务已被正确禁用");
    }

    @Test
    @DisplayName("验证测试环境配置")
    void testTestConfiguration() {
        System.out.println("=== 验证测试环境配置 ===");

        // 验证其他必要的Bean仍然存在（不使用MockBean，检查真实Bean）
        assertTrue(applicationContext.containsBean("jdTokenRefreshServiceImpl"),
                  "JdTokenRefreshServiceImpl Bean应该存在");
        assertTrue(applicationContext.containsBean("redisDistributedLockService"),
                  "RedisDistributedLockService Bean应该存在");
        assertTrue(applicationContext.containsBean("jdConfig"),
                  "JdConfig Bean应该存在");

        System.out.println("✅ 测试环境配置验证通过");
    }
}
