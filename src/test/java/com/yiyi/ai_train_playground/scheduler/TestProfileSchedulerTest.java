package com.yiyi.ai_train_playground.scheduler;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试profile下定时任务禁用验证
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")  // 使用test profile
public class TestProfileSchedulerTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    @DisplayName("验证test profile下定时任务被禁用")
    void testSchedulerDisabledInTestProfile() {
        System.out.println("=== 验证test profile下定时任务被禁用 ===");
        
        // 验证定时任务Bean不应该被创建
        boolean schedulerExists = applicationContext.containsBean("jdTokenRefreshScheduler");
        
        assertFalse(schedulerExists, "在test profile下，定时任务Bean不应该被创建");
        
        System.out.println("✅ test profile下定时任务已被正确禁用");
    }

    @Test
    @DisplayName("验证其他Bean仍然正常创建")
    void testOtherBeansStillExist() {
        System.out.println("=== 验证其他Bean仍然正常创建 ===");
        
        // 验证其他必要的Bean仍然存在
        assertTrue(applicationContext.containsBean("jdTokenRefreshServiceImpl"), 
                  "JdTokenRefreshServiceImpl Bean应该存在");
        assertTrue(applicationContext.containsBean("redisDistributedLockService"), 
                  "RedisDistributedLockService Bean应该存在");
        assertTrue(applicationContext.containsBean("jdConfig"), 
                  "JdConfig Bean应该存在");
        
        System.out.println("✅ 其他Bean正常创建验证通过");
    }
}
