package com.yiyi.ai_train_playground.scheduler;

import com.yiyi.ai_train_playground.config.JdConfig;
import com.yiyi.ai_train_playground.service.RedisDistributedLockService;
import com.yiyi.ai_train_playground.service.jd.JdTokenRefreshService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 京东Token刷新定时任务启用状态测试
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@SpringBootTest
@TestPropertySource(properties = {
    "spring.task.scheduling.enabled=true", // 启用定时任务
    "jd.token-refresh.interval-minutes=999999" // 设置很长的间隔避免测试期间执行
})
public class JdTokenRefreshSchedulerEnabledTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired(required = false)
    private JdTokenRefreshScheduler jdTokenRefreshScheduler;

    @MockBean
    private JdTokenRefreshService jdTokenRefreshService;

    @MockBean
    private RedisDistributedLockService redisDistributedLockService;

    @MockBean
    private JdConfig jdConfig;

    @Test
    @DisplayName("测试定时任务开关功能 - 启用状态")
    void testSchedulerEnabled() {
        System.out.println("=== 测试定时任务开关功能 - 启用状态 ===");
        
        // 验证定时任务Bean应该被创建
        boolean schedulerExists = applicationContext.containsBean("jdTokenRefreshScheduler");
        
        assertTrue(schedulerExists, "当spring.task.scheduling.enabled=true时，定时任务Bean应该被创建");
        assertNotNull(jdTokenRefreshScheduler, "JdTokenRefreshScheduler应该被注入");
        
        System.out.println("✅ 定时任务已被正确启用");
    }

    @Test
    @DisplayName("测试定时任务手动执行")
    void testManualExecution() {
        System.out.println("=== 测试定时任务手动执行 ===");
        
        // 模拟配置
        JdConfig.TokenRefresh tokenRefresh = new JdConfig.TokenRefresh();
        tokenRefresh.setLockExpireMinutes(1);
        when(jdConfig.getTokenRefresh()).thenReturn(tokenRefresh);
        
        // 模拟分布式锁获取失败（避免实际执行业务逻辑）
        when(redisDistributedLockService.tryLock(anyString(), anyString(), anyLong()))
                .thenReturn(false);
        
        // 手动调用定时任务方法
        assertDoesNotThrow(() -> {
            jdTokenRefreshScheduler.refreshJdTokens();
        }, "定时任务方法执行不应该抛出异常");
        
        // 验证分布式锁被调用
        verify(redisDistributedLockService, times(1))
                .tryLock(eq("jd:token:refresh:lock"), anyString(), anyLong());
        
        // 验证由于锁获取失败，业务逻辑不会被执行
        verify(jdTokenRefreshService, never()).refreshAllTokens();
        
        System.out.println("✅ 定时任务手动执行测试通过");
    }
}
