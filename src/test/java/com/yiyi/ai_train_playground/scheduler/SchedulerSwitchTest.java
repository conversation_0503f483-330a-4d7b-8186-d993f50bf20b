package com.yiyi.ai_train_playground.scheduler;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 定时任务开关功能测试 - 禁用状态
 *
 * <AUTHOR> Assistant
 * @since 2025-01-17
 */
@Slf4j
@SpringBootTest
@TestPropertySource(properties = {
    "spring.task.scheduling.enabled=false"
})
public class SchedulerSwitchTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    @DisplayName("测试定时任务禁用")
    void testSchedulerDisabled() {
        System.out.println("=== 测试定时任务禁用 ===");

        // 验证定时任务Bean不应该被创建
        boolean schedulerExists = applicationContext.containsBean("jdTokenRefreshScheduler");

        assertFalse(schedulerExists, "当spring.task.scheduling.enabled=false时，定时任务Bean不应该被创建");

        System.out.println("✅ 定时任务已被正确禁用");
    }
}
