package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.dto.ScriptCreateRequest;
import com.yiyi.ai_train_playground.service.TrainScriptService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 剧本批量删除集成测试
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-03
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TrainScriptBatchDeleteIntegrationTest {

    @Autowired
    private TrainScriptController trainScriptController;

    @Autowired
    private TrainScriptService trainScriptService;

    @Autowired
    private JwtUtil jwtUtil;

    private static final Long TEST_TEAM_ID = 1L;
    private static final String TEST_CREATOR = "test_user";

    @Test
    public void testBatchDeleteIntegration() {
        // 1. 创建测试剧本
        ScriptCreateRequest request1 = createTestScript("集成测试剧本1");
        ScriptCreateRequest request2 = createTestScript("集成测试剧本2");

        Long scriptId1 = trainScriptService.createScriptWithRelatedData(request1, TEST_TEAM_ID, TEST_CREATOR);
        Long scriptId2 = trainScriptService.createScriptWithRelatedData(request2, TEST_TEAM_ID, TEST_CREATOR);

        assertNotNull(scriptId1);
        assertNotNull(scriptId2);
        log.info("创建测试剧本成功：scriptId1={}, scriptId2={}", scriptId1, scriptId2);

        // 2. 验证批量删除服务层功能
        String ids = scriptId1 + "," + scriptId2;
        boolean deleteResult = trainScriptService.batchDeleteScripts(ids, TEST_TEAM_ID);
        assertTrue(deleteResult);
        log.info("批量删除服务层测试通过");

        // 3. 验证删除后的状态
        // 注意：由于使用了@Transactional，测试结束后会回滚，所以这里主要验证逻辑正确性
        log.info("批量删除集成测试完成");
    }

    @Test
    public void testBatchDeleteValidation() {
        // 测试各种异常情况
        
        // 1. 空ID列表
        RuntimeException exception1 = assertThrows(RuntimeException.class, () -> {
            trainScriptService.batchDeleteScripts("", TEST_TEAM_ID);
        });
        assertEquals("剧本ID列表不能为空", exception1.getMessage());

        // 2. 无效ID格式
        RuntimeException exception2 = assertThrows(RuntimeException.class, () -> {
            trainScriptService.batchDeleteScripts("1,abc,3", TEST_TEAM_ID);
        });
        assertTrue(exception2.getMessage().contains("剧本ID格式不正确"));

        // 3. 空团队ID
        RuntimeException exception3 = assertThrows(RuntimeException.class, () -> {
            trainScriptService.batchDeleteScripts("1,2,3", null);
        });
        assertEquals("团队ID不能为空", exception3.getMessage());

        log.info("批量删除验证测试通过");
    }

    /**
     * 创建测试剧本请求
     */
    private ScriptCreateRequest createTestScript(String name) {
        ScriptCreateRequest request = new ScriptCreateRequest();
        request.setName(name);
        request.setGenerationTypeCode(0);
        request.setGroupId(13L);
        request.setIntentId(5L);
        request.setEvaluationPlanId(2001L);
        request.setBuyerRequirement("集成测试需求");
        request.setOrderPriority(3);
        request.setSimulationTool("Simulator_V3");
        return request;
    }
}
