package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.controller.jd.JdProductController;
import com.yiyi.ai_train_playground.dto.JdPrdListRequest;
import com.yiyi.ai_train_playground.dto.JdPrdListResponse;
import com.yiyi.ai_train_playground.entity.TrainTeamShops;
import com.yiyi.ai_train_playground.mapper.TrainTeamShopsMapper;
import com.yiyi.ai_train_playground.service.jd.TrainJdProductsService;
import com.yiyi.ai_train_playground.util.SecurityUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * JdProductController增强功能测试
 * 测试新增的店铺授权状态和同步状态功能
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-16
 */
@SpringBootTest
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@Transactional
public class JdProductControllerEnhancementTest {

    @Autowired
    private JdProductController jdProductController;

    @MockBean
    private TrainJdProductsService trainJdProductsService;

    @Autowired
    private TrainTeamShopsMapper trainTeamShopsMapper;

    @Test
    public void testGetJdProductListWithShopStatus() {
        // 准备测试数据
        Long testTeamId = 1L;
        Long testShopId = 12345L;

        // 创建店铺记录
        TrainTeamShops teamShop = new TrainTeamShops();
        teamShop.setTeamId(testTeamId);
        teamShop.setShopId(testShopId);
        teamShop.setShopType(0); // 京东
        teamShop.setCreator("test_user");
        teamShop.setUpdater("test_user");
        teamShop.setIsAuthorize(true); // 已授权
        teamShop.setIsSyncComplete(1); // 同步中
        trainTeamShopsMapper.insert(teamShop);

        // 模拟服务层返回的响应
        JdPrdListResponse mockResponse = new JdPrdListResponse();
        mockResponse.setTotal(1L);
        mockResponse.setShopId(testShopId);
        mockResponse.setIsAuthorize(true); // 设置授权状态
        mockResponse.setIsSyncComplete(1); // 设置同步状态码
        mockResponse.setIsSyncCompleteDesc("同步中"); // 设置同步状态描述

        JdPrdListResponse.JdProductItem item = new JdPrdListResponse.JdProductItem();
        item.setId(1L);
        item.setBrandId(1L);
        item.setWareId(12345L);
        item.setBrandName("小米");
        item.setSkuId(12345L);
        item.setLogo("https://img11.360buyimg.com/devfe/jfs/t1/137620/25/33301/62910/63ef1f7eFeba5d9e1/19c5a79dee1137be.jpg");
        item.setTitle("数据分析初级版试用");
        item.setStatus("上架中");
        item.setOnlineTime(LocalDateTime.of(2019, 1, 1, 1, 1, 1));
        item.setOffLineTime(LocalDateTime.of(2019, 1, 1, 1, 1, 1));
        
        mockResponse.setRows(Arrays.asList(item));

        // Mock服务层调用
        when(trainJdProductsService.findJdProductList(eq(testTeamId), any(JdPrdListRequest.class)))
                .thenReturn(mockResponse);

        // 使用MockedStatic模拟SecurityUtil
        try (MockedStatic<SecurityUtil> mockedSecurityUtil = mockStatic(SecurityUtil.class)) {
            mockedSecurityUtil.when(SecurityUtil::getCurrentTeamId).thenReturn(testTeamId);

            // 执行测试
            var result = jdProductController.getJdProductList("小米", 1, 10);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getCode()); // code=1表示成功
            
            JdPrdListResponse response = result.getData();
            assertNotNull(response);
            assertEquals(1L, response.getTotal());
            assertEquals(testShopId, response.getShopId());
            
            // 验证新增的字段
            assertTrue(response.getIsAuthorize()); // 应该是true
            assertEquals(1, response.getIsSyncComplete()); // 应该是1（同步中）
            assertEquals("同步中", response.getIsSyncCompleteDesc()); // 应该是"同步中"
            
            assertNotNull(response.getRows());
            assertEquals(1, response.getRows().size());
        }
    }

    @Test
    public void testGetJdProductListWithUnauthorizedShop() {
        // 准备测试数据
        Long testTeamId = 1L;
        Long testShopId = 54321L;

        // 创建未授权的店铺记录
        TrainTeamShops teamShop = new TrainTeamShops();
        teamShop.setTeamId(testTeamId);
        teamShop.setShopId(testShopId);
        teamShop.setShopType(0); // 京东
        teamShop.setCreator("test_user");
        teamShop.setUpdater("test_user");
        teamShop.setIsAuthorize(false); // 未授权
        teamShop.setIsSyncComplete(0); // 未同步
        trainTeamShopsMapper.insert(teamShop);

        // 模拟服务层返回的响应
        JdPrdListResponse mockResponse = new JdPrdListResponse();
        mockResponse.setTotal(0L);
        mockResponse.setShopId(testShopId);
        mockResponse.setIsAuthorize(false); // 设置未授权状态
        mockResponse.setIsSyncComplete(0); // 设置未同步状态码
        mockResponse.setIsSyncCompleteDesc("未同步"); // 设置未同步状态描述
        mockResponse.setRows(Arrays.asList());

        // Mock服务层调用
        when(trainJdProductsService.findJdProductList(eq(testTeamId), any(JdPrdListRequest.class)))
                .thenReturn(mockResponse);

        // 使用MockedStatic模拟SecurityUtil
        try (MockedStatic<SecurityUtil> mockedSecurityUtil = mockStatic(SecurityUtil.class)) {
            mockedSecurityUtil.when(SecurityUtil::getCurrentTeamId).thenReturn(testTeamId);

            // 执行测试
            var result = jdProductController.getJdProductList(null, 1, 10);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getCode()); // code=1表示成功
            
            JdPrdListResponse response = result.getData();
            assertNotNull(response);
            assertEquals(0L, response.getTotal());
            assertEquals(testShopId, response.getShopId());
            
            // 验证新增的字段
            assertFalse(response.getIsAuthorize()); // 应该是false
            assertEquals(0, response.getIsSyncComplete()); // 应该是0（未同步）
            assertEquals("未同步", response.getIsSyncCompleteDesc()); // 应该是"未同步"
        }
    }

    @Test
    public void testGetJdProductListWithNoShopData() {
        // 准备测试数据
        Long testTeamId = 1L;

        // 模拟服务层返回空列表的响应
        JdPrdListResponse mockResponse = new JdPrdListResponse();
        mockResponse.setTotal(0L);
        mockResponse.setShopId(null); // 没有shopId
        mockResponse.setRows(Arrays.asList());

        // Mock服务层调用
        when(trainJdProductsService.findJdProductList(eq(testTeamId), any(JdPrdListRequest.class)))
                .thenReturn(mockResponse);

        // 使用MockedStatic模拟SecurityUtil
        try (MockedStatic<SecurityUtil> mockedSecurityUtil = mockStatic(SecurityUtil.class)) {
            mockedSecurityUtil.when(SecurityUtil::getCurrentTeamId).thenReturn(testTeamId);

            // 执行测试
            var result = jdProductController.getJdProductList(null, 1, 10);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.getCode()); // code=1表示成功
            
            JdPrdListResponse response = result.getData();
            assertNotNull(response);
            assertEquals(0L, response.getTotal());
            assertNull(response.getShopId());
            
            // 当没有shopId时，状态字段应该为null（因为没有调用setShopStatusInfo）
            assertNull(response.getIsAuthorize());
            assertNull(response.getIsSyncComplete());
            assertNull(response.getIsSyncCompleteDesc());
        }
    }
}
