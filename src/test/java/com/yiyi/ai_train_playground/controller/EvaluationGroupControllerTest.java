package com.yiyi.ai_train_playground.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.entity.EvaluationGroup;
import com.yiyi.ai_train_playground.service.EvaluationGroupService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.impl.DefaultClaims;
import org.junit.jupiter.api.*;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@TestPropertySource(properties = {
    "my.doubao.normal.endpoint.name=ep-20250629195408-gtv9c"
})
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class EvaluationGroupControllerTest {

    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext webApplicationContext;

    @MockBean
    private EvaluationGroupService evaluationGroupService;

    @MockBean
    private JwtUtil jwtUtil;

    @Autowired
    private ObjectMapper objectMapper;

    private static final String TEST_TOKEN = "Bearer test-jwt-token";
    private static final Long TEST_TEAM_ID = 123L;
    private static final String TEST_USER_ID = "test-user";

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 模拟JWT解析
        Claims claims = new DefaultClaims();
        claims.put("teamId", TEST_TEAM_ID);
        claims.put("userId", TEST_USER_ID);
        when(jwtUtil.parseToken("test-jwt-token")).thenReturn(claims);
    }

    @Test
    @Order(1)
    @DisplayName("测试获取评价分组树 - 成功")
    void testGetEvaluationGroups_Success() throws Exception {
        System.out.println("=== 测试1: 获取评价分组树 - 成功 ===");

        // 准备测试数据
        Map<String, Object> mockGroupTree = createMockGroupTree();
        when(evaluationGroupService.getGroupTree(anyString(), eq(TEST_TEAM_ID)))
                .thenReturn(mockGroupTree);

        // 执行请求
        mockMvc.perform(get("/api/evaluation-groups")
                        .param("groupTitle", "测试分组")
                        .header("Authorization", TEST_TOKEN))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.data.groups").isArray())
                .andExpect(jsonPath("$.data.groups[0].groupTitle").value("全部评价"));

        // 验证服务调用
        verify(evaluationGroupService, times(1)).getGroupTree("测试分组", TEST_TEAM_ID);
        verify(jwtUtil, times(1)).parseToken("test-jwt-token");

        System.out.println("✅ 获取评价分组树测试通过");
    }

    @Test
    @Order(2)
    @DisplayName("测试获取评价分组树 - 无查询条件")
    void testGetEvaluationGroups_NoFilter() throws Exception {
        System.out.println("=== 测试2: 获取评价分组树 - 无查询条件 ===");

        // 准备测试数据
        Map<String, Object> mockGroupTree = createMockGroupTree();
        when(evaluationGroupService.getGroupTree(isNull(), eq(TEST_TEAM_ID)))
                .thenReturn(mockGroupTree);

        // 执行请求（不传groupTitle参数）
        mockMvc.perform(get("/api/evaluation-groups")
                        .header("Authorization", TEST_TOKEN))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1));

        // 验证服务调用
        verify(evaluationGroupService, times(1)).getGroupTree(null, TEST_TEAM_ID);

        System.out.println("✅ 无查询条件测试通过");
    }

    @Test
    @Order(3)
    @DisplayName("测试新增评价分组 - 成功")
    void testSaveEvaluationGroup_Success() throws Exception {
        System.out.println("=== 测试3: 新增评价分组 - 成功 ===");

        // 准备测试数据
        EvaluationGroup evaluationGroup = createTestEvaluationGroup();
        when(evaluationGroupService.save(any(EvaluationGroup.class))).thenReturn(true);

        // 执行请求
        mockMvc.perform(post("/api/evaluation-groups")
                        .header("Authorization", TEST_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(evaluationGroup)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1));

        // 验证服务调用和参数
        ArgumentCaptor<EvaluationGroup> captor = ArgumentCaptor.forClass(EvaluationGroup.class);
        verify(evaluationGroupService, times(1)).save(captor.capture());
        
        EvaluationGroup savedGroup = captor.getValue();
        assertEquals(TEST_TEAM_ID, savedGroup.getTeamId());
        assertEquals("测试评价分组", savedGroup.getGroupTitle());

        System.out.println("✅ 新增评价分组测试通过");
    }

    @Test
    @Order(4)
    @DisplayName("测试新增评价分组 - 失败")
    void testSaveEvaluationGroup_Failure() throws Exception {
        System.out.println("=== 测试4: 新增评价分组 - 失败 ===");

        // 准备测试数据
        EvaluationGroup evaluationGroup = createTestEvaluationGroup();
        when(evaluationGroupService.save(any(EvaluationGroup.class))).thenReturn(false);

        // 执行请求
        mockMvc.perform(post("/api/evaluation-groups")
                        .header("Authorization", TEST_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(evaluationGroup)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("添加失败"));

        System.out.println("✅ 新增评价分组失败测试通过");
    }

    @Test
    @Order(5)
    @DisplayName("测试更新评价分组 - 成功")
    void testUpdateEvaluationGroup_Success() throws Exception {
        System.out.println("=== 测试5: 更新评价分组 - 成功 ===");

        // 准备测试数据
        EvaluationGroup evaluationGroup = createTestEvaluationGroup();
        evaluationGroup.setId(1L);
        evaluationGroup.setTeamId(TEST_TEAM_ID); // 确保团队ID匹配
        when(evaluationGroupService.update(any(EvaluationGroup.class))).thenReturn(true);

        // 执行请求
        mockMvc.perform(put("/api/evaluation-groups")
                        .header("Authorization", TEST_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(evaluationGroup)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1));

        // 验证服务调用
        verify(evaluationGroupService, times(1)).update(any(EvaluationGroup.class));

        System.out.println("✅ 更新评价分组测试通过");
    }

    @Test
    @Order(6)
    @DisplayName("测试更新评价分组 - 权限不足")
    void testUpdateEvaluationGroup_NoPermission() throws Exception {
        System.out.println("=== 测试6: 更新评价分组 - 权限不足 ===");

        // 准备测试数据（不同的团队ID）
        EvaluationGroup evaluationGroup = createTestEvaluationGroup();
        evaluationGroup.setId(1L);
        evaluationGroup.setTeamId(999L); // 不同的团队ID

        // 执行请求
        mockMvc.perform(put("/api/evaluation-groups")
                        .header("Authorization", TEST_TOKEN)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(evaluationGroup)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("无权修改其他团队的分组"));

        // 验证服务没有被调用
        verify(evaluationGroupService, never()).update(any(EvaluationGroup.class));

        System.out.println("✅ 权限验证测试通过");
    }

    @Test
    @Order(7)
    @DisplayName("测试删除评价分组 - 成功")
    void testDeleteEvaluationGroup_Success() throws Exception {
        System.out.println("=== 测试7: 删除评价分组 - 成功 ===");

        // 准备测试数据
        String ids = "1,2,3";
        when(evaluationGroupService.deleteByIds(ids, TEST_TEAM_ID)).thenReturn(true);

        // 执行请求
        mockMvc.perform(delete("/api/evaluation-groups")
                        .param("ids", ids)
                        .header("Authorization", TEST_TOKEN))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1));

        // 验证服务调用
        verify(evaluationGroupService, times(1)).deleteByIds(ids, TEST_TEAM_ID);

        System.out.println("✅ 删除评价分组测试通过");
    }

    @Test
    @Order(8)
    @DisplayName("测试删除评价分组 - 失败")
    void testDeleteEvaluationGroup_Failure() throws Exception {
        System.out.println("=== 测试8: 删除评价分组 - 失败 ===");

        // 准备测试数据
        String ids = "1,2,3";
        when(evaluationGroupService.deleteByIds(ids, TEST_TEAM_ID)).thenReturn(false);

        // 执行请求
        mockMvc.perform(delete("/api/evaluation-groups")
                        .param("ids", ids)
                        .header("Authorization", TEST_TOKEN))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("删除失败"));

        System.out.println("✅ 删除失败测试通过");
    }

    // 辅助方法：创建测试用的评价分组
    private EvaluationGroup createTestEvaluationGroup() {
        EvaluationGroup group = new EvaluationGroup();
        group.setGroupTitle("测试评价分组");
        group.setParentId(null);
        group.setIsOfficial(false);
        group.setSortOrder(0);
        group.setCreateTime(LocalDateTime.now());
        group.setUpdateTime(LocalDateTime.now());
        group.setCreator(TEST_USER_ID);
        group.setUpdater(TEST_USER_ID);
        group.setVersion(0L);
        return group;
    }

    // 辅助方法：创建模拟的分组树数据
    private Map<String, Object> createMockGroupTree() {
        Map<String, Object> groupTree = new HashMap<>();
        List<Map<String, Object>> groups = new ArrayList<>();
        
        Map<String, Object> rootGroup = new HashMap<>();
        rootGroup.put("id", -1L);
        rootGroup.put("groupTitle", "全部评价");
        rootGroup.put("isOfficial", 0);
        rootGroup.put("subGroups", new ArrayList<>());
        
        groups.add(rootGroup);
        groupTree.put("groups", groups);
        
        return groupTree;
    }
}
