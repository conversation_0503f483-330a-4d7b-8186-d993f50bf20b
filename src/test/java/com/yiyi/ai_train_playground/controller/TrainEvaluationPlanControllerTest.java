package com.yiyi.ai_train_playground.controller;

import com.yiyi.ai_train_playground.common.Result;
import com.yiyi.ai_train_playground.dto.EvaluationPlanShortDTO;
import com.yiyi.ai_train_playground.service.TrainEvaluationPlanService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 评价方案控制器测试
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-03
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TrainEvaluationPlanControllerTest {

    @Autowired
    private TepController tepController;

    @Autowired
    private TrainEvaluationPlanService trainEvaluationPlanService;

    @Autowired
    private JwtUtil jwtUtil;

    private static final Long TEST_TEAM_ID = 1L;
    private static final String TEST_USER_ID = "test_user";

    @Test
    public void testGetTepShortList_Success() {
        // 创建模拟的HTTP请求
        MockHttpServletRequest request = new MockHttpServletRequest();
        
        // 生成JWT token
        String token = jwtUtil.generateToken(1L, TEST_USER_ID, TEST_TEAM_ID, false);
        request.addHeader("Authorization", "Bearer " + token);
        
        // 调用控制器方法
        var result = tepController.getShortList(request);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getCode());
        assertEquals("success", result.getMessage());
        assertNotNull(result.getData());
        
        @SuppressWarnings("unchecked")
        List<EvaluationPlanShortDTO> data = (List<EvaluationPlanShortDTO>) result.getData();
        assertTrue(data.size() > 0);
        
        // 验证第一个结果的字段
        EvaluationPlanShortDTO first = data.get(0);
        assertNotNull(first.getId());
        assertNotNull(first.getTeamId());
        assertNotNull(first.getGroupId());
        assertNotNull(first.getEvaName());
        assertNotNull(first.getEvaGroupName());
        
        assertEquals(TEST_TEAM_ID, first.getTeamId());
        
        log.info("获取评价方案简短列表成功：size={}", data.size());
        for (EvaluationPlanShortDTO dto : data) {
            log.info("评价方案：id={}, teamId={}, groupId={}, evaName={}, evaGroupName={}", 
                    dto.getId(), dto.getTeamId(), dto.getGroupId(), dto.getEvaName(), dto.getEvaGroupName());
        }
    }

    @Test
    public void testGetTepShortList_NoToken() {
        // 创建没有token的HTTP请求
        MockHttpServletRequest request = new MockHttpServletRequest();
        
        // 调用控制器方法
        var result = tepController.getShortList(request);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertTrue(result.getMessage().contains("未找到有效的认证token"));
        
        log.info("无token测试通过");
    }

    @Test
    public void testGetTepShortList_InvalidToken() {
        // 创建无效token的HTTP请求
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("Authorization", "Bearer invalid_token");
        
        // 调用控制器方法
        var result = tepController.getShortList(request);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(500, result.getCode());
        assertNotNull(result.getMessage());
        
        log.info("无效token测试通过");
    }

    @Test
    public void testDirectServiceCall() {
        // 直接调用服务层方法进行对比
        List<EvaluationPlanShortDTO> serviceResult = trainEvaluationPlanService.getShortList(TEST_TEAM_ID);
        
        assertNotNull(serviceResult);
        assertTrue(serviceResult.size() > 0);
        
        // 验证包含第一个评价方案
        boolean found = serviceResult.stream()
                .anyMatch(dto -> dto.getEvaName() != null && !dto.getEvaName().isEmpty());
        assertTrue(found, "应该包含有效的评价方案名称");
        
        log.info("直接服务调用测试通过：size={}", serviceResult.size());
    }
}
