package com.yiyi.ai_train_playground.controller.sms;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.dto.SmsCodeResponse;
import com.yiyi.ai_train_playground.dto.SmsLoginResponse;
import com.yiyi.ai_train_playground.service.sms.SmsServiceV2;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 短信验证码控制器V2测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-07-22
 */
@WebMvcTest(SmsControllerV2.class)
class SmsControllerV2Test {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SmsServiceV2 smsServiceV2;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testSendCode_Success() throws Exception {
        // Given
        String phone = "13800138000";
        SmsCodeResponse mockResponse = new SmsCodeResponse("f6e1d78b378643d6bafa847fd7d43071");
        
        when(smsServiceV2.sendVerificationCode(phone)).thenReturn(mockResponse);

        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("phone", phone);

        // When & Then
        mockMvc.perform(post("/api/v2/sms/code")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("发送成功"))
                .andExpect(jsonPath("$.data.verificationKey").value("f6e1d78b378643d6bafa847fd7d43071"));

        verify(smsServiceV2).sendVerificationCode(phone);
    }

    @Test
    void testSendCode_MissingPhoneParameter() throws Exception {
        // Given - 请求体中没有phone参数
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("verificationKey", "f6e1d78b378643d6bafa847fd7d43071");
        requestBody.put("verificationCode", "9025");

        // When & Then
        mockMvc.perform(post("/api/v2/sms/code")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("手机号不能为空"));

        verify(smsServiceV2, never()).sendVerificationCode(anyString());
    }

    @Test
    void testSendCode_InvalidPhoneFormat() throws Exception {
        // Given
        String invalidPhone = "12345678901";

        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("phone", invalidPhone);

        // When & Then
        mockMvc.perform(post("/api/v2/sms/code")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("手机号格式不正确，请输入正确的中国大陆手机号"));

        verify(smsServiceV2, never()).sendVerificationCode(anyString());
    }

    @Test
    void testSendCode_ServiceException() throws Exception {
        // Given
        String phone = "13800138000";
        when(smsServiceV2.sendVerificationCode(phone)).thenThrow(new RuntimeException("发送过于频繁，请1分钟后再试"));

        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("phone", phone);

        // When & Then
        mockMvc.perform(post("/api/v2/sms/code")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("发送过于频繁，请1分钟后再试"));

        verify(smsServiceV2).sendVerificationCode(phone);
    }

    @Test
    void testVerifyAndLogin_Success() throws Exception {
        // Given
        String verificationKey = "f6e1d78b378643d6bafa847fd7d43071";
        String verificationCode = "9025";
        
        SmsLoginResponse mockResponse = new SmsLoginResponse();
        mockResponse.setUserId(1L);
        mockResponse.setUsername("testuser");
        mockResponse.setDisplayName("Test User");
        mockResponse.setMobile("13800138000");
        mockResponse.setToken("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...");
        
        when(smsServiceV2.smsLogin(verificationKey, verificationCode, null, "test-xid")).thenReturn(mockResponse);

        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("verificationKey", verificationKey);
        requestBody.put("verificationCode", verificationCode);
        requestBody.put("xid", "test-xid");

        // When & Then
        mockMvc.perform(post("/api/v2/sms/verify")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("验证成功"))
                .andExpect(jsonPath("$.data.userId").value(1))
                .andExpect(jsonPath("$.data.username").value("testuser"))
                .andExpect(jsonPath("$.data.displayName").value("Test User"))
                .andExpect(jsonPath("$.data.mobile").value("13800138000"))
                .andExpect(jsonPath("$.data.token").value("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."))
                .andExpect(jsonPath("$.data.redirectUrl").value("http://training.yiyiai.com:8081/pg-guide/auth/1"));

        verify(smsServiceV2).smsLogin(verificationKey, verificationCode, null, "test-xid");
    }

    @Test
    void testVerifyAndLogin_MissingVerificationKey() throws Exception {
        // Given - 请求体中没有verificationKey参数
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("verificationCode", "9025");

        // When & Then
        mockMvc.perform(post("/api/v2/sms/verify")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("验证密钥不能为空"));

        verify(smsServiceV2, never()).smsLogin(anyString(), anyString(), anyString(), anyString());
    }

    @Test
    void testVerifyAndLogin_MissingVerificationCode() throws Exception {
        // Given - 请求体中没有verificationCode参数
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("verificationKey", "f6e1d78b378643d6bafa847fd7d43071");

        // When & Then
        mockMvc.perform(post("/api/v2/sms/verify")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("验证码不能为空"));

        verify(smsServiceV2, never()).smsLogin(anyString(), anyString(), anyString(), anyString());
    }

    @Test
    void testVerifyAndLogin_InvalidVerificationCodeFormat() throws Exception {
        // Given
        String verificationKey = "f6e1d78b378643d6bafa847fd7d43071";
        String invalidVerificationCode = "12345"; // 5位数字，应该是4位

        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("verificationKey", verificationKey);
        requestBody.put("verificationCode", invalidVerificationCode);

        // When & Then
        mockMvc.perform(post("/api/v2/sms/verify")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("验证码必须是4位数字"));

        verify(smsServiceV2, never()).smsLogin(anyString(), anyString(), anyString(), anyString());
    }

    @Test
    void testVerifyAndLogin_ServiceException() throws Exception {
        // Given
        String verificationKey = "f6e1d78b378643d6bafa847fd7d43071";
        String verificationCode = "9025";
        
        when(smsServiceV2.smsLogin(verificationKey, verificationCode, null, "test-xid"))
                .thenThrow(new RuntimeException("验证码错误"));

        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("verificationKey", verificationKey);
        requestBody.put("verificationCode", verificationCode);
        requestBody.put("xid", "test-xid");

        // When & Then
        mockMvc.perform(post("/api/v2/sms/verify")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestBody)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.message").value("验证失败：验证码错误"));

        verify(smsServiceV2).smsLogin(verificationKey, verificationCode, null, "test-xid");
    }
}
