package com.yiyi.ai_train_playground.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.dto.ScriptUpdateRequest;
import com.yiyi.ai_train_playground.entity.TrainScript;
import com.yiyi.ai_train_playground.mapper.TrainScriptMapper;
import com.yiyi.ai_train_playground.service.TrainScriptService;
import com.yiyi.ai_train_playground.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 剧本更新控制器测试
 */
@Slf4j
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
public class TrainScriptUpdateControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Autowired
    private TrainScriptService trainScriptService;

    @Autowired
    private TrainScriptMapper trainScriptMapper;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private ObjectMapper objectMapper;

    private static final Long DEFAULT_TEAM_ID = 1L;
    private static final String DEFAULT_UPDATER = "test_updater";

    @BeforeEach
    public void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    @WithMockUser(username = "test_updater", roles = {"USER"})
    public void testUpdateScript_Success() throws Exception {
        // 1. 先创建一个剧本
        TrainScript originalScript = new TrainScript();
        originalScript.setTeamId(DEFAULT_TEAM_ID);
        originalScript.setName("原始剧本");
        originalScript.setGenerationType(0);
        originalScript.setGroupId(1L);
        originalScript.setIntentId(1L);
        originalScript.setEvaluationId(1L);
        originalScript.setBuyerRequirement("原始需求");
        originalScript.setOrderPriority(1);
        originalScript.setOrderRemark("原始备注");
        originalScript.setRetryBuyerRequirementCounts(0);
        originalScript.setRetryFlowNodeCounts(0);
        originalScript.setSimulationTool("原始工具");
        originalScript.setCreator(DEFAULT_UPDATER);
        originalScript.setUpdater(DEFAULT_UPDATER);
        originalScript.setVersion(1L);
        originalScript.setIsOfficial(false);

        boolean createResult = trainScriptService.createScript(originalScript);
        assertTrue(createResult, "创建剧本应该成功");

        Long scriptId = originalScript.getId();
        log.info("创建的剧本ID: {}", scriptId);

        // 2. 准备更新请求
        ScriptUpdateRequest updateRequest = new ScriptUpdateRequest();
        updateRequest.setId(scriptId);
        updateRequest.setName("更新后的剧本");
        updateRequest.setGenerationTypeCode(1);
        updateRequest.setGroupId(2L);
        updateRequest.setIntentId(2L);
        updateRequest.setEvaluationPlanId(2L);
        updateRequest.setBuyerRequirement("更新后的需求");
        updateRequest.setOrderIsRemarked(1);
        updateRequest.setOrderPriority(2);
        updateRequest.setOrderRemark("更新后的备注");
        updateRequest.setRetryBuyerRequirementCounts(0);
        updateRequest.setRetryFlowNodeCounts(0);
        updateRequest.setSimulationTool("更新后的工具");
        updateRequest.setVersion(1L);
        updateRequest.setIsOfficial(false);

        // 3. 准备商品列表
        List<ScriptUpdateRequest.ProductUpdateDTO> productList = new ArrayList<>();
        ScriptUpdateRequest.ProductUpdateDTO product = new ScriptUpdateRequest.ProductUpdateDTO();
        product.setId(1L);
        product.setExternalProductId("update_product_001");
        product.setExternalProductName("更新商品1");
        product.setExternalProductLink("http://update.example.com/product1");
        product.setExternalProductImage("http://update.example.com/image1.jpg");
        productList.add(product);
        updateRequest.setProductList(productList);

        // 4. 准备关联图片列表
        List<ScriptUpdateRequest.RelatedImageUpdateDTO> relateImgs = new ArrayList<>();
        ScriptUpdateRequest.RelatedImageUpdateDTO image = new ScriptUpdateRequest.RelatedImageUpdateDTO();
        image.setId(1L);
        image.setRecognizedText("更新图片识别文本");
        image.setUploadType(1);
        image.setMediaType(1);
        image.setUrl("http://update.example.com/image.jpg");
        relateImgs.add(image);
        updateRequest.setRelateImgs(relateImgs);

        // 5. 准备流程节点列表
        List<ScriptUpdateRequest.FlowNodeUpdateDTO> flowNodes = new ArrayList<>();
        ScriptUpdateRequest.FlowNodeUpdateDTO node = new ScriptUpdateRequest.FlowNodeUpdateDTO();
        node.setId(1L);
        node.setNodeName("更新节点1");
        node.setNodeBuyerRequirement("更新节点需求1");
        flowNodes.add(node);
        updateRequest.setFlowNodes(flowNodes);

        // 6. 生成JWT token
        String jwtToken = jwtUtil.generateToken(1L, DEFAULT_UPDATER, DEFAULT_TEAM_ID, false);

        // 7. 执行HTTP请求
        String requestJson = objectMapper.writeValueAsString(updateRequest);
        log.info("请求JSON: {}", requestJson);

        mockMvc.perform(put("/api/scripts/{id}", scriptId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestJson)
                        .header("Authorization", "Bearer " + jwtToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("操作成功"))
                .andExpect(jsonPath("$.data").value("剧本更新成功"));

        // 8. 验证更新结果
        TrainScript updatedScript = trainScriptMapper.selectById(scriptId, DEFAULT_TEAM_ID);
        assertNotNull(updatedScript, "更新后的剧本应该存在");
        assertEquals("更新后的剧本", updatedScript.getName(), "剧本名称应该已更新");
        assertEquals(Integer.valueOf(1), updatedScript.getGenerationType(), "生成类型应该已更新");
        assertEquals(Long.valueOf(2), updatedScript.getGroupId(), "分组ID应该已更新");
        assertEquals(Long.valueOf(2), updatedScript.getIntentId(), "意图ID应该已更新");
        assertEquals(Long.valueOf(2), updatedScript.getEvaluationId(), "评估计划ID应该已更新");
        assertEquals("更新后的需求", updatedScript.getBuyerRequirement(), "买家需求应该已更新");
        assertEquals(Integer.valueOf(2), updatedScript.getOrderPriority(), "订单优先级应该已更新");
        assertEquals("更新后的备注", updatedScript.getOrderRemark(), "订单备注应该已更新");
        assertEquals(Integer.valueOf(0), updatedScript.getRetryBuyerRequirementCounts(), "重试买家需求次数应该已更新");
        assertEquals(Integer.valueOf(0), updatedScript.getRetryFlowNodeCounts(), "重试流程节点次数应该已更新");
        assertEquals("更新后的工具", updatedScript.getSimulationTool(), "模拟工具应该已更新");

        log.info("剧本更新测试完成");
    }
}
