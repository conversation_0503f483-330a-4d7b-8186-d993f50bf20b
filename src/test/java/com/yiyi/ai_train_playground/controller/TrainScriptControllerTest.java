package com.yiyi.ai_train_playground.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.ScriptListDTO;
import com.yiyi.ai_train_playground.dto.ScriptQueryRequest;
import com.yiyi.ai_train_playground.dto.ScriptCreateRequest;
import com.yiyi.ai_train_playground.dto.ScriptDetailDTO;
import com.yiyi.ai_train_playground.entity.TrainScript;
import com.yiyi.ai_train_playground.service.TrainScriptService;
import com.yiyi.ai_train_playground.dto.PageResult;
import com.yiyi.ai_train_playground.dto.ScriptListDTO;
import com.yiyi.ai_train_playground.security.CustomUserDetails;
import com.yiyi.ai_train_playground.util.JwtUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 剧本控制器测试类
 *
 * <AUTHOR> Assistant
 * @since 2025-07-02
 */
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@Transactional
public class TrainScriptControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private TrainScriptService trainScriptService;

    @MockBean
    private JwtUtil jwtUtil;

    @MockBean
    private UserDetailsService userDetailsService;

    @Autowired
    private ObjectMapper objectMapper;
    
    private String validToken;
    private Long teamId;
    private Long userId;
    
    @BeforeEach
    void setUp() {
        validToken = "valid-jwt-token";
        teamId = 1L;
        userId = 1L;

        // Mock JWT工具类
        when(jwtUtil.validateToken(validToken)).thenReturn(true);
        when(jwtUtil.getUsernameFromToken(validToken)).thenReturn("testuser");
        when(jwtUtil.getTeamIdFromToken(validToken)).thenReturn(teamId);
        when(jwtUtil.getUserIdFromToken(validToken)).thenReturn(userId);

        // Mock UserDetailsService
        CustomUserDetails userDetails = new CustomUserDetails(
            userId, "testuser", "password", teamId, true, true
        );
        when(userDetailsService.loadUserByUsername("testuser")).thenReturn(userDetails);

        // Mock TrainScriptService
        List<ScriptListDTO> mockScripts = new ArrayList<>();
        ScriptListDTO script1 = new ScriptListDTO();
        script1.setId(1L);
        script1.setName("测试剧本1");
        script1.setCreator("testuser");
        script1.setGenerationType("商品知识训练");
        script1.setIntentName("测试意图");
        mockScripts.add(script1);

        PageResult<ScriptListDTO> mockPageResult = new PageResult<>();
        mockPageResult.setTotal(1L);
        mockPageResult.setRecords(mockScripts);
        mockPageResult.setPage(1);
        mockPageResult.setSize(10);
        mockPageResult.setPages(1);

        when(trainScriptService.getScriptList(any(ScriptQueryRequest.class), eq(teamId))).thenReturn(mockPageResult);
    }
    
    @Test
    void testGetScripts_Success() throws Exception {
        // 执行请求
        mockMvc.perform(get("/api/scripts")
                .header("Authorization", "Bearer " + validToken)
                .param("page", "1")
                .param("pageSize", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.message").value("success"));
    }
    
    @Test
    void testGetScripts_WithFilters() throws Exception {
        // 执行请求
        mockMvc.perform(get("/api/scripts")
                .header("Authorization", "Bearer " + validToken)
                .param("name", "催促派单")
                .param("generationTypeCode", "0")
                .param("page", "1")
                .param("pageSize", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1));
    }
    
    @Test
    void testGetScripts_NoToken() throws Exception {
        // 执行请求（无token）
        mockMvc.perform(get("/api/scripts")
                .param("page", "1")
                .param("pageSize", "10"))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.code").value(401));
    }
    
    @Test
    void testCreateScript_Success() throws Exception {
        // 准备测试数据
        ScriptCreateRequest request = new ScriptCreateRequest();
        request.setName("测试剧本");
        request.setBuyerRequirement("测试买家需求");
        request.setGenerationTypeCode(0);
        request.setGroupId(13L);
        request.setIntentId(5L);
        request.setEvaluationPlanId(2001L);
        request.setOrderIsRemarked(3);
        request.setOrderRemark("测试备注");
        request.setSimulationTool("Simulator_V3");

        when(trainScriptService.createScriptWithRelatedData(any(ScriptCreateRequest.class), anyLong(), anyString())).thenReturn(1L);

        // 执行请求
        mockMvc.perform(post("/api/scripts")
                .header("Authorization", "Bearer " + validToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.data").value(1L));
    }
    
    @Test
    void testUpdateScript_Success() throws Exception {
        // 准备测试数据
        TrainScript script = new TrainScript();
        script.setName("更新后的剧本");
        script.setBuyerRequirement("更新后的买家需求");
        script.setGenerationType(1);
        
        when(trainScriptService.updateScript(any(TrainScript.class))).thenReturn(true);
        
        // 执行请求
        mockMvc.perform(put("/api/scripts/1")
                .header("Authorization", "Bearer " + validToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(script)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.data").value("剧本更新成功"));
    }
    
    @Test
    void testDeleteScript_Success() throws Exception {
        when(trainScriptService.deleteScript(1L, teamId)).thenReturn(true);

        // 执行请求
        mockMvc.perform(delete("/api/scripts/1")
                .header("Authorization", "Bearer " + validToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.data").value("剧本删除成功"));
    }

    @Test
    void testGetScriptById_Success() throws Exception {
        // 准备测试数据
        ScriptDetailDTO mockDetail = new ScriptDetailDTO();
        mockDetail.setId(1L);
        mockDetail.setName("商品知识训练剧本");
        mockDetail.setGenerationTypeCode(0);
        mockDetail.setGenerationType("商品知识训练");
        mockDetail.setIntentName("销售意图");
        mockDetail.setBuyerRequirement("需要了解产品特性");

        when(trainScriptService.getScriptDetail(1L, teamId)).thenReturn(mockDetail);

        // 执行请求
        mockMvc.perform(get("/api/scripts/1")
                .header("Authorization", "Bearer " + validToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(1))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.name").value("商品知识训练剧本"))
                .andExpect(jsonPath("$.data.generationType").value("商品知识训练"))
                .andExpect(jsonPath("$.data.intentName").value("销售意图"));
    }

    @Test
    void testGetScriptById_NotFound() throws Exception {
        when(trainScriptService.getScriptDetail(999L, teamId))
                .thenThrow(new RuntimeException("剧本不存在或无权限访问"));

        // 执行请求
        mockMvc.perform(get("/api/scripts/999")
                .header("Authorization", "Bearer " + validToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.message").value("剧本不存在或无权限访问"));
    }
}
