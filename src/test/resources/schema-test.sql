-- 创建评价分组表
CREATE TABLE IF NOT EXISTS train_evaluation_group (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    group_title VARCHAR(255) NOT NULL,
    parent_id BIGINT,
    is_official B<PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VA<PERSON><PERSON><PERSON>(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 1
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_evaluation_group_team_id ON train_evaluation_group(team_id);
CREATE INDEX IF NOT EXISTS idx_evaluation_group_parent_id ON train_evaluation_group(parent_id);

-- 创建意图表
CREATE TABLE IF NOT EXISTS train_intent (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    parent_id BIGINT,
    team_id BIGINT NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建剧本分组表
CREATE TABLE IF NOT EXISTS train_script_group (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    group_title VARCHAR(255) NOT NULL,
    parent_id BIGINT,
    is_official BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 1
);

-- 创建剧本表
CREATE TABLE IF NOT EXISTS train_script (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    generation_type INT DEFAULT 0,
    group_id BIGINT,
    intent_id BIGINT,
    evaluation_id BIGINT,
    buyer_requirement TEXT,
    order_priority INT DEFAULT 0,
    order_remark VARCHAR(500),
    retry_buyer_requirement_counts INT DEFAULT 0,
    retry_flow_node_counts INT DEFAULT 0,
    simulation_tool VARCHAR(100),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 1,
    is_official BOOLEAN DEFAULT FALSE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_script_team_id ON train_script(team_id);
CREATE INDEX IF NOT EXISTS idx_script_group_id ON train_script(group_id);
CREATE INDEX IF NOT EXISTS idx_script_intent_id ON train_script(intent_id);

-- 创建评价方案表
CREATE TABLE IF NOT EXISTS train_evaluation_plan (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    group_id BIGINT,
    description TEXT,
    is_official BOOLEAN DEFAULT FALSE,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 1
);

-- 创建剧本商品关联表
CREATE TABLE IF NOT EXISTS train_script_products (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    script_id BIGINT NOT NULL,
    external_product_id VARCHAR(255),
    external_product_name VARCHAR(255),
    external_product_link VARCHAR(500),
    external_product_image VARCHAR(500),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 1
);

-- 创建流程节点表
CREATE TABLE IF NOT EXISTS train_flow_node (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    script_id BIGINT NOT NULL,
    node_name VARCHAR(255) NOT NULL,
    node_buyer_requirement TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 1
);

-- 创建关联图片表
CREATE TABLE IF NOT EXISTS train_related_image (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    team_id BIGINT NOT NULL,
    script_id BIGINT NOT NULL,
    recognized_text TEXT,
    upload_type INT DEFAULT 1,
    media_type INT DEFAULT 1,
    url VARCHAR(500),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    creator VARCHAR(255),
    updater VARCHAR(255),
    version BIGINT DEFAULT 1
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_evaluation_plan_team_id ON train_evaluation_plan(team_id);
CREATE INDEX IF NOT EXISTS idx_evaluation_plan_group_id ON train_evaluation_plan(group_id);
CREATE INDEX IF NOT EXISTS idx_script_products_team_id ON train_script_products(team_id);
CREATE INDEX IF NOT EXISTS idx_script_products_script_id ON train_script_products(script_id);
CREATE INDEX IF NOT EXISTS idx_flow_node_team_id ON train_flow_node(team_id);
CREATE INDEX IF NOT EXISTS idx_flow_node_script_id ON train_flow_node(script_id);
CREATE INDEX IF NOT EXISTS idx_related_image_team_id ON train_related_image(team_id);
CREATE INDEX IF NOT EXISTS idx_related_image_script_id ON train_related_image(script_id);
