-- 插入测试数据
INSERT INTO train_evaluation_group (id, team_id, group_title, parent_id, is_official, sort_order, creator, updater) VALUES
(1, 1, '根分组', NULL, TRUE, 1, 'test_user', 'test_user'),
(2, 1, '子分组1', 1, FALSE, 2, 'test_user', 'test_user'),
(3, 1, '子分组2', 1, FALSE, 3, 'test_user', 'test_user'),
(4, 2, '其他团队分组', NULL, TRUE, 1, 'other_user', 'other_user');

-- 插入意图测试数据
INSERT INTO train_intent (id, name, parent_id, team_id) VALUES
(1, '销售意图', NULL, 1),
(2, '客服意图', NULL, 1),
(3, '产品咨询', 1, 1),
(4, '其他团队意图', NULL, 2);

-- 插入剧本分组测试数据
INSERT INTO train_script_group (id, team_id, group_title, parent_id, is_official, sort_order, creator, updater) VALUES
(1, 1, '剧本根分组', NULL, TRUE, 1, 'test_user', 'test_user'),
(2, 1, '剧本子分组1', 1, FALSE, 2, 'test_user', 'test_user'),
(3, 1, '剧本子分组2', 1, FALSE, 3, 'test_user', 'test_user'),
(4, 2, '其他团队剧本分组', NULL, TRUE, 1, 'other_user', 'other_user');

-- 插入评价方案测试数据
INSERT INTO train_evaluation_plan (id, team_id, name, group_id, creator, updater) VALUES
(1, 1, '六边形战士', 1, 'test_user', 'test_user'),
(2, 1, '进阶评价方案', 2, 'test_user', 'test_user'),
(3, 1, '高级评价方案', 3, 'test_user', 'test_user'),
(2001, 1, '测试评价方案', 1, 'test_user', 'test_user');

-- 插入剧本测试数据
INSERT INTO train_script (id, team_id, name, generation_type, group_id, intent_id, evaluation_id, buyer_requirement, order_priority, order_remark, creator, updater) VALUES
(1, 1, '商品知识训练剧本', 0, 1, 1, 1, '需要了解产品特性', 1, '高优先级', 'test_user', 'test_user'),
(2, 1, '实战能力进阶剧本', 1, 2, 2, 2, '提升销售技巧', 2, '中优先级', 'test_user', 'test_user'),
(3, 1, '自定义内容剧本', 2, 3, 3, 3, '个性化培训', 3, '低优先级', 'test_user', 'test_user'),
(4, 2, '其他团队剧本', 0, 4, 4, NULL, '其他需求', 1, '备注', 'other_user', 'other_user');

-- 插入剧本商品关联测试数据
INSERT INTO train_script_products (id, team_id, script_id, external_product_id, external_product_name, external_product_link, external_product_image, creator, updater) VALUES
(1, 1, 1, 'PROD001', '智能手机', 'https://example.com/phone', 'https://example.com/phone.jpg', 'test_user', 'test_user'),
(2, 1, 1, 'PROD002', '蓝牙耳机', 'https://example.com/earphone', 'https://example.com/earphone.jpg', 'test_user', 'test_user'),
(3, 1, 2, 'PROD003', '笔记本电脑', 'https://example.com/laptop', 'https://example.com/laptop.jpg', 'test_user', 'test_user');

-- 插入流程节点测试数据
INSERT INTO train_flow_node (id, team_id, script_id, node_name, node_buyer_requirement, creator, updater) VALUES
(1, 1, 1, '产品参数', '询问产品的详细参数', 'test_user', 'test_user'),
(2, 1, 1, '价格咨询', '了解产品价格和优惠', 'test_user', 'test_user'),
(3, 1, 2, '需求分析', '分析客户的具体需求', 'test_user', 'test_user'),
(4, 1, 2, '方案推荐', '推荐合适的解决方案', 'test_user', 'test_user');

-- 插入关联图片测试数据
INSERT INTO train_related_image (id, team_id, script_id, recognized_text, upload_type, media_type, url, creator, updater) VALUES
(1, 1, 1, '产品展示图片', 1, 1, 'https://example.com/image1.jpg', 'test_user', 'test_user'),
(2, 1, 1, '使用说明视频', 1, 2, 'https://example.com/video1.mp4', 'test_user', 'test_user'),
(3, 1, 2, '培训材料图片', 2, 1, 'https://external.com/training.jpg', 'test_user', 'test_user');
