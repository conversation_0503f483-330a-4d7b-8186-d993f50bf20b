server:
  port: 9999
  tomcat:
    threads:
      max: 200 # 统一设置最大线程

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:yiyi_ai_db}?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf-8&allowPublicKeyRetrieval=true&useSSL=false
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
    druid:
      # 初始连接数
      initial-size: 5
      # 最小连接池数量
      min-idle: 5
      # 最大连接池数量
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
#  mvc:
#    servlet:
#      path: /api
  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost} # Redis服务器地址
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:123456} # Redis密码
      database: ${REDIS_DATABASE:0} # 指定数据库
      ssl:
        enabled: false # 开发环境不启用SSL
      # Lettuce连接池配置（关键优化点）
      lettuce:
        pool:
          max-active: 50 # 最大连接数
          max-idle: 20   # 最大空闲连接
          min-idle: 5    # 最小空闲连接
          max-wait: 2000 # 获取连接最大等待时间(ms)
          time-between-eviction-runs: 30000 # 空闲连接检查周期(ms)
  # 定时任务配置
  task:
    scheduling:
      enabled: false # 测试环境禁用定时任务
      pool:
        size: 5 # 定时任务线程池大小
      thread-name-prefix: "scheduler-" # 线程名前缀
  # 事务管理配置
  transaction:
    default-timeout: 30 # 默认事务超时时间（秒）
    rollback-on-commit-failure: true # 提交失败时回滚

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.yiyi.ai_train_playground.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  level:
    root: INFO
    com.yiyi.ai_train_playground: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/application-test.log

jwt:
  secret: ${JWT_SECRET:gJj0XQdx9yhNY3sQpOPcHLYV7rWmb8A4KtIZ2EMi6onFk1qlaDSeuTvRfzG5wU}
  expiration: ${JWT_EXPIRATION:86400000} # 24小时 (24 * 60 * 60 * 1000)
  remember-me-expiration: ${JWT_REMEMBER_ME_EXPIRATION:2592000000} # 30天 (30 * 24 * 60 * 60 * 1000)



# 豆包大模型配置
ARK_API_KEY: df48ab3c-dc0d-46c3-bae4-5e83b1258297

# 豆包模型配置
my:
  doubao:
    think:
      model:
        name: doubao-1.5-thinking-pro-250415
    normal:
      model:
        name: doubao-1-5-pro-32k-250115
      endpoint:
        name: ep-20250629195408-gtv9c
    url: https://ark.cn-beijing.volces.com/api/v3/
    embed:
      model-name: doubao-embedding-text-240715
    image:
      model:
        name: doubao-1-5-vision-pro-32k-250115
    estimateToken: 500
    connection-pool:
      max-idle: 100
      max-requests: 100
      max-requests-per-host: 50
      keep-alive-duration: 5  # 连接保持时间（分钟）

# 阿里云OSS配置
oss:
  endpoint: ${OSS_ENDPOINT:oss-cn-shanghai.aliyuncs.com}
  access-id: ${OSS_ACCESS_ID:LTAIPXL4NBBjtwG6}
  access-key: ${OSS_ACCESS_KEY:smybdcjqJAL4oL8qkL9qRESoYQfhv0}
  bucket-name: ${OSS_BUCKET_NAME:ai-playground}
  temp-file-expiration: ${OSS_TEMP_FILE_EXPIRATION:24}  # 临时文件过期时间（小时）

# Qdrant向量数据库配置
qdrant:
  host: ${QDRANT_HOST:localhost}
  http-port: ${QDRANT_HTTP_PORT:6333}
  grpc-port: ${QDRANT_GRPC_PORT:6334}
  username: ${QDRANT_USERNAME:qdrant}
  password: ${QDRANT_PASSWORD:qdrant}
  use-tls: ${QDRANT_USE_TLS:false}
  api-key: ${QDRANT_API_KEY:} # 如果需要认证，请设置API Key

# 商品处理配置
product:
  processing:
    chunk-size: 100      # 文本分块大小
    overlap-size: 20     # 文本重叠大小
    vector-dimension: 2560 # 向量维度
    collection-name: train_prod_collection # 向量集合名称
    jd-collection-name: train_prod_jd_collection # 京东商品向量集合名称

# 京东配置（测试环境）
jd:
  server-url: ${JD_SERVER_URL:https://api.jd.com/routerjson}  # 京东API服务器地址
  expected-state: ${JD_EXPECTED_STATE:YyJdPlayground2025}  # 期望的state参数值
  app-key: ${JD_APP_KEY:58EC57CE1D6C6B997519BA25E73A7228}  # 京东应用Key
  app-secret: ${JD_APP_SECRET:dbff743f33fd4a46bfa3399cf189e252}  # 京东应用Secret
  access-token: ${JD_ACCESS_TOKEN:89fd9dcc03d34c6d997fc66e019700bcy2mw}  # 京东访问令牌
  token-url: ${JD_TOKEN_URL:https://open-oauth.jd.com/oauth2/access_token}  # 京东令牌获取URL
  refresh-token-url: ${JD_REFRESH_TOKEN_URL:https://open-oauth.jd.com/oauth2/refresh_token}  # 京东令牌刷新URL
  redirect-base-url: ${JD_REDIRECT_BASE_URL:http://www.yiyiailocal.com:5173/home}  # 重定向基础URL
  sync:
    page-size: ${JD_SYNC_PAGE_SIZE:10}  # 京东商品同步分页大小，每页获取的商品数量
    is-mock-switch: true  # 测试环境启用本地模拟数据开关
  # Token刷新定时任务配置（测试环境）
  token-refresh:
    interval-minutes: 1  # 测试环境设置为1分钟，方便测试
    lock-expire-minutes: 1  # 测试环境锁过期时间1分钟


