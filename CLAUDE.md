# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Spring Boot 3.5.0 application that simulates customer service training using AI models. The system enables multiple AI-powered mock customers to interact with customer service representatives in real-time for training purposes.

## Build System & Commands

### Maven Commands
- **Build**: `mvn clean compile`
- **Run application**: `mvn spring-boot:run`
- **Run tests**: `mvn test`
- **Package**: `mvn clean package`
- **Run specific test**: `mvn test -Dtest=ClassName`

### Application Configuration

#### Environment Configuration (Security Enhanced)
- **Configuration externalization**: All sensitive values are now externalized via environment variables
- **Default port**: 8081 (configured in application.yml, can be overridden with SERVER_PORT env var)
- **Profile configurations**: `application.yml`, `application-test.yml`, `application-home.yml`
- **Environment template**: Use `.env.example` as template for setting up environment variables

#### Required Environment Variables
Essential environment variables that must be set for production:

**Database Configuration:**
```bash
DB_HOST=localhost          # Database host
DB_PORT=3306              # Database port  
DB_NAME=yiyi_ai_db        # Database name
DB_USERNAME=root          # Database username
DB_PASSWORD=your_secure_password  # Database password
```

**Redis Configuration:**
```bash
REDIS_HOST=localhost      # Redis host
REDIS_PORT=6379          # Redis port
REDIS_PASSWORD=your_redis_password  # Redis password
REDIS_DATABASE=0         # Redis database number
```

**Security Configuration:**
```bash
JWT_SECRET=your_256_bit_secret_key  # JWT signing secret (CRITICAL)
ARK_API_KEY=your_ark_api_key       # Doubao AI model API key
```

**Cloud Services:**
```bash
# Alibaba Cloud OSS
OSS_ACCESS_ID=your_oss_access_id
OSS_ACCESS_KEY=your_oss_access_key
OSS_ENDPOINT=oss-cn-shanghai.aliyuncs.com
OSS_BUCKET_NAME=ai-playground

# Qdrant Vector Database
QDRANT_HOST=localhost
QDRANT_USERNAME=qdrant
QDRANT_PASSWORD=your_qdrant_password

# JingDong API
JD_APP_KEY=your_jd_app_key
JD_APP_SECRET=your_jd_app_secret
JD_ACCESS_TOKEN=your_jd_access_token
```

#### Environment Setup
1. **Development**: Copy `.env.example` to `.env` and set your local values
2. **Production**: Set environment variables in your deployment environment
3. **Testing**: Use test profile with environment-specific values

#### Security Best Practices
- ✅ **Never commit secrets**: `.env` files are in `.gitignore`
- ✅ **Rotate credentials**: Regularly update API keys and passwords  
- ✅ **Strong JWT secrets**: Use minimum 256-bit random keys
- ✅ **Environment isolation**: Different credentials per environment

## Key Architecture Components

### Core Services
- **BigModelManager** (`src/main/java/com/yiyi/ai_train_playground/service/BigModelManager.java`): Central session manager for all AI conversations, handles message history and context
- **ChatWebSocketController** (`src/main/java/com/yiyi/ai_train_playground/controller/ChatWebSocketController.java`): WebSocket controller managing real-time chat sessions using STOMP protocol
- **DoubaoBigModelServiceImpl**: Handles integration with Doubao AI models (both streaming and non-streaming)

### AI Model Integration
- Supports multiple Doubao model types: Normal (non-streaming), Thinking (non-streaming), Normal (streaming), Thinking (streaming)
- Custom connection pool configuration with max 100 connections
- Vector search integration with Qdrant for context retrieval
- Text chunking and embedding services using HanLP

### Database & Persistence
- MyBatis for ORM with XML mapper configurations in `src/main/resources/mapper/`
- MySQL database with Druid connection pooling
- Entity classes in `src/main/java/com/yiyi/ai_train_playground/entity/`
- Comprehensive test database setup with H2 for testing

### WebSocket Real-time Communication
- Session format: `{robotId}_{servicerId}`
- Message endpoints: `/app/init`, `/app/send`, `/app/initMultiple`
- Response topics: `/topic/init`, `/topic/chat/{sessionId}`, `/topic/initMultiple`
- Uses SockJS + STOMP for browser compatibility

### Security & Authentication
- JWT-based authentication with configurable expiration
- Spring Security integration with custom user details service
- Role-based access control for different training scenarios

### External Integrations
- **JingDong (JD) API**: Product synchronization and callback handling
- **Alibaba Cloud OSS**: File storage and media upload
- **Redis**: Caching and session management (optional)
- **SMS Service**: User authentication via SMS codes

## Development Workflow

### Test Structure
Tests are organized by type:
- Unit tests: `src/test/java/com/yiyi/ai_train_playground/service/`
- Integration tests: `src/test/java/com/yiyi/ai_train_playground/integration/`
- Controller tests: `src/test/java/com/yiyi/ai_train_playground/controller/`

### Configuration Management
- Environment-specific configurations in `application-{profile}.yml`
- Database credentials, API keys, and service endpoints configured via YAML
- Docker support with included Dockerfile

### Key Business Logic
- **Training Scenarios**: `trialOne`, `trialFour`, `formal` - different modes for customer service training
- **Role Simulation**: Random customer personas (washing machine buyer, refrigerator buyer, computer buyer, car buyer)
- **Context Management**: Each session maintains conversation history and system prompts
- **Vector Search**: Product information retrieval using embeddings and Qdrant vector database

## Important Notes
- The application requires external services: MySQL, Redis (optional), Qdrant vector database
- AI model functionality depends on valid ARK_API_KEY environment variable
- WebSocket connections use STOMP protocol - ensure proper client library usage
- Database migrations and schema updates should be applied manually using provided SQL scripts