#!/bin/bash

# AI Train Playground API测试脚本
# 使用JWT认证测试API接口

# 配置
BASE_URL="http://localhost:8081"
LOGIN_ENDPOINT="/api/auth/login"
HEALTH_ENDPOINT="/actuator/health"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== AI Train Playground API测试 ===${NC}"

# 1. 测试登录接口获取JWT Token
echo -e "\n${YELLOW}1. 测试登录接口...${NC}"
LOGIN_RESPONSE=$(curl -s -X POST "${BASE_URL}${LOGIN_ENDPOINT}" \
  -H "Content-Type: application/json" \
  -d '{
    "identity": "admin",
    "password": "123456",
    "rememberMe": false
  }')

echo "登录响应: $LOGIN_RESPONSE"

# 检查登录是否成功
if echo "$LOGIN_RESPONSE" | grep -q '"code":200'; then
    echo -e "${GREEN}✅ 登录成功${NC}"
    
    # 提取token
    TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$TOKEN" ]; then
        echo -e "${GREEN}✅ 成功获取JWT Token${NC}"
        echo "Token: ${TOKEN:0:50}..."
        
        # 2. 使用token测试健康检查端点
        echo -e "\n${YELLOW}2. 测试健康检查端点...${NC}"
        
        # 不带认证的健康检查
        echo "测试不带认证的健康检查:"
        HEALTH_RESPONSE=$(curl -s -X GET "${BASE_URL}${HEALTH_ENDPOINT}")
        echo "响应: $HEALTH_RESPONSE"
        
        # 带认证的健康检查
        echo -e "\n测试带认证的健康检查:"
        HEALTH_AUTH_RESPONSE=$(curl -s -X GET "${BASE_URL}${HEALTH_ENDPOINT}" \
          -H "Authorization: Bearer $TOKEN")
        echo "响应: $HEALTH_AUTH_RESPONSE"
        
        # 3. 测试其他需要认证的API端点
        echo -e "\n${YELLOW}3. 测试其他API端点...${NC}"
        
        # 测试WebSocket信息接口
        echo "测试WebSocket信息接口:"
        WS_INFO_RESPONSE=$(curl -s -X GET "${BASE_URL}/api/script-chat/ws-info" \
          -H "Authorization: Bearer $TOKEN")
        echo "响应: $WS_INFO_RESPONSE"
        
        # 测试大模型聊天接口（如果存在）
        echo -e "\n测试大模型聊天接口:"
        CHAT_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/bigmodel/chat" \
          -H "Authorization: Bearer $TOKEN" \
          -H "Content-Type: application/x-www-form-urlencoded" \
          -d "prompt=你好")
        echo "响应: $CHAT_RESPONSE"
        
        echo -e "\n${GREEN}=== 测试完成 ===${NC}"
        echo -e "${YELLOW}JWT Token已获取，可用于后续API调用${NC}"
        echo -e "${YELLOW}Token: Bearer $TOKEN${NC}"
        
    else
        echo -e "${RED}❌ 未能从响应中提取Token${NC}"
    fi
    
else
    echo -e "${RED}❌ 登录失败${NC}"
    echo "请检查用户名密码是否正确，或查看应用日志"
fi

# 4. 提供手动测试命令
echo -e "\n${YELLOW}=== 手动测试命令示例 ===${NC}"
echo "如果上述自动测试成功，您可以使用以下命令进行手动测试："
echo ""
echo "# 1. 登录获取token"
echo "curl -X POST ${BASE_URL}${LOGIN_ENDPOINT} \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{"
echo "    \"identity\": \"admin\","
echo "    \"password\": \"123456\","
echo "    \"rememberMe\": false"
echo "  }'"
echo ""
echo "# 2. 使用token访问API（替换YOUR_TOKEN为实际token）"
echo "curl -X GET ${BASE_URL}/api/some-endpoint \\"
echo "  -H \"Authorization: Bearer YOUR_TOKEN\""
echo ""
echo "# 3. 健康检查"
echo "curl -X GET ${BASE_URL}${HEALTH_ENDPOINT}"
