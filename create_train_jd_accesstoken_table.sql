-- 创建京东访问令牌表
CREATE TABLE train_jd_accesstoken (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户ID',
  `team_id` bigint NOT NULL DEFAULT 0 COMMENT '团队ID,0代表系统',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  `access_token` varchar(255) NOT NULL COMMENT '访问令牌',
  `expires_time` datetime NOT NULL COMMENT '过期时间',
  `refresh_token` varchar(255) NOT NULL COMMENT '刷新令牌',
  `scope` varchar(255) NOT NULL COMMENT '权限范围',
  `xid` varchar(255) NOT NULL COMMENT '唯一标识',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_xid` (`xid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='京东访问令牌表';

-- 添加索引以提高查询性能
CREATE INDEX `idx_user_team` ON train_jd_accesstoken(`user_id`, `team_id`);
CREATE INDEX `idx_expires_time` ON train_jd_accesstoken(`expires_time`);
CREATE INDEX `idx_create_time` ON train_jd_accesstoken(`create_time`); 