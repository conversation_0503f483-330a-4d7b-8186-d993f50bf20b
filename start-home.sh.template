#!/bin/bash
# 启动home环境的脚本模板
# 复制为 start-home.sh 并填入真实的密码

echo "🚀 启动 yiyi_ai_playground (home环境)"
echo "📍 设置环境变量..."

# 设置敏感信息环境变量 - 请替换为真实值
export DB_PASSWORD="123456"
export REDIS_PASSWORD="123456"
export JWT_SECRET="gJj0XQdx9yhNY3sQpOPcHLYV7rWmb8A4KtIZ2EMi6onFk1qlaDSeuTvRfzG5wU"
export OSS_ACCESS_KEY="smybdcjqJAL4oL8qkL9qRESoYQfhv0"
export QDRANT_PASSWORD="qdrant"
export JD_APP_SECRET="dbff743f33fd4a46bfa3399cf189e252"
export JD_ACCESS_TOKEN="89fd9dcc03d34c6d997fc66e019700bcy2mw"

echo "✅ 环境变量设置完成"
echo "🏃 启动应用 (home profile)..."

# 启动Spring Boot应用
mvn spring-boot:run -Dspring-boot.run.profiles=home