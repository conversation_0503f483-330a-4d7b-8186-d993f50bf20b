---
type: "manual"
---

每个修改或者新增的功能都要写单元测试，只做service层的即可,需要复用原来的serviceTest.比如原来有个serviceA,这次新加了一个方法m1(),那么只需要在原来serviceTest添加testM1()即可。
禁止不复用测试类，禁止新加一个方法就新建一个测试类
单元测试放在/test同包名的目录下。比如类路径是com.yiyi.ai_train_playground.service.jd.impl,则测试类也要放在这个目录下，方便管理。
测试需要用springbootTest,不要用webmvc
测试环节，如果需要生成ddl语句，放在resource.db的同包名路径下。比如类路径是com.yiyi.ai_train_playground.service.jd.impl,则db存放的目录则是是resource.db.com.yiyi.ai_train_playground.service.jd.impl目录下。
不需要用insert生成测试数据
SQL必须写在XML文件里面，必须使用动态SQL，不允许写在JAVA类上面
不需要生成md说明文档，不要生成Html测试文件
项目用的springdoc-openapi-starter-webmvc-ui，来管理api文档
如果想测试应用是否启动成功，利用curl 来测试应用有没有启动成功