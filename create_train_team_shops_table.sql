-- 创建团队店铺表
CREATE TABLE `train_team_shops` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `team_id` bigint NOT NULL DEFAULT '0' COMMENT '团队ID,0代表系统',
  `shop_id` varchar(64) NOT NULL DEFAULT '1' COMMENT '店铺ID',
  `shop_type` int NOT NULL DEFAULT '0' COMMENT '店铺类型,0:京东，1：淘宝，2：抖店',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) NOT NULL DEFAULT '0' COMMENT '创建人',
  `updater` varchar(64) NOT NULL DEFAULT '0' COMMENT '更新人',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号（用于乐观锁）',
  `is_authorize` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否和远程店铺同步',
  `is_sync_complete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '商品同步是否已完成,0:未完成，1：同步中，2：已完成',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_team_shop_type` (`team_id`,`shop_id`,`shop_type`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='团队店铺表'; 