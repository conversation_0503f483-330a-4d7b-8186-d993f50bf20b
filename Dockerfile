# 使用官方 JDK21 基础镜像（Alpine版）
FROM eclipse-temurin:21-jdk-alpine

# 设置时区为上海
RUN apk add --no-cache tzdata && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 设置工作目录
WORKDIR /app

# 创建日志目录并赋予写权限
RUN mkdir -p /app/logs && chmod 777 /app/logs

# 复制 JAR 文件到容器
COPY target/*.jar app.jar

# 设置默认环境变量（可在运行时覆盖）
ENV DB_URL=************************************************************ \
    DB_USERNAME=root \
    DB_PASSWORD=123456 \
    # 固定容器内部端口
    SERVER_PORT=8888

# 暴露端口
EXPOSE ${SERVER_PORT}

# 以 root 用户运行（确保写权限）
USER root

# 启动应用并传入配置参数
ENTRYPOINT ["java", "-Dspring.datasource.url=${DB_URL}", \
            "-Dspring.datasource.username=${DB_USERNAME}", \
            "-Dspring.datasource.password=${DB_PASSWORD}", \
            "-Dserver.port=8888", \
            "-jar", "/app/app.jar"]

