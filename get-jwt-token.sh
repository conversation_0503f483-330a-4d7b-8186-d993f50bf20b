#!/bin/bash

# 简单的JWT Token获取脚本
# 用于快速获取JWT token进行API测试

BASE_URL="http://localhost:8081"

echo "正在获取JWT Token..."

# 发送登录请求
RESPONSE=$(curl -s -X POST "${BASE_URL}/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "identity": "admin",
    "password": "123456",
    "rememberMe": false
  }')

# 检查是否成功
if echo "$RESPONSE" | grep -q '"code":200'; then
    # 提取token
    TOKEN=$(echo "$RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$TOKEN" ]; then
        echo "✅ 登录成功！"
        echo ""
        echo "JWT Token:"
        echo "$TOKEN"
        echo ""
        echo "使用方法："
        echo "curl -X GET ${BASE_URL}/api/your-endpoint \\"
        echo "  -H \"Authorization: Bearer $TOKEN\""
        echo ""
        echo "测试健康检查："
        echo "curl -X GET ${BASE_URL}/actuator/health \\"
        echo "  -H \"Authorization: Bearer $TOKEN\""
    else
        echo "❌ 无法提取Token"
    fi
else
    echo "❌ 登录失败"
    echo "响应: $RESPONSE"
fi
