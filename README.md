# 模拟客服聊天系统

基于Spring Boot 3.5.0 + WebSocket + 豆包大模型的模拟客服训练系统，支持多个AI模拟客户与客服进行实时对话训练。

## 功能特性

### 核心功能
- **多模拟客户支持**: 支持N个大模型扮演的模拟客户同时在线
- **WebSocket实时通信**: 采用STOMP协议，支持JSON数据格式
- **会话管理**: BigModelManager统一管理所有会话和历史消息
- **流式响应**: 支持实时流式对话，提供进度显示
- **场景化训练**: 支持不同训练场景（试用、随机、正式）

### 大模型服务
- **多模型支持**: 
  - `ntns`: Normal模型非流式
  - `tns`: Thinking模型非流式  
  - `nts`: Normal模型流式
  - `ts`: Thinking模型流式
  - `tsOnce`: 独立的一次性流式调用
- **自建连接池**: 最大连接数100，优化性能
- **进度显示**: 流式响应包含实时进度信息

## 技术架构

### 后端技术栈
- Spring Boot 3.5.0
- WebSocket + STOMP
- MyBatis 3.0.3
- Druid 1.2.21
- JWT认证
- 豆包大模型API

### 前端技术栈
- HTML5 + CSS3 + JavaScript
- SockJS + STOMP.js
- 响应式设计

## 快速开始

### 1. 环境要求
- JDK 21+
- MySQL 8.0+
- Maven 3.6+

### 2. 配置数据库
```sql
-- 创建数据库
CREATE DATABASE yiyi_ai_db;

-- 创建必要的表（train_team, user等）
-- 具体SQL请参考项目中的数据库脚本
```

### 3. 配置环境变量
```bash
# 设置豆包API密钥
export ARK_API_KEY=your_doubao_api_key
```

### 4. 修改配置文件
编辑 `src/main/resources/application.yml`:
```yaml
spring:
  datasource:
    url: **************************************
    username: your_username
    password: your_password
```

### 5. 启动应用
```bash
mvn spring-boot:run
```

### 6. 访问系统
- 登录页面: http://localhost:8080/login.html
- 聊天页面: http://localhost:8080/chat.html

## 使用说明

### 登录流程
1. 访问登录页面
2. 选择训练场景：
   - `trialOne`: 试用剧本
   - `trialFour`: 随机剧本  
   - `formal`: 正式剧本
3. 输入JWT令牌（用于身份验证）
4. 可选输入客服ID（留空则从JWT获取）

### 聊天功能
1. 点击"添加新客户"创建模拟客户
2. 系统自动生成机器人角色和首条消息
3. 选择客户开始对话训练
4. 支持多个客户同时在线聊天

### WebSocket API

#### 初始化会话
```javascript
// 发送到 /app/init
{
  "sceneName": "trialOne",
  "servicerId": "optional_service_id", 
  "token": "jwt_token"
}

// 响应从 /topic/init
{
  "sessionId": "robot_id_service_id",
  "robotName": "试用剧本",
  "serviceName": "团队名称",
  "firstMessage": "你好，我想咨询一下产品..."
}
```

#### 发送消息
```javascript
// 发送到 /app/send
{
  "sessionId": "session_id",
  "message": "用户消息内容"
}

// 响应从 /topic/chat/{sessionId}
{
  "content": "AI回复内容",
  "progress": 85
}
```

## 系统架构

### 会话管理
- **BigModelManager**: 核心会话管理器
  - 管理所有会话ID（格式：机器人ID_客服ID）
  - 存储会话历史消息
  - 管理系统提示词
  - 支持Redis扩展（预留）

### 角色设定
- **模拟客户角色**（随机选择）:
  - 买洗碗机的客户
  - 买冰箱的客户  
  - 买电脑的客户
  - 买小米SU7的客户
- **系统提示词**: "你模拟一个客户，扮演成以下{角色}，和客服聊天"

### 数据流向
```
客户端 → WebSocket → ChatWebSocketController → BigModelManager → DoubaoBigModelService → 豆包API
```

## API接口

### REST API
- `POST /api/bigmodel/chat` - 非流式对话
- `POST /api/bigmodel/stream-chat` - 流式对话  
- `POST /api/bigmodel/image-chat` - 图像对话

### WebSocket端点
- 连接地址: `/ws`
- 消息前缀: `/app`
- 订阅前缀: `/topic`, `/queue`

## 配置说明

### 豆包模型配置
```yaml
my:
  doubao:
    think:
      model:
        name: doubao-1.5-thinking-pro-250415
    normal:
      model:
        name: doubao-1-5-pro-32k-250115
    estimateToken: 500  # 进度计算基准
```

### 连接池配置
- 最大连接数: 100
- 最大请求数: 100
- 每主机最大请求数: 50

## 开发说明

### 扩展新场景
1. 在 `SceneName` 枚举中添加新场景
2. 在 `BigModelManager.generateRobotName()` 中添加对应逻辑
3. 更新前端登录页面的选项

### 添加新角色
1. 修改 `BigModelManager.ROLES` 常量
2. 后期可从数据库动态加载

### Redis集成
代码中已预留Redis扩展点，搜索 `TODO redis` 注释进行集成。

## 故障排除

### 常见问题
1. **WebSocket连接失败**: 检查防火墙和代理设置
2. **大模型调用失败**: 验证ARK_API_KEY是否正确
3. **数据库连接失败**: 检查数据库配置和网络连接
4. **JWT解析失败**: 确认JWT格式和密钥配置

### 日志查看
```bash
# 查看应用日志
tail -f logs/application.log

# 查看实时日志
mvn spring-boot:run -Dspring-boot.run.arguments="--logging.level.com.yiyi.ai_train_playground=DEBUG"
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。 