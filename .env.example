# ========================================
# yiyi_ai_playground Environment Configuration
# ========================================
# This file serves as a template for environment variables.
# Copy this file to .env and set the appropriate values for your environment.
# DO NOT commit .env files to version control!

# ========================================
# DATABASE CONFIGURATION
# ========================================
# MySQL Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=yiyi_ai_db
DB_USERNAME=root
DB_PASSWORD=your_database_password_here

# ========================================
# REDIS CONFIGURATION  
# ========================================
# Redis Cache Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here
REDIS_DATABASE=0

# ========================================
# JWT SECURITY CONFIGURATION
# ========================================
# JWT Secret Key (MUST be changed in production!)
JWT_SECRET=your_jwt_secret_key_here_minimum_256_bits
JWT_EXPIRATION=86400000
JWT_REMEMBER_ME_EXPIRATION=2592000000

# ========================================
# AI MODEL CONFIGURATION
# ========================================
# Doubao AI Model API Key
ARK_API_KEY=your_ark_api_key_here

# ========================================
# ALIBABA CLOUD OSS CONFIGURATION
# ========================================
# Object Storage Service Configuration
OSS_ENDPOINT=oss-cn-shanghai.aliyuncs.com
OSS_ACCESS_ID=your_oss_access_id_here
OSS_ACCESS_KEY=your_oss_access_key_here
OSS_BUCKET_NAME=ai-playground
OSS_TEMP_FILE_EXPIRATION=24

# ========================================
# QDRANT VECTOR DATABASE CONFIGURATION
# ========================================
# Vector Database Configuration
QDRANT_HOST=localhost
QDRANT_HTTP_PORT=6333
QDRANT_GRPC_PORT=6334
QDRANT_USERNAME=qdrant
QDRANT_PASSWORD=your_qdrant_password_here
QDRANT_USE_TLS=false
QDRANT_API_KEY=

# ========================================
# JD (京东) API CONFIGURATION
# ========================================
# JingDong E-commerce API Configuration
JD_SERVER_URL=https://api.jd.com/routerjson
JD_EXPECTED_STATE=YyJdPlayground2025
JD_APP_KEY=your_jd_app_key_here
JD_APP_SECRET=your_jd_app_secret_here
JD_ACCESS_TOKEN=your_jd_access_token_here
JD_TOKEN_URL=https://open-oauth.jd.com/oauth2/access_token
JD_REDIRECT_BASE_URL=http://www.yiyiailocal.com:5173/home
JD_SYNC_PAGE_SIZE=10

# ========================================
# ENVIRONMENT-SPECIFIC NOTES
# ========================================
# 
# For development environment:
# - Use localhost for DB_HOST, REDIS_HOST, QDRANT_HOST
# - Use default passwords (but still set them for security awareness)
#
# For home/remote development environment:
# - Set DB_HOST=************** (or your remote server IP)
# - Set REDIS_HOST=************** 
# - Set QDRANT_HOST=**************
#
# For production environment:
# - NEVER use default passwords
# - Generate secure random JWT_SECRET (minimum 256 bits)
# - Use proper SSL/TLS configuration
# - Set QDRANT_USE_TLS=true in production
# - Regularly rotate API keys and secrets
#
# Security Best Practices:
# - Never commit actual credentials to version control
# - Use different credentials for each environment
# - Enable audit logging for credential access
# - Regularly rotate secrets and API keys
# - Monitor for unauthorized access attempts