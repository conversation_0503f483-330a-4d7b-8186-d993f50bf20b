# JdNewSyncService 实现文档

## 概述

根据用户需求，实现了新的京东商品同步服务 `JdNewSyncService`，该服务提供异步的商品数据同步功能，支持Mock机制和智能的数据更新策略。

## 核心功能

### 1. 异步同步方法

```java
@Async("taskExecutor")
void sync(String xid, String accessToken, Long teamId, String creator)
```

- 使用 `@Async("taskExecutor")` 注解实现异步执行
- 支持事务回滚 `@Transactional(rollbackFor = Exception.class)`
- 完整的参数验证和异常处理

### 2. Mock机制支持

根据 `jdConfig.getSync().getIsMockSwitch()` 配置决定数据源：

- **Mock开启**: 使用 `getTdFromJsonService.getTrainJdProductsFromJson()` 获取本地数据
- **Mock关闭**: 使用 `yiYiJdService.getWare4ValidProductList()` 分页获取远程数据

### 3. 智能分页获取

- 从 `JdConfig` 获取 `pageSize` 配置
- 循环调用远程接口直到返回数据量小于 `pageSize`
- 自动停止分页，避免无效请求

### 4. 数据处理策略

#### 4.1 新商品插入
- 生成 `introduction` 的MD5签名存储在 `descSignature` 字段
- 设置 `syncStatus` 为 `JdSyncStatus.UN_SYNC.getCode()`
- 提取图片URL并批量插入到 `train_jd_prod_images` 表

#### 4.2 现有商品更新
- 比较 `rmtJdProduct.getModified()` 和 `existingLocalProduct.getModified()`
- 只有远程数据更新时才执行更新操作
- 比较MD5签名，如果描述内容变更则更新图片数据

### 5. 图片处理

- 使用 `ResolveUtil.extractImageSources()` 提取HTML中的图片URL
- 批量插入图片记录到 `train_jd_prod_images` 表
- 设置图片的 `syncStatus` 为 `JdSyncStatus.UN_SYNC.getCode()`

## 实现组件

### 1. 核心类

#### JdNewSyncService.java (接口)
```java
public interface JdNewSyncService {
    void sync(String xid, String accessToken, Long teamId, String creator);
}
```

#### JdNewSyncServiceImpl.java (实现类)
主要方法：
- `sync()` - 主同步方法
- `getRemoteJdProdList()` - 获取远程商品列表
- `processRemoteJdProdList()` - 处理商品列表
- `insertNewProduct()` - 插入新商品
- `updateExistingProduct()` - 更新现有商品
- `extractAndInsertImages()` - 提取并插入图片

#### Md5Util.java (工具类)
提供MD5摘要生成和验证功能：
- `generateMd5(String input)` - 生成MD5摘要
- `verifyMd5(String input, String md5Hash)` - 验证MD5摘要
- `compareMd5(String md5Hash1, String md5Hash2)` - 比较MD5摘要

### 2. 依赖服务

- `JdConfig` - 获取配置信息
- `GetTdFromJsonService` - 获取本地Mock数据
- `YiYiJdService` - 获取远程API数据
- `TrainJdProductsMapper` - 商品表操作
- `TrainJdProdImagesMapper` - 图片表操作
- `TrainJdProdImagesService` - 图片服务

## 配置要求

### application.yml 配置示例

```yaml
jd:
  sync:
    page-size: 10                    # 分页大小
    is-mock-switch: false            # Mock开关，true=使用本地数据，false=使用远程API
```

## 数据库字段

### TrainJdProducts 表
- `desc_signature` - introduction的MD5签名
- `sync_status` - 同步状态（0=未同步，1=同步中，2=已同步）

### train_jd_prod_images 表
- `team_id` - 团队ID
- `jd_prod_id` - 商品ID
- `img_url` - 图片URL
- `sync_status` - 同步状态

## 使用示例

```java
@Autowired
private JdNewSyncService jdNewSyncService;

// 异步同步商品数据
jdNewSyncService.sync("xid-001", "access-token", 1L, "admin");
```

## 测试

### 单元测试
- `JdNewSyncServiceImplTest` - 服务层测试
- `Md5UtilTest` - MD5工具类测试

### 测试运行
```bash
mvn test -Dtest=Md5UtilTest
mvn test -Dtest=JdNewSyncServiceImplTest
```

## 特性

1. **异步执行**: 使用Spring的异步机制，不阻塞主线程
2. **事务支持**: 完整的事务回滚机制
3. **Mock支持**: 开发和测试环境可使用本地数据
4. **智能更新**: 基于时间戳和MD5签名的智能更新策略
5. **批量操作**: 图片数据批量插入，提高性能
6. **异常处理**: 完善的异常处理和日志记录
7. **单一职责**: 每个方法职责单一，代码清晰易维护

## 注意事项

1. 确保 `taskExecutor` 线程池已正确配置
2. Mock数据文件路径需要正确配置
3. 数据库表结构需要包含新增的字段
4. 建议在测试环境开启Mock开关以避免频繁调用远程API
