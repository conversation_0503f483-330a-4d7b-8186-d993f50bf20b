# GetTdFromJsonServiceImpl 日期字段解析修复

## 问题描述

在 `GetTdFromJsonServiceImpl.convertMapToTrainJdProducts` 方法中，缺少了对 `online_time` 和 `offline_time` 字段的解析，导致数据库中这两个字段为 Null。

同时，该方法也缺少了对 `created` 和 `modified` 字段的解析。

## 修复内容

### 1. 添加日期解析辅助方法

在 `GetTdFromJsonServiceImpl` 类中添加了 `parseLocalDateTimeFromString` 方法：

```java
/**
 * 辅助方法：解析日期字符串为LocalDateTime
 */
private LocalDateTime parseLocalDateTimeFromString(String dateStr) {
    if (dateStr == null || dateStr.trim().isEmpty()) {
        return null;
    }

    // 支持多种日期格式
    String[] dateFormats = {
        "yyyy-MM-dd HH:mm:ss",  // 2025-04-02 10:30:00
        "yyyy-MM-dd",           // 2025-04-02
        "yyyy/MM/dd HH:mm:ss",  // 2025/04/02 10:30:00
        "yyyy/MM/dd"            // 2025/04/02
    };

    for (String format : dateFormats) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            LocalDateTime result;
            if (format.contains("HH:mm:ss")) {
                result = LocalDateTime.parse(dateStr, formatter);
            } else {
                // 如果只有日期，设置时间为00:00:00
                result = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(format)).atStartOfDay();
            }
            return result;
        } catch (DateTimeParseException e) {
            // 继续尝试下一种格式
        }
    }

    log.warn("无法解析日期字符串: '{}'", dateStr);
    return null;
}
```

### 2. 修改 convertMapToTrainJdProducts 方法

在 `convertMapToTrainJdProducts` 方法中添加了对四个日期字段的解析：

```java
// 处理日期字段
product.setCreated(parseLocalDateTimeFromString(getStringValue(rawData, "created")));
product.setModified(parseLocalDateTimeFromString(getStringValue(rawData, "modified")));
product.setOnlineTime(parseLocalDateTimeFromString(getStringValue(rawData, "online_time")));
product.setOfflineTime(parseLocalDateTimeFromString(getStringValue(rawData, "offline_time")));
```

### 3. 添加必要的 import 语句

```java
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
```

## 支持的日期格式

修复后的方法支持以下日期格式：

1. `yyyy-MM-dd HH:mm:ss` - 完整日期时间格式（如：2025-01-15 10:30:00）
2. `yyyy-MM-dd` - 仅日期格式（如：2025-01-15，时间设置为 00:00:00）
3. `yyyy/MM/dd HH:mm:ss` - 斜杠分隔的完整日期时间格式
4. `yyyy/MM/dd` - 斜杠分隔的仅日期格式

## 测试验证

添加了测试方法 `testConvertMapToTrainJdProducts_DateFields` 来验证日期字段的解析功能：

```java
@Test
@DisplayName("测试convertMapToTrainJdProducts方法 - 验证日期字段解析")
void testConvertMapToTrainJdProducts_DateFields() throws Exception {
    // 准备测试数据
    Map<String, Object> rawData = new HashMap<>();
    rawData.put("created", "2025-01-15 10:30:00");
    rawData.put("modified", "2025-01-16 15:45:30");
    rawData.put("online_time", "2025-01-10 09:00:00");
    rawData.put("offline_time", "2025-12-31 23:59:59");

    // 执行测试并验证结果
    TrainJdProducts result = convertMapToTrainJdProducts(rawData);
    
    // 验证日期字段不为null且值正确
    assertThat(result.getCreated()).isEqualTo(LocalDateTime.of(2025, 1, 15, 10, 30, 0));
    assertThat(result.getModified()).isEqualTo(LocalDateTime.of(2025, 1, 16, 15, 45, 30));
    assertThat(result.getOnlineTime()).isEqualTo(LocalDateTime.of(2025, 1, 10, 9, 0, 0));
    assertThat(result.getOfflineTime()).isEqualTo(LocalDateTime.of(2025, 12, 31, 23, 59, 59));
}
```

## 影响的字段

修复后，以下字段将能够正确从 JSON 数据中解析并存储到数据库：

- `created` - 京东商品创建时间
- `modified` - 京东商品修改时间
- `online_time` - 商品上线时间
- `offline_time` - 商品下线时间

## 数据库表结构

这些字段对应 `train_jd_products` 表中的以下列：

- `created` → `created` (DATETIME)
- `modified` → `modified` (DATETIME)
- `online_time` → `online_time` (DATETIME)
- `offline_time` → `offline_time` (DATETIME)

## 测试结果

测试通过，日志显示：

```
2025-07-17 17:43:54.894 [main] INFO  c.y.a.s.j.i.GetTdFromJsonServiceImplTest - 日期字段解析测试通过 - created: 2025-01-15T10:30, modified: 2025-01-16T15:45:30, onlineTime: 2025-01-10T09:00, offlineTime: 2025-12-31T23:59:59
```

## 总结

此次修复解决了 `GetTdFromJsonServiceImpl.convertMapToTrainJdProducts` 方法中缺少日期字段解析的问题，确保了 `online_time`、`offline_time`、`created` 和 `modified` 字段能够正确从 JSON 数据中解析并存储到数据库中，避免了这些字段在数据库中为 Null 的问题。
