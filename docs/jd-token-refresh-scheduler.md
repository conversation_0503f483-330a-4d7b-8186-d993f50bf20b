# 京东Token刷新定时任务

## 功能概述

本功能实现了一个定时任务，用于自动刷新京东访问令牌（access token），确保与京东API的连接始终有效。

## 核心组件

### 1. JdTokenRefreshScheduler
- **位置**: `src/main/java/com/yiyi/ai_train_playground/scheduler/JdTokenRefreshScheduler.java`
- **功能**: 定时任务调度器，每60分钟执行一次token刷新
- **特性**: 
  - 使用Redis分布式锁确保同一时刻只有一个实例执行
  - 自动处理锁的获取和释放
  - 详细的日志记录

### 2. JdTokenRefreshService
- **位置**: `src/main/java/com/yiyi/ai_train_playground/service/jd/impl/JdTokenRefreshServiceImpl.java`
- **功能**: 核心业务逻辑，负责刷新所有京东访问令牌
- **流程**:
  1. 从数据库加载所有访问令牌记录
  2. 逐个调用京东refresh token API
  3. 更新数据库中的令牌信息

### 3. RedisDistributedLockService
- **位置**: `src/main/java/com/yiyi/ai_train_playground/service/RedisDistributedLockService.java`
- **功能**: Redis分布式锁实现
- **特性**:
  - 使用Redis SET NX EX命令实现原子性锁操作
  - Lua脚本确保锁释放的原子性
  - 支持锁过期时间设置

### 4. TrainJdAccessTokenMapper扩展
- **位置**: `src/main/java/com/yiyi/ai_train_playground/mapper/TrainJdAccessTokenMapper.java`
- **新增方法**:
  - `findAll()`: 查询所有访问令牌记录
  - `updateTokenInfo()`: 更新令牌信息

## 配置说明

### 定时任务配置
在所有`application*.yml`文件中添加了定时任务配置：

```yaml
spring:
  task:
    scheduling:
      enabled: true # 定时任务总开关，默认启用
      pool:
        size: 5 # 定时任务线程池大小
      thread-name-prefix: "scheduler-" # 线程名前缀
```

**定时任务开关说明**：
- `spring.task.scheduling.enabled`: 定时任务总开关
  - `true`: 启用定时任务（默认值）
  - `false`: 禁用定时任务，定时任务Bean不会被创建
- 支持环境变量：`SPRING_TASK_SCHEDULING_ENABLED`
- 测试环境默认禁用：`application-test.yml`中设置为`false`

### 京东API配置
使用现有的JdConfig配置：
- `app-key`: 京东应用Key
- `app-secret`: 京东应用Secret
- `token-url`: 京东令牌获取URL（用于获取access token）
- `refresh-token-url`: 京东令牌刷新URL（专门用于刷新token）

**新增配置项**：
```yaml
jd:
  refresh-token-url: ${JD_REFRESH_TOKEN_URL:https://open-oauth.jd.com/oauth2/refresh_token}
```

### Token刷新配置（可配置化）
在`application*.yml`中新增的配置项：

```yaml
jd:
  token-refresh:
    interval-minutes: 60  # Token刷新间隔时间（分钟），默认60分钟
    lock-expire-minutes: 50  # 分布式锁过期时间（分钟），默认50分钟
```

**配置说明**：
- `interval-minutes`: 定时任务执行间隔，支持环境变量`JD_TOKEN_REFRESH_INTERVAL_MINUTES`
- `lock-expire-minutes`: 分布式锁过期时间，支持环境变量`JD_TOKEN_REFRESH_LOCK_EXPIRE_MINUTES`
- 建议锁过期时间略小于执行间隔，确保在下次任务执行前释放

### Redis配置
使用现有的Redis配置，分布式锁相关参数：
- 锁键名: `jd:token:refresh:lock`
- 锁过期时间: 从配置文件读取（默认50分钟）

## 执行流程

1. **定时触发**: 根据配置文件中的`interval-minutes`执行（默认60分钟）
2. **获取分布式锁**: 尝试获取Redis分布式锁，过期时间从配置文件读取
3. **执行刷新任务**:
   - 查询所有访问令牌记录
   - 逐个调用京东refresh token API
   - 更新数据库中的令牌信息
4. **释放分布式锁**: 任务完成后释放锁

## API调用详情

### 京东refresh token API
- **URL**: `https://open-oauth.jd.com/oauth2/refresh_token`
- **参数**:
  - `app_key`: 从JdConfig获取
  - `app_secret`: 从JdConfig获取
  - `grant_type`: 固定值"refresh_token"
  - `refresh_token`: 从数据库记录获取

### 响应处理
- 解析返回的`access_token`、`expires_in`、`refresh_token`、`scope`
- 更新数据库中对应的记录
- 设置更新人为"system"

## 错误处理

1. **API调用失败**: 记录错误日志，继续处理下一个令牌
2. **数据库更新失败**: 记录错误日志，继续处理下一个令牌
3. **分布式锁获取失败**: 跳过本次执行，等待下次调度
4. **异常处理**: 确保分布式锁在finally块中释放

## 监控和日志

### 日志级别
- INFO: 任务开始/结束、成功刷新的令牌数量
- DEBUG: 分布式锁操作详情
- ERROR: API调用失败、数据库操作失败等错误

### 关键日志示例
```
京东Token刷新定时任务开始执行，尝试获取分布式锁
成功获取分布式锁，开始执行京东Token刷新任务
找到 X 个京东访问令牌记录，开始逐个刷新
成功刷新京东访问令牌: id=X, xid=XXX, teamId=X
京东Token刷新定时任务执行完成，成功刷新 X 个令牌
```

## 测试

### 单元测试
- `RedisDistributedLockServiceTest`: 测试分布式锁功能
- `JdTokenRefreshServiceTest`: 测试令牌刷新逻辑

### 测试覆盖
- 正常刷新流程
- 部分失败场景
- 空令牌列表处理
- 并发锁竞争

## 部署注意事项

1. **Redis连接**: 确保Redis服务正常运行
2. **网络连接**: 确保能够访问京东API
3. **数据库权限**: 确保有读写train_jd_accesstoken表的权限
4. **多实例部署**: 分布式锁确保多实例环境下的安全执行

## 配置调整建议

### 生产环境
```yaml
jd:
  token-refresh:
    interval-minutes: 60  # 1小时刷新一次，平衡及时性和API调用频率
    lock-expire-minutes: 50  # 确保在下次执行前释放锁
```

### 开发/测试环境
```yaml
spring:
  task:
    scheduling:
      enabled: false  # 测试环境禁用定时任务

jd:
  token-refresh:
    interval-minutes: 5   # 5分钟刷新一次，便于测试
    lock-expire-minutes: 4   # 相应调整锁过期时间
```

### 高频场景
```yaml
jd:
  token-refresh:
    interval-minutes: 30  # 30分钟刷新一次，提高令牌新鲜度
    lock-expire-minutes: 25  # 相应调整锁过期时间
```

## 🧪 测试验证

### 单元测试
项目包含完整的定时任务开关功能测试：

1. **SchedulerSwitchTest**: 测试定时任务禁用功能
   - 验证当`spring.task.scheduling.enabled=false`时，定时任务Bean不被创建

2. **SchedulerEnabledTest**: 测试定时任务启用功能
   - 验证当`spring.task.scheduling.enabled=true`时，定时任务Bean被正确创建
   - 验证定时任务能够正常执行

### 运行测试
```bash
# 测试定时任务禁用功能
mvn test -Dtest=SchedulerSwitchTest

# 测试定时任务启用功能
mvn test -Dtest=SchedulerEnabledTest

# 运行所有定时任务相关测试
mvn test -Dtest="*Scheduler*Test"
```

## 维护建议

1. **监控令牌过期**: 定期检查令牌是否正常刷新
2. **日志监控**: 关注ERROR级别日志，及时处理异常
3. **性能监控**: 监控任务执行时间，必要时调整线程池配置
4. **API限制**: 注意京东API的调用频率限制
5. **配置调优**: 根据业务需求调整刷新间隔，平衡及时性和资源消耗

---

**最后更新**: 2025-07-17
**维护者**: AI训练平台开发团队
