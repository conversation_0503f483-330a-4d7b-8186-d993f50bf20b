# 剧本批量删除功能实现文档

## 功能概述

实现了剧本批量删除接口，支持通过逗号分隔的ID列表一次性删除多个剧本。

## API 接口

### 请求信息
- **请求路径**: `/api/scripts`
- **请求方式**: `DELETE`
- **请求参数**: `ids` (查询参数，逗号分隔的剧本ID列表)
- **请求样例**: `/api/scripts?ids=1,2,3`

### 响应格式
```json
{
  "code": 1,
  "message": "success",
  "data": null
}
```

## 实现层次

### 1. Controller层 (TrainScriptController)
- **方法**: `deleteScript(@RequestParam("ids") String ids, HttpServletRequest request)`
- **功能**: 
  - 从JWT token中提取团队ID
  - 调用服务层批量删除方法
  - 返回统一的响应格式

### 2. Service层 (TrainScriptService)
- **接口方法**: `boolean batchDeleteScripts(String ids, Long teamId)`
- **实现类**: `TrainScriptServiceImpl`
- **功能**:
  - 输入验证（ids不能为空，teamId不能为空）
  - 字符串解析（将逗号分隔的ID字符串转换为Long列表）
  - 数字格式验证
  - 调用Mapper层执行批量删除

### 3. Mapper层 (TrainScriptMapper)
- **方法**: `int batchDeleteByIds(@Param("ids") List<Long> ids, @Param("teamId") Long teamId)`
- **SQL实现**: 使用MyBatis的foreach循环构建IN子句

## 核心代码实现

### Service层实现
```java
@Override
@Transactional
public boolean batchDeleteScripts(String ids, Long teamId) {
    log.info("批量删除剧本：ids={}, teamId={}", ids, teamId);
    
    // 输入验证
    if (ids == null || ids.trim().isEmpty()) {
        throw new RuntimeException("剧本ID列表不能为空");
    }
    
    if (teamId == null) {
        throw new RuntimeException("团队ID不能为空");
    }
    
    // 解析ID字符串为Long列表
    List<Long> idList = new ArrayList<>();
    try {
        String[] idArray = ids.split(",");
        for (String idStr : idArray) {
            idStr = idStr.trim();
            if (!idStr.isEmpty()) {
                idList.add(Long.parseLong(idStr));
            }
        }
    } catch (NumberFormatException e) {
        throw new RuntimeException("剧本ID格式不正确：" + ids);
    }
    
    if (idList.isEmpty()) {
        throw new RuntimeException("没有有效的剧本ID");
    }
    
    // 批量删除剧本
    int result = trainScriptMapper.batchDeleteByIds(idList, teamId);
    return result > 0;
}
```

### Mapper XML实现
```xml
<!-- 批量删除剧本 -->
<delete id="batchDeleteByIds">
    DELETE FROM train_script 
    WHERE team_id = #{teamId}
    <if test="ids != null and ids.size() > 0">
        AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </if>
</delete>
```

## 安全特性

1. **团队隔离**: 只能删除属于当前团队的剧本
2. **JWT认证**: 通过JWT token验证用户身份和团队归属
3. **输入验证**: 严格验证输入参数格式和有效性
4. **事务管理**: 使用@Transactional确保操作的原子性

## 错误处理

1. **空ID列表**: 返回"剧本ID列表不能为空"
2. **无效ID格式**: 返回"剧本ID格式不正确：{ids}"
3. **空团队ID**: 返回"团队ID不能为空"
4. **JWT验证失败**: 返回"无法获取团队信息"

## 测试覆盖

### 服务层测试
- ✅ 成功批量删除测试
- ✅ 空ID列表异常测试
- ✅ 无效ID格式异常测试
- ✅ 空团队ID异常测试

### 集成测试
- ✅ 端到端批量删除流程测试
- ✅ 各种异常情况验证测试

## 性能考虑

1. **批量操作**: 使用SQL IN子句一次性删除多条记录，避免循环单条删除
2. **参数绑定**: 使用MyBatis参数绑定防止SQL注入
3. **事务管理**: 确保批量操作的原子性

## 使用示例

```bash
# 删除单个剧本
DELETE /api/scripts?ids=1

# 删除多个剧本
DELETE /api/scripts?ids=1,2,3,4,5

# 请求头需要包含JWT token
Authorization: Bearer <your-jwt-token>
```

## 注意事项

1. 删除操作不可逆，请谨慎使用
2. 确保传入的ID都是有效的剧本ID
3. 只能删除当前团队的剧本
4. 建议在前端添加确认对话框
