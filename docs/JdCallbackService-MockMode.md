# JdCallbackService Mock 模式使用说明

## 概述

`JdCallbackServiceImpl` 现在支持 Mock 模式，允许在开发和测试环境中使用模拟数据，而不需要调用真实的京东 API。

## 功能特性

### 1. Mock 模式开关

通过配置文件控制是否启用 Mock 模式：

```yaml
jd:
  sync:
    is-mock-switch: true  # 启用Mock模式
```

或通过环境变量：

```bash
JD_SYNC_IS_MOCK_SWITCH=true
```

### 2. Mock 数据生成策略

Mock 模式支持两种数据生成策略：

#### 策略1：使用现有 Token 数据
- 如果数据库中存在对应团队的 Mock Token（xid 格式：`mock_xid_{teamId}`）
- 系统会使用现有的 Token 数据构造响应
- 保持数据的一致性和可预测性

#### 策略2：生成新的 Mock 数据
- 如果数据库中不存在对应的 Mock Token
- 系统会生成新的模拟数据
- 包含时间戳确保唯一性

## 使用场景

### 开发环境
```yaml
# application-dev.yml
jd:
  sync:
    is-mock-switch: true
```

### 测试环境
```yaml
# application-test.yml
jd:
  sync:
    is-mock-switch: true
```

### 生产环境
```yaml
# application-prod.yml
jd:
  sync:
    is-mock-switch: false  # 使用真实API
```

## Mock 数据格式

Mock 模式生成的响应数据格式：

```json
{
  "access_token": "existing_mock_token" | "mock_access_token_{timestamp}",
  "expires_in": 7200,
  "refresh_token": "existing_refresh_token" | "mock_refresh_token_{timestamp}",
  "scope": "read,write",
  "xid": "mock_xid_{teamId}"
}
```

## 日志输出

启用 Mock 模式时，系统会输出相关日志：

```
INFO - Mock模式已启用，构造假的token响应数据
INFO - 使用现有token数据构造Mock响应，xid: mock_xid_1
INFO - Mock响应数据构造完成: {access_token=..., ...}
```

## 优势

1. **开发效率**：无需依赖外部 API，加快开发速度
2. **测试稳定性**：避免网络问题和 API 限制影响测试
3. **数据一致性**：可重复使用相同的测试数据
4. **成本控制**：减少对外部 API 的调用次数

## 注意事项

1. Mock 模式仅影响 Token 获取阶段，不影响后续的业务逻辑
2. 确保在生产环境中关闭 Mock 模式
3. Mock 数据的 `xid` 格式固定为 `mock_xid_{teamId}`
4. 建议在测试中预先准备好 Mock Token 数据以确保测试的可重复性

## 配置示例

### 完整配置示例

```yaml
jd:
  server-url: https://api.jd.com/routerjson
  expected-state: YyJdPlayground2025
  app-key: your-app-key
  app-secret: your-app-secret
  token-url: https://open-oauth.jd.com/oauth2/access_token
  sync:
    page-size: 10
    is-mock-switch: true  # 启用Mock模式
```

### 环境变量配置

```bash
# 启用Mock模式
export JD_SYNC_IS_MOCK_SWITCH=true

# 或在Docker中
docker run -e JD_SYNC_IS_MOCK_SWITCH=true your-app
```
