# JdNewSyncService 删除图片向量功能增强

## 概述

根据用户需求，在 `JdNewSyncServiceImpl.updateExistingProduct` 方法中新增了删除图片向量的功能。当商品描述发生变更需要更新图片时，现在会先删除旧的向量数据，然后再插入新图片，确保向量数据的一致性。

## 修改内容

### 1. 依赖注入增强

#### 新增依赖
```java
private final VectorSearchService vectorSearchService;
```

#### 新增导入
```java
import com.yiyi.ai_train_playground.service.VectorSearchService;
```

### 2. updateExistingProduct方法增强

#### 原有TODO注释位置
```java
//TODO 此处向量要删除，忘删了,可以考虑先用列表输入进去，然后遍历列表
```

#### 新增向量删除调用
在TODO注释下方添加了向量删除逻辑：
```java
// 删除旧的向量数据
deleteImgVectors(introduction, teamId, rmtJdProduct, creator);
```

### 3. 新增核心方法

#### deleteImgVectors私有方法
```java
/**
 * 删除图片向量数据
 * 
 * @param introduction 商品介绍HTML内容
 * @param teamId 团队ID
 * @param rmtJdProduct 远程商品数据
 * @param creator 创建者
 */
private void deleteImgVectors(String introduction, Long teamId, TrainJdProducts rmtJdProduct, String creator)
```

**主要逻辑：**
1. 提取图片URL列表：`List<String> imgList = ResolveUtil.extractImageSources(introduction)`
2. 遍历图片URL列表
3. 调用向量删除服务：`vectorSearchService.deleteVectorForWare(rmtJdProduct, imgUrl, teamId)`

## 核心实现逻辑

### 1. 图片URL提取
```java
// 1. 提取图片URL列表
List<String> imgList = ResolveUtil.extractImageSources(introduction);

if (imgList == null || imgList.isEmpty()) {
    log.info("未找到图片URL，无需删除向量，wareId: {}", rmtJdProduct.getWareId());
    return;
}
```

### 2. 遍历删除向量
```java
// 2. 遍历图片URL列表，删除对应的向量
for (String imgUrl : imgList) {
    try {
        if (imgUrl == null || imgUrl.trim().isEmpty()) {
            log.warn("图片URL为空，跳过删除，wareId: {}", rmtJdProduct.getWareId());
            continue;
        }
        
        // 3. 调用VectorSearchService删除向量
        boolean deleted = vectorSearchService.deleteVectorForWare(rmtJdProduct, imgUrl, teamId);
        
        if (deleted) {
            deletedCount++;
            log.debug("成功删除图片向量，wareId: {}, imgUrl: {}", rmtJdProduct.getWareId(), imgUrl);
        } else {
            failedCount++;
            log.warn("删除图片向量失败，wareId: {}, imgUrl: {}", rmtJdProduct.getWareId(), imgUrl);
        }
        
    } catch (Exception e) {
        failedCount++;
        log.error("删除图片向量异常，wareId: {}, imgUrl: {}", rmtJdProduct.getWareId(), imgUrl, e);
    }
}
```

### 3. 完整的更新流程
```java
// 比较签名
if (!Md5Util.compareMd5(currentDescSignature, newDescSignature)) {
    log.debug("商品描述已变更，需要更新图片，wareId: {}", rmtJdProduct.getWareId());

    // 删除旧图片
    trainJdProdImagesMapper.deleteByTeamIdAndJdProdId(teamId, existingLocalProduct.getId());

    // 删除旧的向量数据 ✅ 新增
    deleteImgVectors(introduction, teamId, rmtJdProduct, creator);

    // 插入新图片
    extractAndInsertImages(introduction, teamId, existingLocalProduct.getId(), creator);

    // 更新签名
    currentDescSignature = newDescSignature;
}
```

## 特性

### 1. 完全按照用户需求实现
✅ **在指定位置添加**: 在TODO注释下方添加了deleteImgVectors方法调用
✅ **提取图片URL列表**: 使用 `ResolveUtil.extractImageSources(introduction)`
✅ **遍历列表**: 遍历每个imgUrl
✅ **调用向量删除**: 调用 `vectorSearchService.deleteVectorForWare(rmtJdProduct, imgUrl, teamId)`

### 2. 智能处理策略
- **空HTML内容**: 如果没有图片URL，直接返回
- **空图片URL**: 跳过空的图片URL，继续处理其他图片
- **异常隔离**: 单个图片向量删除失败不影响其他图片的处理
- **不影响主流程**: 向量删除异常不会中断商品更新流程

### 3. 完善的日志记录
- 记录删除开始和结果
- 记录找到的图片URL数量
- 记录每个图片向量的删除状态
- 记录成功、失败的统计信息
- 记录异常信息

### 4. 性能优化
- 批量处理图片URL
- 异常隔离保证整体流程稳定
- 详细的统计信息便于监控

## 使用场景

### 商品更新流程
1. **检查商品是否需要更新**: 比较modified字段
2. **检查描述是否变更**: 比较MD5签名
3. **描述变更时的处理**:
   - 删除旧图片记录（数据库）
   - **删除旧向量数据（新增）** ✅
   - 插入新图片记录
   - 更新商品信息

### 向量数据一致性
- **问题**: 之前只删除了数据库中的图片记录，但向量数据库中的向量没有删除
- **解决**: 现在会同时删除向量数据库中对应的向量
- **效果**: 确保数据库和向量数据库的数据一致性

## 测试

### 单元测试类
`JdNewSyncServiceDeleteVectorTest.java`

#### 测试用例
1. **deleteImgVectors私有方法** (`testDeleteImgVectors`)
   - 测试私有方法的调用链
   - 验证图片URL提取和向量删除逻辑

2. **图片URL提取功能** (`testImageUrlExtraction`)
   - 测试多种图片格式的URL提取
   - 验证ResolveUtil.extractImageSources方法

3. **空HTML内容的处理** (`testEmptyHtmlContent`)
   - 测试null、空字符串、无图片HTML的处理
   - 验证异常处理逻辑

4. **向量删除的性能** (`testVectorDeletionPerformance`)
   - 测试大量图片的向量删除性能
   - 验证批量处理能力

5. **不同团队ID的向量删除** (`testVectorDeletionWithDifferentTeamIds`)
   - 测试不同团队的向量删除隔离
   - 验证多租户支持

### 测试结果
```
Tests run: 5, Failures: 0, Errors: 0, Skipped: 0
```

## 方法调用链

### updateExistingProduct方法中的调用
```java
updateExistingProduct(rmtJdProduct, existingLocalProduct, teamId, creator)
  └── deleteImgVectors(introduction, teamId, rmtJdProduct, creator)
      ├── ResolveUtil.extractImageSources(introduction)
      └── vectorSearchService.deleteVectorForWare(rmtJdProduct, imgUrl, teamId)
```

### 完整的商品更新流程
```java
1. 检查商品是否需要更新 (modified字段比较)
2. 生成新的描述签名 (MD5)
3. 比较描述签名
4. 如果描述变更:
   a. 删除旧图片记录 (trainJdProdImagesMapper.deleteByTeamIdAndJdProdId)
   b. 删除旧向量数据 (deleteImgVectors) ✅ 新增
   c. 插入新图片记录 (extractAndInsertImages)
   d. 更新签名
5. 更新商品主表
```

## 配置要求

### 依赖注入
确保VectorSearchService已正确配置并可注入：
```java
@Autowired
private VectorSearchService vectorSearchService;
```

### 向量数据库配置
确保Qdrant向量数据库配置正确，支持deleteVectorForWare操作。

## 注意事项

1. **数据一致性**: 确保向量删除和图片记录删除的一致性
2. **异常处理**: 向量删除失败不会中断商品更新流程
3. **性能考虑**: 大量图片的向量删除可能需要时间
4. **日志监控**: 通过日志监控向量删除的成功率
5. **幂等性**: 多次调用相同参数的删除操作是安全的

## 兼容性

- 新增功能不影响现有商品更新逻辑
- 完全向后兼容
- 如果VectorSearchService不可用，会记录异常但不中断流程

## 解决的问题

### 原问题
```java
//TODO 此处向量要删除，忘删了,可以考虑先用列表输入进去，然后遍历列表
```

### 解决方案
1. ✅ 提取图片URL列表
2. ✅ 遍历列表
3. ✅ 调用向量删除服务
4. ✅ 完善的异常处理和日志记录
5. ✅ 不影响主流程的稳定性

现在商品描述更新时，会正确地删除旧的向量数据，确保数据一致性。
