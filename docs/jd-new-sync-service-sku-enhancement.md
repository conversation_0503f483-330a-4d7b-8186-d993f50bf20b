# JdNewSyncService SKU同步功能增强

## 概述

根据用户需求，在 `JdNewSyncService.sync` 方法中新增了SKU同步功能。现在每个商品同步完成后，会自动同步该商品的SKU数据，实现商品和SKU的一体化同步。

## 修改内容

### 1. 依赖注入增强

#### 新增依赖
```java
private final GetSkuFromJsonService getSkuFromJsonService;
private final TrainJdSkuMapper trainJdSkuMapper;
```

#### 新增导入
```java
import com.yiyi.ai_train_playground.entity.TrainJdSku;
import com.yiyi.ai_train_playground.mapper.TrainJdSkuMapper;
import com.yiyi.ai_train_playground.service.jd.GetSkuFromJsonService;
```

### 2. sync方法增强

#### 修改方法签名
```java
// 原方法签名
private void processRemoteJdProdList(List<TrainJdProducts> remoteJdProdList, Long teamId, String creator)

// 新方法签名
private void processRemoteJdProdList(List<TrainJdProducts> remoteJdProdList, String accessToken, Long teamId, String creator)
```

#### 新增SKU同步调用
在每个商品处理完成后，添加SKU同步：
```java
// 同步该商品的SKU数据
insertOrUpdateNewSku(accessToken, rmtJdProduct.getWareId(), teamId, creator);
```

### 3. 新增核心方法

#### insertOrUpdateNewSku方法
```java
/**
 * 插入或更新SKU数据
 * 
 * @param accessToken 访问令牌
 * @param wareId 商品ID
 * @param teamId 团队ID
 * @param creator 创建者
 */
private void insertOrUpdateNewSku(String accessToken, Long wareId, Long teamId, String creator)
```

**主要逻辑：**
1. 调用 `getRemoteSkuList` 获取远程SKU列表
2. 遍历每个SKU，检查是否存在
3. 不存在则直接插入
4. 存在则比较修改时间，决定是否更新

#### getRemoteSkuList方法
```java
/**
 * 获取远程SKU列表
 * 
 * @param accessToken 访问令牌
 * @param wareId 商品ID
 * @return SKU列表
 */
private List<TrainJdSku> getRemoteSkuList(String accessToken, Long wareId)
```

**主要逻辑：**
1. 从JdConfig获取pageSize配置
2. 检查isMockSwitch决定数据源
3. Mock开启：调用 `getSkuFromJsonService.getSkuListFromJson(wareId)`
4. Mock关闭：分页调用 `yiYiJdService.getTrainJdSkuList(accessToken, wareId, page, pageSize)`
5. 循环直到返回数据量小于pageSize

#### convertDateToLocalDateTime方法
```java
/**
 * 将LocalDateTime转换为LocalDateTime（实际上是直接返回，保持兼容性）
 */
private LocalDateTime convertDateToLocalDateTime(LocalDateTime dateTime)
```

## 核心实现逻辑

### 1. Mock机制支持
```java
boolean isMockSwitch = (jdConfig.getSync() != null && jdConfig.getSync().getIsMockSwitch() != null)
    ? jdConfig.getSync().getIsMockSwitch() : false;

if (isMockSwitch) {
    // 使用本地Mock数据
    allSkus = getSkuFromJsonService.getSkuListFromJson(wareId);
} else {
    // 使用远程API分页获取
    List<TrainJdSku> pageSkus = yiYiJdService.getTrainJdSkuList(accessToken, wareId, page, pageSize);
}
```

### 2. 智能分页获取
```java
while (true) {
    List<TrainJdSku> pageSkus = yiYiJdService.getTrainJdSkuList(accessToken, wareId, page, pageSize);
    
    if (pageSkus == null || pageSkus.isEmpty()) {
        break;
    }
    
    allSkus.addAll(pageSkus);
    
    // 如果当前页数据量小于pageSize，说明已经到最后一页
    if (pageSkus.size() < pageSize) {
        break;
    }
    
    page++;
}
```

### 3. SKU数据处理策略
```java
// 检查SKU是否已存在
TrainJdSku existingSku = trainJdSkuMapper.findBySkuId(rmtSku.getSkuId());

if (existingSku == null) {
    // 不存在，直接插入
    trainJdSkuMapper.insert(rmtSku);
} else {
    // 存在，检查是否需要更新
    LocalDateTime skuModified = convertDateToLocalDateTime(rmtSku.getModified());
    boolean isRemoteNewer = trainJdSkuMapper.isRemoteNewer(rmtSku.getSkuId(), skuModified);
    
    if (isRemoteNewer) {
        // 远程数据更新，执行更新
        trainJdSkuMapper.updateBySkuId(rmtSku);
    }
}
```

### 4. 关键代码参考
按照用户要求，参考了以下关键代码：

**时间比较逻辑：**
```java
LocalDateTime skuModified = JdProductSyncServiceImpl.convertDateToLocalDateTime(rmtSku.getModified());
boolean isRemoteNewer = trainJdSkuMapper.isRemoteNewer(rmtSku.getSkuId(), skuModified);
```

**更新操作：**
```java
trainJdSkuMapper.updateBySkuId(rmtSku);
```

## 特性

### 1. 完全复用现有逻辑
- 分页获取逻辑与商品同步完全一致
- Mock机制与商品同步完全一致
- 时间比较逻辑参考 `JdProductSyncServiceImpl.syncSkuMainTable`

### 2. 智能处理策略
- **不存在SKU**: 直接插入
- **存在SKU**: 比较修改时间，只有远程更新时才执行更新
- **异常处理**: 单个SKU处理失败不影响其他SKU

### 3. 完善的日志记录
- 记录每个商品的SKU同步开始和结果
- 记录获取到的SKU数量
- 记录插入、更新、跳过的统计信息
- 记录异常信息

### 4. 性能优化
- 批量处理SKU数据
- 智能分页避免无效请求
- 异常隔离保证整体流程稳定

## 使用示例

```java
@Autowired
private JdNewSyncService jdNewSyncService;

// 现在sync方法会自动同步商品和SKU数据
jdNewSyncService.sync("xid-001", "access-token", 1L, "admin");
```

## 测试

### 单元测试类
`JdNewSyncServiceSkuTest.java`

#### 测试用例
1. **包含SKU同步的完整同步方法** (`testSyncWithSkuSync`)
2. **SKU同步逻辑的参数验证** (`testSkuSyncParameterValidation`)
3. **Mock模式下的SKU同步** (`testSkuSyncWithMockMode`)
4. **不同团队ID的SKU同步** (`testSkuSyncWithDifferentTeamIds`)
5. **SKU同步的性能** (`testSkuSyncPerformance`)

### 测试结果
```
Tests run: 5, Failures: 0, Errors: 0, Skipped: 0
```

## 配置要求

### application.yml 配置示例
```yaml
jd:
  sync:
    page-size: 10                    # 分页大小
    is-mock-switch: false            # Mock开关，true=使用本地数据，false=使用远程API
```

## 数据库操作

### TrainJdSku 表操作
- `trainJdSkuMapper.findBySkuId(skuId)` - 根据SKU ID查找现有记录
- `trainJdSkuMapper.insert(rmtSku)` - 插入新SKU记录
- `trainJdSkuMapper.isRemoteNewer(skuId, modified)` - 比较修改时间
- `trainJdSkuMapper.updateBySkuId(rmtSku)` - 根据SKU ID更新记录

## 执行流程

1. **商品同步**: 按原有逻辑同步商品数据
2. **SKU同步**: 每个商品同步完成后，立即同步该商品的SKU数据
3. **分页获取**: 根据配置的pageSize分页获取SKU数据
4. **数据处理**: 逐个处理SKU，插入或更新
5. **统计报告**: 记录处理结果统计信息

## 注意事项

1. **依赖关系**: SKU同步依赖商品同步，确保商品数据先同步完成
2. **Mock数据**: 确保Mock模式下有对应的SKU数据文件
3. **数据库字段**: 确保TrainJdSku表包含所有必要字段
4. **性能考虑**: 大量SKU数据同步时注意内存和数据库连接使用
5. **异常处理**: 单个SKU处理失败不会中断整个同步流程

## 兼容性

- 新增功能不影响现有商品同步逻辑
- 完全向后兼容
- 可以通过配置控制是否启用SKU同步
