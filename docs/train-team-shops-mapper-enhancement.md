# TrainTeamShopsMapper 功能增强文档

## 概述

为 `TrainTeamShopsMapper` 新增了两个方法，并统一了 `shopId` 字段类型为 `Long`。

## 新增方法

### 1. findByShopId

**功能**: 根据店铺ID查询店铺信息

**方法签名**:
```java
TrainTeamShops findByShopId(@Param("shopId") Long shopId);
```

**参数**:
- `shopId`: 店铺ID (Long类型)

**返回值**:
- `TrainTeamShops`: 店铺信息（如果有多条记录，返回第一条；如果没有记录，返回null）

**使用场景**:
- 查找使用特定店铺的团队信息
- 店铺授权状态检查
- 获取店铺基本信息

**示例**:
```java
@Autowired
private TrainTeamShopsMapper trainTeamShopsMapper;

// 查询店铺ID为12345的记录
TrainTeamShops shop = trainTeamShopsMapper.findByShopId(12345L);
if (shop != null) {
    System.out.println("团队ID: " + shop.getTeamId() +
                      ", 授权状态: " + shop.getIsAuthorize() +
                      ", 同步状态: " + shop.getIsSyncComplete());
} else {
    System.out.println("未找到店铺ID为12345的记录");
}
```

### 2. updateByShopId

**功能**: 根据店铺ID更新店铺授权状态和同步状态

**方法签名**:
```java
int updateByShopId(@Param("shopId") Long shopId,
                  @Param("isAuthorize") Boolean isAuthorize,
                  @Param("isSyncComplete") Integer isSyncComplete,
                  @Param("updater") String updater);
```

**参数**:
- `shopId`: 店铺ID (Long类型)
- `isAuthorize`: 是否授权 (Boolean类型)
- `isSyncComplete`: 同步完成状态 (Integer类型: 0=未完成, 1=同步中, 2=已完成)
- `updater`: 更新人 (String类型)

**返回值**:
- `int`: 影响的行数

**使用场景**:
- 批量更新使用同一店铺的所有团队状态
- 店铺授权状态变更
- 同步状态统一更新

**示例**:
```java
// 将店铺ID为12345的所有记录设置为已授权且同步完成
int updatedRows = trainTeamShopsMapper.updateByShopId(
    12345L,           // shopId
    true,             // isAuthorize = true (已授权)
    2,                // isSyncComplete = 2 (已完成)
    "system"          // updater
);
System.out.println("更新了 " + updatedRows + " 条记录");
```

## shopId 类型统一

### 变更内容

1. **数据库表结构变更**:
   - 将 `train_team_shops` 表的 `shop_id` 字段从 `varchar(64)` 改为 `bigint`
   - 重建相关索引以适应新的数据类型

2. **代码层面统一**:
   - 确保所有相关实体类、Mapper、Service中的 `shopId` 都使用 `Long` 类型
   - 保持与其他表（如 `train_jd_products`、`train_jd_accesstoken`）的类型一致性

### 数据库迁移

执行以下SQL脚本完成数据库结构变更：

```sql
-- 删除包含shop_id的唯一索引
ALTER TABLE `train_team_shops` DROP INDEX `uk_team_shop_type`;

-- 修改shop_id字段类型为bigint
ALTER TABLE `train_team_shops` 
MODIFY COLUMN `shop_id` bigint NOT NULL DEFAULT 1 COMMENT '店铺ID';

-- 重新创建唯一索引
ALTER TABLE `train_team_shops` 
ADD UNIQUE KEY `uk_team_shop_type` (`team_id`, `shop_id`, `shop_type`);

-- 更新注释
ALTER TABLE `train_team_shops` COMMENT = '团队店铺表 - shopId已统一为Long类型';
```

## 测试

新增了完整的单元测试 `TrainTeamShopsMapperTest`，包括：

1. **testFindByShopId**: 测试根据shopId查询功能
2. **testUpdateByShopId**: 测试根据shopId更新功能
3. **testFindByShopIdNotFound**: 测试查询不存在记录的情况
4. **testUpdateByShopIdNotFound**: 测试更新不存在记录的情况

运行测试：
```bash
mvn test -Dtest=TrainTeamShopsMapperTest
```

## 注意事项

1. **数据迁移**: 在生产环境执行数据库迁移前，请先备份数据
2. **类型一致性**: 确保所有使用 `shopId` 的地方都使用 `Long` 类型
3. **业务逻辑**: `updateByShopId` 会更新所有匹配的记录，请根据业务需求谨慎使用
4. **索引影响**: 字段类型变更可能影响查询性能，建议监控相关查询的执行计划

## 兼容性

- **向后兼容**: 现有使用 `Long` 类型 `shopId` 的代码无需修改
- **数据兼容**: 数字型店铺ID可以无损转换为 `bigint` 类型
- **API兼容**: 新增方法不影响现有API接口
