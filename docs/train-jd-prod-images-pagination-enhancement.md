# TrainJdProdImages分页查询功能增强

## 概述

为TrainJdProdImagesMapper添加了根据team_id、sync_status分页查询的方法，支持动态SQL和完整的参数验证。

## 新增功能

### 1. Mapper层新增方法

#### TrainJdProdImagesMapper.java
```java
/**
 * 根据team_id、sync_status分页查询商品图片列表
 *
 * @param teamId 团队ID
 * @param syncStatus 同步状态（可为null，表示查询所有状态）
 * @param offset 偏移量
 * @param pageSize 每页大小
 * @return 商品图片列表
 */
List<TrainJdProdImages> findByTeamIdAndSyncStatusWithPagination(@Param("teamId") Long teamId,
                                                                @Param("syncStatus") Integer syncStatus,
                                                                @Param("offset") Integer offset,
                                                                @Param("pageSize") Integer pageSize);
```

#### TrainJdProdImagesMapper.xml
```xml
<!-- 根据team_id、sync_status分页查询商品图片列表 -->
<select id="findByTeamIdAndSyncStatusWithPagination" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM train_jd_prod_images
    <where>
        <if test="teamId != null">
            AND team_id = #{teamId}
        </if>
        <if test="syncStatus != null">
            AND sync_status = #{syncStatus}
        </if>
    </where>
    ORDER BY create_time DESC
    <if test="offset != null and pageSize != null">
        LIMIT #{offset}, #{pageSize}
    </if>
</select>
```

### 2. Service层新增方法

#### TrainJdProdImagesService.java
```java
/**
 * 根据team_id、sync_status分页查询商品图片列表
 *
 * @param teamId 团队ID
 * @param syncStatus 同步状态（可为null，表示查询所有状态）
 * @param offset 偏移量
 * @param pageSize 每页大小
 * @return 商品图片列表
 */
List<TrainJdProdImages> findByTeamIdAndSyncStatusWithPagination(Long teamId, Integer syncStatus, Integer offset, Integer pageSize);
```

#### TrainJdProdImagesServiceImpl.java
实现了完整的参数验证和异常处理：
- teamId不能为null
- offset不能为null或负数
- pageSize不能为null、0或负数
- 包含详细的日志记录
- 异常情况返回空列表

## 功能特性

### ✅ 动态SQL支持
- **灵活查询条件**: syncStatus为null时查询所有状态的记录
- **条件组合**: 支持teamId和syncStatus的任意组合
- **安全查询**: 使用MyBatis的`<where>`和`<if>`标签防止SQL注入

### ✅ 完整的分页功能
- **数据库层分页**: 使用LIMIT进行高效分页
- **排序优化**: 按create_time DESC排序，确保最新数据优先
- **性能优化**: 避免全表扫描，提高查询效率

### ✅ 严格的参数验证
- **必填参数检查**: teamId、offset、pageSize必须提供
- **数值范围验证**: offset >= 0, pageSize > 0
- **空值处理**: 参数无效时返回空列表而不是抛出异常

### ✅ 完善的日志记录
- **调试日志**: 记录查询参数和结果数量
- **警告日志**: 记录参数验证失败的情况
- **错误日志**: 记录异常信息便于排查问题

## 使用示例

### 基础分页查询
```java
// 查询团队1中未同步的商品图片，第1页，每页10条
List<TrainJdProdImages> images = trainJdProdImagesService.findByTeamIdAndSyncStatusWithPagination(
    1L,    // teamId
    0,     // syncStatus (0=未同步)
    0,     // offset (第1页)
    10     // pageSize
);
```

### 查询所有状态
```java
// 查询团队1中所有状态的商品图片，第2页，每页20条
List<TrainJdProdImages> allImages = trainJdProdImagesService.findByTeamIdAndSyncStatusWithPagination(
    1L,    // teamId
    null,  // syncStatus (null=所有状态)
    20,    // offset (第2页)
    20     // pageSize
);
```

### 同步状态说明
- **0**: 未完成
- **1**: 同步中
- **2**: 已完成

## 测试验证

### 新增测试用例
1. **testFindByTeamIdAndSyncStatusWithPagination**: 测试基本分页功能
   - 插入15条不同状态的测试数据
   - 验证分页查询所有状态
   - 验证分页查询特定状态
   - 验证边界情况（超出范围）

2. **testFindByTeamIdAndSyncStatusWithPaginationValidation**: 测试参数验证
   - teamId为null
   - offset为负数
   - pageSize为0或负数
   - 所有无效参数均返回空列表

### 测试结果
```
Tests run: 2, Failures: 0, Errors: 0, Skipped: 0
```
所有测试用例均通过，验证了功能的正确性和健壮性。

## 技术实现亮点

### 1. 遵循项目规范
- **命名规范**: 方法名遵循项目中其他分页查询方法的命名模式
- **参数传递**: 使用offset和pageSize参数，与项目中其他分页方法保持一致
- **返回类型**: 直接返回List，简化调用方的使用

### 2. 代码质量
- **单一职责**: 方法功能明确，只负责分页查询
- **异常安全**: 参数验证失败时优雅降级，不抛出异常
- **可维护性**: 代码结构清晰，注释完整

### 3. 性能优化
- **索引友好**: 查询条件使用了数据库索引字段
- **分页高效**: 使用数据库层LIMIT分页，避免内存分页
- **排序优化**: 按时间倒序排列，符合业务需求

## 总结

本次增强为TrainJdProdImages表提供了完整的分页查询功能，支持动态条件过滤和严格的参数验证。实现遵循了项目的编码规范和架构设计，具有良好的可维护性和扩展性。通过完整的单元测试验证了功能的正确性和健壮性。
