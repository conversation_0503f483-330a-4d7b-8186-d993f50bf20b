# AI Train Playground API测试脚本 - Windows PowerShell版本
# 使用JWT认证测试API接口

$BaseUrl = "http://localhost:8081"
$LoginEndpoint = "/api/auth/login"
$HealthEndpoint = "/actuator/health"

Write-Host "=== AI Train Playground API测试 ===" -ForegroundColor Yellow

# 1. 测试登录接口获取JWT Token
Write-Host "`n1. 测试登录接口..." -ForegroundColor Yellow

$loginBody = @{
    identity = "admin"
    password = "123456"
    rememberMe = $false
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$BaseUrl$LoginEndpoint" -Method POST -Body $loginBody -ContentType "application/json"
    
    Write-Host "登录响应: $($loginResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Green
    
    if ($loginResponse.code -eq 200) {
        Write-Host "✅ 登录成功" -ForegroundColor Green
        
        $token = $loginResponse.data.token
        
        if ($token) {
            Write-Host "✅ 成功获取JWT Token" -ForegroundColor Green
            Write-Host "Token: $($token.Substring(0, [Math]::Min(50, $token.Length)))..." -ForegroundColor Green
            
            # 2. 使用token测试健康检查端点
            Write-Host "`n2. 测试健康检查端点..." -ForegroundColor Yellow
            
            # 不带认证的健康检查
            Write-Host "测试不带认证的健康检查:"
            try {
                $healthResponse = Invoke-RestMethod -Uri "$BaseUrl$HealthEndpoint" -Method GET
                Write-Host "响应: $($healthResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Green
            } catch {
                Write-Host "响应: $($_.Exception.Message)" -ForegroundColor Red
            }
            
            # 带认证的健康检查
            Write-Host "`n测试带认证的健康检查:"
            try {
                $headers = @{
                    "Authorization" = "Bearer $token"
                }
                $healthAuthResponse = Invoke-RestMethod -Uri "$BaseUrl$HealthEndpoint" -Method GET -Headers $headers
                Write-Host "响应: $($healthAuthResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Green
            } catch {
                Write-Host "响应: $($_.Exception.Message)" -ForegroundColor Red
            }
            
            # 3. 测试其他需要认证的API端点
            Write-Host "`n3. 测试其他API端点..." -ForegroundColor Yellow
            
            # 测试WebSocket信息接口
            Write-Host "测试WebSocket信息接口:"
            try {
                $wsInfoResponse = Invoke-RestMethod -Uri "$BaseUrl/api/script-chat/ws-info" -Method GET -Headers $headers
                Write-Host "响应: $($wsInfoResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Green
            } catch {
                Write-Host "响应: $($_.Exception.Message)" -ForegroundColor Red
            }
            
            Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
            Write-Host "JWT Token已获取，可用于后续API调用" -ForegroundColor Yellow
            Write-Host "Token: Bearer $token" -ForegroundColor Yellow
            
        } else {
            Write-Host "❌ 未能从响应中提取Token" -ForegroundColor Red
        }
        
    } else {
        Write-Host "❌ 登录失败" -ForegroundColor Red
        Write-Host "请检查用户名密码是否正确，或查看应用日志" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ 登录请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 提供手动测试命令
Write-Host "`n=== 手动测试命令示例 ===" -ForegroundColor Yellow
Write-Host "如果上述自动测试成功，您可以使用以下PowerShell命令进行手动测试:" -ForegroundColor White
Write-Host ""
Write-Host "# 1. 登录获取token" -ForegroundColor Cyan
Write-Host '$body = @{identity="admin"; password="123456"; rememberMe=$false} | ConvertTo-Json' -ForegroundColor Gray
Write-Host 'Invoke-RestMethod -Uri "http://localhost:8081/api/auth/login" -Method POST -Body $body -ContentType "application/json"' -ForegroundColor Gray
Write-Host ""
Write-Host "# 2. 使用token访问API（替换YOUR_TOKEN为实际token）" -ForegroundColor Cyan
Write-Host '$headers = @{"Authorization" = "Bearer YOUR_TOKEN"}' -ForegroundColor Gray
Write-Host 'Invoke-RestMethod -Uri "http://localhost:8081/api/some-endpoint" -Method GET -Headers $headers' -ForegroundColor Gray
Write-Host ""
Write-Host "# 3. 健康检查" -ForegroundColor Cyan
Write-Host 'Invoke-RestMethod -Uri "http://localhost:8081/actuator/health" -Method GET' -ForegroundColor Gray
